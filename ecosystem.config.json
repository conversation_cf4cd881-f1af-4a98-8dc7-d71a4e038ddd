{"apps": [{"name": "vg2-dev", "script": "dist/server/server.mjs", "max_memory_restart": "250M", "max_restarts": 10, "env": {"PORT": 4000}}, {"name": "vg-test", "script": "dist/server/server.mjs", "exec_mode": "cluster", "instances": "2", "cwd": "/content/apps/vgfe/app", "max_memory_restart": "500M", "max_restarts": 10, "env": {"PORT": 30005, "HTTPS_PROXY": "http://pkg-trendency:<EMAIL>:3128"}, "out_file": "/content/logs/vgfe/out.log", "err_file": "/content/logs/vgfe/err.log", "log_type": "json", "time": true, "merge_logs": true}, {"name": "vg-prod", "script": "dist/server/server.mjs", "max_memory_restart": "500M", "cwd": "/content/apps/vgfe/app", "exec_mode": "cluster", "instances": "6", "max_restarts": 10, "env": {"PORT": 30005, "HTTPS_PROXY": "http://trendency-prd:<EMAIL>:3128"}, "out_file": "/content/logs/vgfe/out.log", "err_file": "/content/logs/vgfe/err.log", "log_type": "json", "time": true, "merge_logs": true}]}