<!doctype html>
<html lang="hu">
  <head>
    <base href="/" />
    <title>Világgazdaság</title>
    <meta charset="utf-8" />
    <meta content="index, follow, max-image-preview:large" name="robots" />
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=5.0" name="viewport" />
    <link href="/favicon.ico" rel="icon" type="image/x-icon" />
    <link href="/favicon-32x32.png" rel="icon" sizes="32x32" type="image/png" />
    <link href="/favicon-16x16.png" rel="icon" sizes="16x16" type="image/png" />
    <link href="/favicon.svg" rel="icon" type="image/svg" />
    <link href="/apple-touch-icon.png" rel="apple-touch-icon" sizes="180x180" />
    <link href="/manifest.json" rel="manifest" />
    <link color="#262626" href="/safari-pinned-tab.svg" rel="mask-icon" />
    <meta content="#262626" name="msapplication-TileColor" />
    <meta content="#262626" name="theme-color" />
    <link href="https://fonts.gstatic.com" rel="preconnect" />
    <link href="https://vg.hu/publicapi/hu/rss/vilaggazdasag/articles" rel="alternate" type="application/rss+xml" />

    <script class="structured-data" type="application/ld+json"></script>

    <script>
      if (document.head.dataset.mobileApp) {
        console.log('[mobileApp] identified - removing elements...');
        const skipMobileAppElements = Array.from(document.querySelectorAll('[data-skip-on-mobile-app]'));
        skipMobileAppElements.forEach((element) => element.remove());
      }
    </script>

    <script data-skip-on-mobile-app src="/assets/scripts/inmobi.js"></script>
    <!-- InMobi Choice. Consent Manager Tag v3.0 (for TCF 2.2) -->
    <script async data-skip-on-mobile-app type="text/javascript">
      // Make sure we call gemius init only after InMobi is loaded and inited
      console.log(' >> initializing InMobi ready callback for AdOcean');
      const inMobiReadyCallback = () => {
        console.log(' >> InMobi ready');

        if (!initAdOcean) {
          console.warn(' >> << no adocean init found');
        }
        !adOceanInited && initAdOcean && initAdOcean();
      };

      InMobiHandler.init('gq2uc_c-uMyQL', 'www.vg.hu', inMobiReadyCallback);
    </script>
    <!-- End InMobi Choice. Consent Manager Tag v3.0 (for TCF 2.2) -->

    <script data-skip-on-mobile-app>
      window.adocf = {
        useDOMContentLoaded: true,
      };

      window.PRE_adocean_queue = [];
      window.adOceanInited = false;

      window.adoQueueFn = (fn, args) => {
        console.log(' <> queuing ado call: %s', fn, args);
        PRE_adocean_queue.push({ fn, args });
      };
    </script>

    <!-- AD OCEAN GEMIUS -->
    <script data-skip-on-mobile-app src="https://hu.adocean.pl/files/js/ado.js" type="text/javascript"></script>

    <script async data-skip-on-mobile-app type="text/javascript">
      /* (c)AdOcean 2003-2020 */

      window.initAdOcean = () => {
        console.log(' >> init AdOcean');
        if (typeof window.ado !== 'object') {
          window.ado = {};
          window.ado.config = window.ado.preview = window.ado.placement = window.ado.master = window.ado.slave = function () {};
        }

        window.ado.config({
          mode: 'new',
          xml: false,
          consent: true,
          characterEncoding: true,
          attachReferrer: true,
          fpc: 'auto',
          defaultServer: 'hu.adocean.pl',
          cookieDomain: 'SLD',
        });

        window.ado.preview({ enabled: true });

        window.adOceanInited = true;
        window.PRE_adocean_queue.forEach(({ fn, args }) => {
          console.log(' >>> replaying adOcean queue: %s:', fn, args);
          window.ado[fn](...args);
        });
      };

      window.setTimeout(() => {
        if (!window.adOceanInited && window.Ado) {
          console.warn(' >> GFC initialization timeout: activating AdOcean');
          window.initAdOcean();
        } else if (!window.Ado) {
          console.error(' <!> GFC initialization timeout: AdOcean is not loaded! Aborting');
        }
      }, 30000);
    </script>

    <!-- AD OCEAN GEMIUS -->
  </head>

  <body>
    <div class="trendency-fullscreen-loader" id="init-loader"></div>

    <app-root></app-root>
    <script type="text/javascript">
      function isFacebookOrMessengerWebview(userAgent) {
        const facebookWebviewRegex = /FBAN|FBAV|FBBV|FB_IAB|FB_MESSENGER|FB4A/i;

        return facebookWebviewRegex.test(userAgent);
      }

      // This hack is needed here because of SSR problems
      // We already added a lot of ngIfs into ts and html files, this is an extra hide function for same elements because of SSR
      function isMobileAppCheck(userAgent) {
        const mobileAppRegex = /mobile\/(?<platform>.+)\/(?<device>.+)\/(?<os_version>.+)\/(?<app_version>.+)\/(?<device_language>.+)\/(?<userid>.+)/gi;
        return isFacebookOrMessengerWebview(userAgent) ? false : !!userAgent.match(mobileAppRegex);
      }

      const toRemoveOnMobileApp = [
        'ins.adsbygoogle', // Google vignette
      ];
      const isMobileApp = isMobileAppCheck(navigator.userAgent || navigator.vendor || window.opera);

      (function () {
        if (!isMobileApp && !window.location.search.match(/forceMobileApp/)) {
          return;
        }
        console.log('[isMobileApp] Removing all unnecessary elements for native mobile app...');
        toRemoveOnMobileApp.forEach((selector) => {
          Array.from(document.querySelectorAll(selector)).forEach((element) => element.remove());
        });
        const skipMobileAppElements = Array.from(document.querySelectorAll('[data-skip-on-mobile-app]'));
        skipMobileAppElements.forEach((element) => element.remove());

        const base = document.querySelector('base');
        if (base) {
          base.target = '_blank';
        }
      })();
    </script>
    <script src="/assets/scripts/init-loader.js"></script>
    <script async defer src="/assets/scripts/version.js"></script>
  </body>
</html>
