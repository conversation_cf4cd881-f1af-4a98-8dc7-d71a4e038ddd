@use 'shared' as *;

.vg-form {
  &-label {
    color: var(--kui-gray-950);
    font-weight: 700;
    font-size: 14px;
    line-height: 18px;
    margin-bottom: 8px;

    strong {
      color: var(--kui-red-500);
    }
  }

  &-input,
  &-textarea {
    background: var(--kui-white);
    border: 1px solid var(--kui-deep-teal-900);
    color: var(--kui-gray-950);
    border-radius: 2px;
    display: block;
    width: 100%;
    height: 44px;
    padding: 10px 16px;
    font-size: 16px;
    line-height: 16px;
    margin-bottom: 16px;

    &.is-invalid {
      border-color: var(--kui-red-500);
    }
  }

  &-input {
    &-password,
    &-datepicker {
      position: relative;

      &-img,
      i.icon {
        position: absolute;
        right: 12px;
        bottom: 12px;
        cursor: pointer;
        width: 20px;
        height: 20px;
      }
    }

    &.is-invalid[type='hidden'] + input {
      border-color: var(--kui-red-500);
    }
  }

  &-textarea {
    min-height: 120px;
  }

  &-general-error {
    color: var(--kui-red-500);
    font-size: 12px;
    line-height: 14px;
    font-weight: 500;
    margin-bottom: 16px;
  }

  &-checkbox,
  &-radio {
    display: flex;
    align-items: center;
    position: relative;
    cursor: pointer;
    font-size: 16px;
    line-height: 24px;
    color: var(--kui-gray-950);
    padding-left: 24px;
    min-height: 16px;
    margin-bottom: 16px;

    &-link {
      color: var(--kui-deep-teal-600);
      font-weight: 700;
      transition: color 0.3s ease;

      &:hover {
        color: var(--kui-amethyst-500);
        text-decoration: underline;
      }
    }

    input {
      display: none;
    }

    &:before {
      content: '';
      display: block;
      width: 16px;
      height: 16px;
      border: 1px solid var(--kui-gray-950);
      border-radius: 2px;
      background-color: var(--kui-white);
      position: absolute;
      top: 3px;
      left: 0;
    }

    input:checked {
      & + span:before {
        content: '';
        display: block;
        width: 16px;
        height: 16px;
        border-radius: 2px;
        background-color: var(--kui-deep-teal-600);
        border-color: var(--kui-deep-teal-600);
        position: absolute;
        top: 3px;
        left: 0;
      }

      & + span:after {
        content: '';
        display: block;
        width: 12px;
        height: 9px;
        background-image: url(/assets/images/icons/vg-checkmark-white.svg);
        background-size: 12px 9px;
        position: absolute;
        top: 6.5px;
        left: 2px;
      }
    }

    input.is-invalid {
      & + span:before {
        content: '';
        display: block;
        width: 16px;
        height: 16px;
        border-radius: 2px;
        background-color: var(--kui-red-500);
        border-color: var(--kui-red-500);
        position: absolute;
        top: 3px;
        left: 0;
      }
    }
  }

  &-radio {
    &::before {
      border-radius: 50%;
    }

    input:checked {
      & + span::before {
        border: 1px solid var(--kui-deep-teal-600);
        border-radius: 50%;
        background-color: var(--kui-white);
      }

      & + span::after {
        background-color: var(--kui-deep-teal-600);
        background-image: none;
        border-radius: 50%;
        width: 10px;
        height: 10px;
        top: 6px;
        left: 3px;
      }
    }

    input.is-invalid {
      & + span:before {
        border-radius: 50%;
      }
    }
  }

  &-select {
    margin-bottom: 16px;

    .ng-select-container {
      background-color: var(--kui-white);
      border: 1px solid var(--kui-deep-teal-900) !important;
      color: var(--kui-gray-950);
      border-radius: 2px !important;
      min-height: 44px;
      padding: 0;
      font-size: 16px;
      line-height: 22px;
      transition: none;
      box-shadow: none !important;

      .ng-value-container {
        position: relative;
        width: 100%;
        height: 100%;
        padding: 10px 12px;

        .ng-placeholder {
          color: var(--kui-gray-500);
        }

        .ng-input {
          top: 0 !important;
          padding: 0 !important;
          height: 100%;

          input {
            height: 100%;
            box-sizing: border-box !important;
            color: var(--kui-gray-950);
            font-weight: 400;
            font-size: 16px;
            line-height: 22px;
          }
        }
      }

      .ng-arrow-wrapper {
        height: 20px;
        width: 20px;
        background-image: url('/assets/images/icons/vg-arrow-down.svg');
        background-repeat: no-repeat;
        transition: transform 0.3s ease;
        margin-right: 12px;

        .ng-arrow {
          display: none;
        }
      }

      .ng-clear-wrapper {
        height: 14px;
        width: 14px;
        background-image: url('/assets/images/icons/vg-close.svg');
        background-size: 14px 14px;
        background-repeat: no-repeat;
        transition: opacity 0.3s ease;
        margin-right: 5px;

        &:hover {
          opacity: 0.9;
        }

        .ng-clear {
          display: none;
        }
      }
    }

    &.ng-select-opened .ng-select-container .ng-arrow-wrapper {
      transform: rotate(-180deg);
    }

    .ng-dropdown-panel {
      border: 1px solid var(--kui-deep-teal-900) !important;
      background: var(--kui-white);
      padding: 0;
      box-shadow: none !important;
      margin-top: -2px;
      border-top: var(--kui-white) !important;
      border-radius: 0 0 2px 2px;

      .ng-dropdown-panel-items {
        .ng-option {
          padding: 8px 12px;
          color: var(--kui-gray-900);
          font-size: 16px;
          font-weight: 500;
          line-height: 24px;
          transition: 0.3s;

          &:last-child {
            border-bottom-left-radius: 2px;
            border-bottom-right-radius: 2px;
          }

          &.ng-option-marked {
            background: var(--kui-deep-teal-500);
            color: var(--kui-white);
          }

          &.ng-option-selected {
            font-weight: 700;
            background: var(--kui-deep-teal-500);
            color: var(--kui-white);
          }
        }
      }
    }

    &:not(.ng-select-multiple) {
      .ng-select-container {
        .ng-value-container {
          .ng-input {
            input {
              padding: 10px 12px !important;
            }
          }
        }
      }
    }

    &.ng-select-multiple {
      .ng-select-container {
        .ng-value-container {
          padding: 10px 12px !important;

          .ng-placeholder {
            padding: 10px 0 !important;
            top: 0;
          }

          .ng-value {
            padding: 2px;
            background-color: var(--kui-deep-teal-500);
            color: var(--kui-white);
            font-weight: 500;
            font-size: 12px;
            line-height: 12px;
            border-radius: 2px;
            margin-bottom: 2px;
            margin-right: 2px;

            &-icon {
              padding: 0 5px;
              border-left: 1px solid var(--kui-white) !important;
              vertical-align: top;

              img {
                width: 12px;
                height: 12px;
                margin-top: -2px;
                transition: opacity 0.3s ease;
              }

              &:hover {
                background-color: transparent !important;

                img {
                  opacity: 0.9;
                }
              }
            }
          }
        }
      }
    }

    &.is-invalid .ng-select-container {
      border-color: var(--kui-red-500) !important;
    }
  }
}

.form-error {
  position: relative !important;
  top: initial !important;
  right: initial !important;
  margin: -11px 0 16px 0;
  font-size: 12px !important;
  line-height: 14px !important;
  font-weight: 500 !important;
  color: var(--kui-red-500) !important;
}

.checkbox .form-error,
.radio .form-error {
  margin: -8px 0 16px 26px;
}

::placeholder {
  color: var(--kui-gray-500);
}
