@use 'variables' as *;

%center-vertically {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

%icon {
  display: inline-block;
  min-width: 5px;
  min-height: 5px;
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
}

%img {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}

// Background image
@mixin img($file) {
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
  background-image: url($images-path + $file);
}

// IE10 IE11 only
@mixin ieonly() {
  @media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    @content;
  }
}

// Firefox only
@mixin ffonly() {
  @-moz-document url-prefix() {
    @content;
  }
}

@mixin size($width, $height: $width) {
  width: $width;
  height: $height;
}

@mixin imgRatio($x, $y) {
  display: block;
  width: 100%;
  padding-top: ($y/$x) * 100%;
  background-size: cover;
  background-position: center;
  background-color: grey;
}

@mixin icon($name, $important: false) {
  $importantFlag: '';
  @if ($important) {
    $importantFlag: '!important';
  }
  background-image: url($images-path + $name) #{$importantFlag};
}

@mixin icon_mask($name) {
  -webkit-mask-image: url($images-path + $name);
  mask-image: url($images-path + $name);
}

@mixin transition {
  transition-duration: 0.3s;
  transition-timing-function: ease-out;
  transition-property: border-color, background-size, background-color, color, transform, position, top, height, width, opacity, background-opacity, transform;
}

@mixin layoutFullWidth {
  width: 100vw;
  position: relative;
  left: calc(-50vw + 50%);
}

@mixin center-vertically {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// Display block icon
@mixin icon($name) {
  display: inline-block;
  min-width: 5px;
  min-height: 5px;
  background-position: center;
  background-size: contain;
  background-repeat: no-repeat;
  background-image: url($images-path + $name);
}

@mixin underline($color) {
  position: relative;

  &:hover {
    &:before {
      opacity: 1;
    }
  }

  &:before {
    content: '';
    position: absolute;
    left: 0;
    bottom: -1px;
    height: 1px;
    width: 100%;
    background: $color;
    opacity: 0;
    transition: 0.3s;
  }
}
