@use 'shared' as *;

.full-width {
  width: 100%;
}

.desktop {
  @include media-breakpoint-down(md) {
    display: none;
  }
}

.mobile {
  @include media-breakpoint-up(sm) {
    display: none;
  }
}

.align-items {
  &-start {
    align-items: flex-start;
  }

  &-center {
    align-items: center;
  }

  &-end {
    align-items: flex-end;
  }

  &-stretch {
    align-items: stretch;
  }
}

.justify-content {
  &-center {
    justify-content: center;
  }

  &-between {
    justify-content: space-between;
  }

  &-around {
    justify-content: space-around;
  }

  &-start {
    justify-content: flex-start;
  }

  &-end {
    justify-content: flex-end;
  }

  &-evenly {
    justify-content: space-evenly;
  }

  &-stretch {
    justify-content: stretch;
  }
}

.hover-underline {
  @include underline(var(--kui-deep-teal-400));

  &-purple {
    @include underline(var(--kui-amethyst-500));
  }
}

:root,
swiper-container {
  --swiper-theme-color: var(--kui-primary);
}
