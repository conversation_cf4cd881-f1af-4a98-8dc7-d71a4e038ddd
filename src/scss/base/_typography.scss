@use 'shared' as *;

html,
body {
  height: 100%;
}

body {
  margin: 0;
  font-family: var(--kui-font-primary);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--kui-font-condensed);
  font-weight: 700;
}

// Cikk cime
h1 {
  font-size: 48px;
  line-height: 56px;
  //tag/kereses listazo focime (maga a tag)
  &.taglist-title {
    font-size: 44px;
    line-height: 44px;
    text-transform: uppercase;
  }
}

h2 {
  // Foldali fo hir, amelynek pl fekete/kek a hattere vagy termeklistazo oldalon a termekek cime
  font-size: 40px;
  line-height: 47px;
  // tag/kereso listazoban a cikk cime
  &.tag-title {
    font-size: 32px;
    line-height: 36px;
    text-decoration: underline;
  }

  // szolgaltatasok aloldal ( orvosok, szolgaltatok a keruletben)
  &.service-title {
    font-size: 24px;
    line-height: 44px;
    text-transform: uppercase;
  }
}

h3 {
  font-size: 30px;
  line-height: 30px;
}

h4 {
  font-size: 18px;
  text-decoration: underline;
  line-height: 24px;
}

p {
  font-family: var(--kui-font-primary);
  font-size: 20px;
  line-height: 30px;
}

a {
  color: var(--kui-gray-950);
}

h1,
h2,
h3,
h4,
h5,
button,
p,
a,
span {
  font-family: var(--kui-font-primary);
}

// Figma Typos

// 36 Bold - Desktop cikk címe, H1
.font-bold-36 {
  font-size: 36px;
  line-height: 40px;
  font-weight: 700;

  @include media-breakpoint-down(md) {
    font-size: 24px;
    line-height: 32px;
    letter-spacing: 0.96px;
  }
}

// 32 Medium - Desktop idézet Elemzői blog fejléc
.font-bold-32 {
  font-size: 32px;
  font-weight: 500;
  line-height: 42px;
}

// 20 Bold - Desktop cikk lead, H2 Köztes cím, Kiemelt
.font-bold-20 {
  font-size: 20px;
  font-weight: 700;
  line-height: 26px;
  letter-spacing: 0.4px;

  @include media-breakpoint-down(md) {
    font-size: 16px;
    line-height: 22px;
  }
}

// 18 Bold - cím H3
.font-bold-18 {
  font-size: 18px;
  font-weight: 700;
  line-height: 24px;
  letter-spacing: 0.36px;

  @include media-breakpoint-down(md) {
    font-size: 16px;
    line-height: 22px;
  }
}

.font-bold-24 {
  font-size: 24px;
  font-weight: 700;
  line-height: normal;
  letter-spacing: 0.96px;
}

.font-small-14 {
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
}

// 18 Medium
.font-medium-18 {
  font-size: 18px;
  font-weight: 500;
  line-height: 24px;

  @include media-breakpoint-down(md) {
    font-size: 16px;
    line-height: 22px;
  }
}

.font-medium-16 {
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}

.font-bold-12 {
  font-size: 12px;
  font-weight: 700;
  line-height: 14px;
}

.font-bold-16 {
  font-size: 16px;
  font-weight: 700;
  line-height: 22px;
}

// 14 Regular
.font-regular-14 {
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
}
