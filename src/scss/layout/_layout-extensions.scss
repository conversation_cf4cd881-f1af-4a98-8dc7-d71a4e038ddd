@use 'shared' as *;

.wrapper.with-aside {
  display: flex;
  flex-wrap: wrap;
  margin-top: 40px;
  margin-bottom: 24px;
  gap: $aside-gap;

  @include media-breakpoint-down(md) {
    margin-top: 15px;
  }

  > .left-column {
    display: block;
    width: calc(100% - #{$aside-width} - #{$aside-gap});

    @include media-breakpoint-down(md) {
      width: 100%;
    }
  }

  > aside {
    display: block;
    width: $aside-width;
    margin-bottom: 30px;

    @include media-breakpoint-down(md) {
      width: 100%;
    }

    > * {
      margin-bottom: $block-bottom-margin;
    }
  }
}
