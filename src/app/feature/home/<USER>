import { AfterViewInit, ChangeDetectionStrategy, Component, ElementRef, inject, OnDestroy } from '@angular/core';
import { ActivatedRoute, Data } from '@angular/router';
import { SeoService, UtilService } from '@trendency/kesma-core';
import { AnalyticsService, LayoutApiData, LayoutPageType } from '@trendency/kesma-ui';
import { map, Observable } from 'rxjs';
import { defaultMetaInfo } from '../../shared';
import { AsyncPipe, DOCUMENT, NgIf } from '@angular/common';
import { LayoutComponent } from '../layout/components/layout/layout.component';

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, LayoutComponent, AsyncPipe],
})
export class HomeComponent implements AfterViewInit, OnDestroy {
  layoutApiData$: Observable<LayoutApiData> = this.route.data.pipe(
    map((data: Data | { layoutData: LayoutApiData }) => {
      this.seo.setMetaData(
        {
          ...defaultMetaInfo,
          keywords: 'hírek, információk, sporthírek, sztárok, életmód, időjárás, programajánló',
          title: 'Világgazdaság | Főoldal',
          ogTitle: 'Világgazdaság | Főoldal',
        },
        { skipSeoMetaCheck: true }
      );
      return data.layoutData;
    })
  );
  links: HTMLCollectionOf<HTMLAnchorElement>;

  readonly document = inject(DOCUMENT);

  LayoutPageType = LayoutPageType;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly utils: UtilService,
    private readonly analytics: AnalyticsService,
    private readonly elementRef: ElementRef
  ) {
    this.seo.updateCanonicalUrl('', { skipSeoMetaCheck: true });
  }

  ngAfterViewInit(): void {
    if (!this.utils.isBrowser()) return;

    queueMicrotask(() => {
      this.links = this.elementRef.nativeElement.getElementsByTagName('a');
      Array.from(this.links ?? []).forEach((element) => {
        element.addEventListener('click', this.handleAnchorClickEvent.bind(this));
        element.addEventListener('auxclick', this.handleAnchorClickEvent.bind(this));
      });
    });
  }

  ngOnDestroy(): void {
    if (!this.utils.isBrowser()) return;

    Array.from(this.links ?? []).forEach((element) => {
      element.removeEventListener('click', this.handleAnchorClickEvent.bind(this));
      element.removeEventListener('auxclick', this.handleAnchorClickEvent.bind(this));
    });
  }

  private handleAnchorClickEvent(mouseEvent: MouseEvent): void {
    const link = (mouseEvent.composedPath().find((elem: EventTarget) => (elem as Element).nodeName === 'A') as HTMLAnchorElement)?.href;
    link && this.analytics.sendMainPageClick(link, this.document.referrer);
  }
}
