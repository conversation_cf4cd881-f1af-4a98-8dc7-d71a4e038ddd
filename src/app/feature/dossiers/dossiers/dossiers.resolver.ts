import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { ApiResponseMetaList, ApiResult, DossierCard, RedirectService } from '@trendency/kesma-ui';
import { catchError, tap } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { ReqService } from '@trendency/kesma-core';

@Injectable()
export class DossiersResolver {
  constructor(
    private readonly reqService: ReqService,
    private readonly router: Router
  ) {}

  kesmaRedirectService = inject(RedirectService);

  resolve(snapshot: ActivatedRouteSnapshot): Observable<ApiResult<DossierCard[], ApiResponseMetaList>> {
    const queryParams = snapshot.queryParams as { page: string };
    const currentPage = queryParams?.page ? parseInt(queryParams.page, 10) - 1 : 0;

    return this.reqService
      .get<ApiResult<DossierCard[], ApiResponseMetaList>>(`/content-group/dossiers`, {
        params: {
          rowCount_limit: '15',
          page_limit: currentPage.toString(),
        },
      })
      .pipe(
        tap((res) => {
          if (this.kesmaRedirectService.shouldBeRedirect(currentPage, res?.data)) {
            this.kesmaRedirectService.redirectOldUrl(`dosszie`, false, 302);
          }
        }),
        catchError((error: HttpErrorResponse) => {
          this.router.navigate(['/404'], { skipLocationChange: true }).then();
          return throwError(() => error);
        })
      );
  }
}
