<section>
  <div class="wrapper with-aside">
    <div class="left-column">
      <h1 class="gallery-title font-bold-24">Aktuá<PERSON> té<PERSON></h1>
      <ng-container *ngFor="let dossier of dossiers; index as index; first as first">
        <vg-dossier-recommender [data]="dossier" [articlesCount]="dossier?.articleCount"></vg-dossier-recommender>
        <vg-newsletter-box *ngIf="first" [styleID]="NewsletterBoxType.SocialSorted"></vg-newsletter-box>
        <ng-container [ngSwitch]="index">
          <ng-container *ngIf="index === 4">
            <kesma-advertisement-adocean
              *ngIf="adverts?.desktop?.roadblock_1 as ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg-light)',
                padding: 'var(--ad-padding)',
              }"
              [ad]="ad"
            >
            </kesma-advertisement-adocean>

            <kesma-advertisement-adocean
              *ngIf="adverts?.mobile?.mobilrectangle_1 as ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg-light)',
                padding: 'var(--ad-padding)',
              }"
              [ad]="ad"
            >
            </kesma-advertisement-adocean
          ></ng-container>

          <ng-container *ngIf="index === 9">
            <kesma-advertisement-adocean
              *ngIf="adverts?.desktop?.roadblock_2 as ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg-light)',
                padding: 'var(--ad-padding)',
              }"
              [ad]="ad"
            >
            </kesma-advertisement-adocean>

            <kesma-advertisement-adocean
              *ngIf="adverts?.mobile?.mobilrectangle_2 as ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg-light)',
                padding: 'var(--ad-padding)',
              }"
              [ad]="ad"
            >
            </kesma-advertisement-adocean
          ></ng-container>
        </ng-container>
      </ng-container>

      <ng-container *ngIf="limitableMeta">
        <vg-pager
          *ngIf="limitableMeta?.pageMax && limitableMeta?.pageMax! > 0"
          [rowAllCount]="limitableMeta?.rowAllCount || 0"
          [rowOnPageCount]="limitableMeta?.rowOnPageCount || 0"
          [isListPager]="true"
          [allowAutoScrollToTop]="true"
          [showFirstPage]="true"
          [showLastPage]="true"
          [maxDisplayedPages]="3"
          [hasSkipButton]="true"
        >
        </vg-pager>
      </ng-container>

      <kesma-advertisement-adocean
        *ngIf="adverts?.desktop?.roadblock_3 as ad"
        [ad]="ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
      >
      </kesma-advertisement-adocean>

      <kesma-advertisement-adocean
        *ngIf="adverts?.mobile?.mobilrectangle_3 as ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
        [ad]="ad"
      >
      </kesma-advertisement-adocean>
    </div>
    <aside>
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>
