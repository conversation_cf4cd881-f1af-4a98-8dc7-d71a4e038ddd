import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  createCanonicalUrlForPageablePage,
  DossierCard,
  LimitableMeta,
} from '@trendency/kesma-ui';
import { map, takeUntil, tap } from 'rxjs/operators';
import {
  createVilaggazdasagTitle,
  defaultMetaInfo,
  NewsletterBoxType,
  VgDossierRecommendedData,
  VgDossierRecommenderComponent,
  VgNewsletterBoxComponent,
  VgPagerComponent,
} from '../../../shared';
import { Subject } from 'rxjs';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { NgFor, NgIf, NgSwitch } from '@angular/common';

@Component({
  selector: 'app-dossiers',
  templateUrl: './dossiers.component.html',
  styleUrls: ['./dossiers.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgFor, NgIf, NgSwitch, SidebarComponent, VgNewsletterBoxComponent, VgPagerComponent, VgDossierRecommenderComponent, AdvertisementAdoceanComponent],
})
export class DossiersComponent implements OnInit, OnDestroy {
  dossiers?: (VgDossierRecommendedData & { articleCount: number })[];
  limitableMeta?: LimitableMeta;
  adverts?: AdvertisementsByMedium;
  readonly NewsletterBoxType = NewsletterBoxType;
  readonly #unsubscribe$: Subject<boolean> = new Subject();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly cdr: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService
  ) {}

  ngOnInit(): void {
    this.route.data
      .pipe(
        tap(() => this.initAds()),
        map(({ data }) => data),
        takeUntil(this.#unsubscribe$)
      )
      .subscribe(({ data, meta }) => {
        this.dossiers = data?.map(this.#mapDossierToRecommenderDossier);
        this.limitableMeta = meta?.limitable;
        this.#setMetaData();
        this.cdr.markForCheck();
      });
  }

  resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  ngOnDestroy(): void {
    this.#unsubscribe$.next(true);
    this.#unsubscribe$.complete();
  }

  private initAds(): void {
    this.resetAds();
    this.adStore.advertisemenets$.pipe(takeUntil(this.#unsubscribe$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStore.separateAdsByMedium(ads);

      this.cdr.detectChanges();
    });
  }

  #setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('dosszie', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);

    const title = createVilaggazdasagTitle('Dossziék');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }

  #mapDossierToRecommenderDossier(dossier: DossierCard & { description: string }): VgDossierRecommendedData {
    return {
      ...dossier,
      lead: dossier?.description,
      articles: dossier?.articles?.slice(0, 3)?.map((article) => ({
        ...article,
        isAdultsOnly: (article as { isAdultOnly: boolean })?.isAdultOnly,
      })),
      headerImage: dossier?.thumbnail,
      coverImageFocusedImages: dossier?.thumbnailFocusedImages,
    } as VgDossierRecommendedData;
  }
}
