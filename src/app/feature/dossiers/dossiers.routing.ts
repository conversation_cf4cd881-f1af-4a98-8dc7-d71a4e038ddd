import { Routes } from '@angular/router';
import { DossiersComponent } from './dossiers/dossiers.component';
import { DossiersResolver } from './dossiers/dossiers.resolver';
import { DossierComponent } from './dossier/dossier.component';
import { DossierResolver } from './dossier/dossier.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const DOSSIERS_ROUTES: Routes = [
  {
    path: '',
    component: DossiersComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    providers: [DossiersResolver],
    resolve: {
      data: DossiersResolver,
    },
    canActivate: [PageValidatorGuard],
  },
  {
    path: ':slug',
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    component: DossierComponent,
    providers: [DossierResolver],
    resolve: {
      data: DossierResolver,
    },
    canActivate: [PageValidatorGuard],
  },
];
