<section>
  <div class="wrapper wrapper-top">
    <h1 class="dossier-title font-bold-24">{{ title }}</h1>
    <vg-article-card
      *ngIf="articles?.[0] as article"
      [data]="article"
      [displayLead]="true"
      [showHighlightedView]="true"
      [styleID]="ArticleCardType.FeaturedRightImgDateTagTitle"
      [showLeadInsteadOfExcerpt]="true"
    >
    </vg-article-card>

    <kesma-advertisement-adocean
      *ngIf="adverts?.desktop?.roadblock_1 as ad"
      [style]="{
        margin: 'var(--ad-margin)',
        padding: 'var(--ad-padding)',
        background: 'var(--ad-bg-light)',
      }"
      [ad]="ad"
    >
    </kesma-advertisement-adocean>

    <kesma-advertisement-adocean
      *ngIf="adverts?.mobile?.mobilrectangle_1 as ad"
      [style]="{
        margin: 'var(--ad-margin)',
        background: 'var(--ad-bg-light)',
        padding: 'var(--ad-padding)',
      }"
      [ad]="ad"
    >
    </kesma-advertisement-adocean>

    <vg-newsletter-box [styleID]="NewsletterBoxType.SocialRight"></vg-newsletter-box>
  </div>

  <div class="wrapper with-aside">
    <div class="left-column">
      <ng-container *ngFor="let article of articles; first as first; index as index">
        <vg-article-card *ngIf="!first" [data]="article" [styleID]="ArticleCardType.FeaturedRightImgDateTagTitle"></vg-article-card>
        <div class="ad-block in-left-column" *ngIf="index === 4">
          <kesma-advertisement-adocean
            *ngIf="adverts?.desktop?.roadblock_2 as ad"
            [ad]="ad"
            [style]="{
              margin: 'var(--ad-margin)',
              background: 'var(--ad-bg-light)',
              padding: 'var(--ad-padding)',
            }"
          >
          </kesma-advertisement-adocean>

          <kesma-advertisement-adocean
            *ngIf="adverts?.mobile?.mobilrectangle_2 as ad"
            [style]="{
              margin: 'var(--ad-margin)',
              background: 'var(--ad-bg-light)',
              padding: 'var(--ad-padding)',
            }"
            [ad]="ad"
          >
          </kesma-advertisement-adocean>
        </div>
      </ng-container>

      <ng-container *ngIf="limitableMeta">
        <vg-pager
          *ngIf="limitableMeta?.pageMax && limitableMeta?.pageMax! > 0"
          [rowAllCount]="limitableMeta?.rowAllCount || 0"
          [rowOnPageCount]="limitableMeta?.rowOnPageCount || 0"
          [isListPager]="true"
          [allowAutoScrollToTop]="true"
          [showFirstPage]="true"
          [showLastPage]="true"
          [maxDisplayedPages]="3"
          [hasSkipButton]="true"
        >
        </vg-pager>
      </ng-container>
      <kesma-advertisement-adocean
        *ngIf="adverts?.desktop?.roadblock_3 as ad"
        [ad]="ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
      >
      </kesma-advertisement-adocean>

      <kesma-advertisement-adocean
        *ngIf="adverts?.mobile?.mobilrectangle_3 as ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
        [ad]="ad"
      >
      </kesma-advertisement-adocean>
    </div>
    <aside>
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>
