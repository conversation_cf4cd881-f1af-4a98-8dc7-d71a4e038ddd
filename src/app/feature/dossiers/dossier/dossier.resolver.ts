import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { ApiResponseMetaList, ApiResult, DossierArticle, FakeBool, RedirectService } from '@trendency/kesma-ui';
import { ApiService } from '../../../shared';
import { catchError, map } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class DossierResolver {
  public constructor(
    private readonly apiService: ApiService,
    private readonly router: Router
  ) {}

  kesmaRedirectService = inject(RedirectService);

  public resolve(snapshot: ActivatedRouteSnapshot): Observable<
    ApiResult<
      (DossierArticle & {
        isVideoType?: FakeBool;
      })[],
      ApiResponseMetaList
    >
  > {
    const { slug } = snapshot.params;
    const { page } = snapshot.queryParams;

    const currentPage = page ? parseInt(page, 10) - 1 : 0;

    return this.apiService.getDossier(slug, currentPage).pipe(
      map((dossierData) => {
        if (this.kesmaRedirectService.shouldBeRedirect(currentPage, dossierData.data)) {
          this.kesmaRedirectService.redirectOldUrl(`dosszie/${slug}`, false, 302);
        }
        return {
          ...dossierData,
          data: dossierData.data.map(
            (
              article: DossierArticle & {
                isVideo?: FakeBool;
                isPodcastType?: FakeBool;
              }
            ) => ({
              ...article,
              isVideoType: article?.isVideo,
              isPodcastType: article.isPodcastType,
            })
          ),
        } as any;
      }),
      catchError((error: HttpErrorResponse) => {
        this.router.navigate(['/404'], { skipLocationChange: true }).then();
        return throwError(() => error);
      })
    );
  }
}
