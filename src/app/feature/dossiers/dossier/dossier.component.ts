import { ChangeDetectionStrategy, ChangeDetectorRef, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ArticleCard,
  BackendArticleSearchResult,
  createCanonicalUrlForPageablePage,
  LimitableMeta,
  mapBackendArticleDataToArticleCard,
} from '@trendency/kesma-ui';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { map, takeUntil, tap } from 'rxjs/operators';
import {
  ArticleCardType,
  createVilaggazdasagTitle,
  defaultMetaInfo,
  NewsletterBoxType,
  VgArticleCardComponent,
  VgNewsletterBoxComponent,
  VgPagerComponent,
} from '../../../shared';
import { Subject } from 'rxjs';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { NgFor, NgIf } from '@angular/common';

@Component({
  selector: 'app-dossier',
  templateUrl: './dossier.component.html',
  styleUrls: ['./dossier.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, VgNewsletterBoxComponent, VgArticleCardComponent, NgFor, SidebarComponent, VgPagerComponent, AdvertisementAdoceanComponent],
})
export class DossierComponent implements OnInit, OnDestroy {
  articles?: ArticleCard[];
  limitableMeta?: LimitableMeta;
  title?: string;
  adverts?: AdvertisementsByMedium;

  readonly ArticleCardType = ArticleCardType;
  readonly NewsletterBoxType = NewsletterBoxType;
  readonly #unsubscribe$: Subject<boolean> = new Subject();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef,
    private readonly seo: SeoService,
    private readonly adStore: AdvertisementAdoceanStoreService
  ) {}

  ngOnInit(): void {
    this.route.data
      .pipe(
        tap(() => this.initAds()),
        map(({ data }) => data),
        takeUntil(this.#unsubscribe$)
      )
      .subscribe(({ data, meta }) => {
        this.articles = data?.map(this.#mapDossierArticleToArticle);
        this.limitableMeta = meta?.limitable;
        this.title = meta?.title;
        this.#setMetaData(meta?.title, meta?.description || `A ${meta?.title} dosszié legfrissebb hírei. ${defaultMetaInfo.ogDescription}`);
        this.cdr.markForCheck();
      });
  }

  resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  ngOnDestroy(): void {
    this.#unsubscribe$.next(true);
    this.#unsubscribe$.complete();
  }

  #setMetaData(title: string, description: string): void {
    const canonical = createCanonicalUrlForPageablePage('dosszie', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);
    title = createVilaggazdasagTitle(title);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      description: description,
      ogDescription: description,
    };
    this.seo.setMetaData(metaData);
  }

  private initAds(): void {
    this.resetAds();
    this.adStore.advertisemenets$.pipe(takeUntil(this.#unsubscribe$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStore.separateAdsByMedium(ads);

      this.cdr.detectChanges();
    });
  }

  #mapDossierArticleToArticle(article: BackendArticleSearchResult): ArticleCard {
    return {
      ...mapBackendArticleDataToArticleCard(article),
      publishDate: article?.publishDate ? new Date(article.publishDate) : new Date(),
      thumbnail: {
        url: article.thumbnail as string,
      },
    };
  }
}
