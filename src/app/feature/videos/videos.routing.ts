import { Routes } from '@angular/router';
import { VideosComponent } from './components/videos/videos.component';
import { VideosResolver } from './videos.resolver';
import { VideoPageComponent } from './components/video-page/video-page.component';
import { VideoPageResolver } from './components/video-page/video-page.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const VIDEOS_ROUTES: Routes = [
  {
    path: '',
    component: VideosComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    providers: [VideosResolver],
    resolve: {
      data: VideosResolver,
    },
    canActivate: [PageValidatorGuard],
  },
  {
    path: ':slug',
    component: VideoPageComponent,
    providers: [VideoPageResolver],
    resolve: {
      data: VideoPageResolver,
    },
  },
];
