<section>
  <div class="wider-wrapper">
    <div class="narrow-wrapper">
      <h1 class="video-title">
        <kesma-icon [size]="24" name="vg-video"></kesma-icon>
        Videók
      </h1>
      <div *ngIf="videos?.length" class="video-latest-list">
        <vg-video-card *ngFor="let video of videos | slice: 0 : 3" [data]="video" [isLight]="true" [isVideoType]="isOld"></vg-video-card>
      </div>
    </div>
  </div>

  <kesma-advertisement-adocean
    *ngIf="adverts?.desktop?.roadblock_1 as ad"
    [ad]="ad"
    [style]="{
      margin: 'var(--ad-margin)',
      background: 'var(--ad-bg-light)',
      padding: 'var(--ad-padding)',
    }"
  >
  </kesma-advertisement-adocean>

  <kesma-advertisement-adocean
    *ngIf="adverts?.mobile?.mobilrectangle_1 as ad"
    [ad]="ad"
    [style]="{
      margin: 'var(--ad-margin)',
      background: 'var(--ad-bg-light)',
      padding: 'var(--ad-padding)',
    }"
  >
  </kesma-advertisement-adocean>

  <div class="wrapper">
    <vg-newsletter-box [styleID]="NewsletterBoxType.SocialRight" class="newsletter"></vg-newsletter-box>
  </div>

  <div class="wrapper with-aside">
    <div class="left-column">
      <ng-container *ngTemplateOutlet="videoBlockTemplate; context: { start: 3, end: 9, showNewsletter: true }"></ng-container>
      <ng-container *ngTemplateOutlet="videoBlockTemplate; context: { start: 9 }"></ng-container>

      <ng-container *ngIf="limitableMeta">
        <vg-pager
          *ngIf="limitableMeta?.pageMax && limitableMeta?.pageMax! > 0"
          [allowAutoScrollToTop]="true"
          [hasSkipButton]="true"
          [isListPager]="true"
          [maxDisplayedPages]="3"
          [rowAllCount]="limitableMeta?.rowAllCount || 0"
          [rowOnPageCount]="limitableMeta?.rowOnPageCount || 0"
          [showFirstPage]="true"
          [showLastPage]="true"
        >
        </vg-pager>

        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.roadblock_3 as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
            background: 'var(--ad-bg-light)',
            padding: 'var(--ad-padding)',
          }"
        >
        </kesma-advertisement-adocean>

        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.mobilrectangle_3 as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
            background: 'var(--ad-bg-light)',
            padding: 'var(--ad-padding)',
          }"
        >
        </kesma-advertisement-adocean>
      </ng-container>
    </div>
    <aside>
      <app-sidebar adPageType="all_articles_and_sub_pages"></app-sidebar>
    </aside>
  </div>
</section>

<ng-template #videoBlockTemplate let-end="end" let-showNewsletter="showNewsletter" let-start="start">
  <ng-container *ngIf="(videos ?? [] | slice: start : end)?.length">
    <div class="video-list">
      <vg-video-card *ngFor="let video of videos | slice: start : end" [data]="video" [isLight]="true" [isVideoType]="isOld"></vg-video-card>
    </div>

    <ng-container *ngIf="showNewsletter">
      <kesma-advertisement-adocean
        *ngIf="adverts?.desktop?.roadblock_2 as ad"
        [ad]="ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
      >
      </kesma-advertisement-adocean>

      <kesma-advertisement-adocean
        *ngIf="adverts?.mobile?.mobilrectangle_2 as ad"
        [ad]="ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
      >
      </kesma-advertisement-adocean>
    </ng-container>

    <vg-newsletter-box *ngIf="showNewsletter" [isListPageView]="true" [styleID]="NewsletterBoxType.Basic"></vg-newsletter-box>
  </ng-container>
</ng-template>
