@use 'shared' as *;

:host {
  display: block;
  margin-bottom: 24px;
  width: 100%;

  ::ng-deep {
    vg-pager .pager {
      margin-bottom: 0;
    }
  }

  .wider-wrapper {
    background-color: var(--kui-gray-950);
    padding: 24px 0;
  }

  .narrow-wrapper {
    width: $global-wrapper-width;
    max-width: calc(100% - 30px);
    margin: 0 auto;
  }

  .left-column {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .video {
    &-title {
      color: var(--kui-white);
      font-size: 24px;
      font-weight: 700;
      line-height: 31.75px;
      letter-spacing: 0.04em;
      display: flex;
      gap: 8px;
    }

    &-list,
    &-latest-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(#{'min(312px, 100%)'}, 1fr));
    }

    &-latest-list {
      grid-gap: 24px 40px;
      padding: 24px 0;
    }

    &-list {
      background-color: var(--kui-gray-950);
      grid-gap: 24px;
      padding: 24px;
    }
  }

  .newsletter {
    margin: 39px 0 30px 0;

    @include media-breakpoint-down(md) {
      margin: 24px 0;
    }
  }
}
