import { ChangeDetectionStrategy, ChangeDetectorRef, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  createCanonicalUrlForPageablePage,
  IconComponent,
  LimitableMeta,
  VideoCard,
} from '@trendency/kesma-ui';
import { map, Subject, takeUntil } from 'rxjs';
import { tap } from 'rxjs/operators';
import {
  createVilaggazdasagTitle,
  defaultMetaInfo,
  NewsletterBoxType,
  VgNewsletterBoxComponent,
  VgPagerComponent,
  VgVideoCardComponent,
} from '../../../../shared';
import { SidebarComponent } from '../../../layout/components/sidebar/sidebar.component';
import { <PERSON><PERSON>or, NgIf, NgTemplateOutlet, SlicePipe } from '@angular/common';

@Component({
  selector: 'app-videos',
  templateUrl: './videos.component.html',
  styleUrls: ['./videos.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    NgFor,
    NgTemplateOutlet,
    SidebarComponent,
    SlicePipe,
    VgNewsletterBoxComponent,
    VgVideoCardComponent,
    VgPagerComponent,
    IconComponent,
    AdvertisementAdoceanComponent,
  ],
})
export class VideosComponent implements OnInit, OnDestroy {
  videos?: VideoCard[];
  limitableMeta?: LimitableMeta;
  isOld = false;
  adverts?: AdvertisementsByMedium;
  readonly NewsletterBoxType = NewsletterBoxType;

  readonly #destroy$: Subject<boolean> = new Subject();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly cdr: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService
  ) {}

  ngOnInit(): void {
    const { isOld } = this.route.snapshot.data;
    this.isOld = isOld;

    this.route.data
      .pipe(
        tap(() => this.initAds()),
        map(({ data }) => data),
        takeUntil(this.#destroy$)
      )
      .subscribe(({ data, meta }) => {
        this.videos = data;
        this.limitableMeta = meta?.limitable;
        this.#setMetaData();
        this.cdr.markForCheck();
      });
  }

  ngOnDestroy(): void {
    this.#destroy$.next(true);
    this.#destroy$.complete();
  }

  private initAds(): void {
    this.resetAds();
    this.adStore.advertisemenets$.pipe(takeUntil(this.#destroy$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStore.separateAdsByMedium(ads);

      this.cdr.detectChanges();
    });
  }

  resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  #setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('videok', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);

    const title = createVilaggazdasagTitle('Videók');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'index, follow, max-image-preview:large, max-video-preview:5',
    };
    this.seo.setMetaData(metaData);
  }
}
