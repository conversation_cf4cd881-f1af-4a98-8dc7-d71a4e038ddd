import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot } from '@angular/router';
import { ApiService } from '../../../../shared';
import { Observable } from 'rxjs';
import { Video } from '@trendency/kesma-ui';
import { map } from 'rxjs/operators';

@Injectable()
export class VideoPageResolver {
  constructor(private readonly apiService: ApiService) {}

  resolve(activatedRouteSnapshot: ActivatedRouteSnapshot): Observable<Video> {
    const { slug } = activatedRouteSnapshot.params;

    return this.apiService.getVideo(slug).pipe(map(({ data }) => data?.[0]));
  }
}
