import { ChangeDetectionStrategy, Component } from '@angular/core';
import { Observable } from 'rxjs';
import { Article, ArticleVideoComponent, createCanonicalUrlForPageablePage, Video, VideoComponentObject } from '@trendency/kesma-ui';
import { ActivatedRoute } from '@angular/router';
import { map, tap } from 'rxjs/operators';
import { ArticleHeaderComponent, createVilaggazdasagTitle, defaultMetaInfo } from '../../../../shared';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { AsyncPipe, NgIf } from '@angular/common';

@Component({
  selector: 'app-video-page',
  templateUrl: 'video-page.component.html',
  styleUrls: ['video-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, ArticleHeaderComponent, AsyncPipe, ArticleVideoComponent],
})
export class VideoPageComponent {
  video$: Observable<Video> = this.activatedRoute.data.pipe(
    map(({ data }) => data),
    tap((video) => this.setMetaData(video))
  );

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService
  ) {}

  convertVideoToKesmaVideo(video: Video): VideoComponentObject {
    return {
      videaUrl: video.videaUrl,
    } as VideoComponentObject;
  }

  convertVideoToArticle(video: Video): Article {
    return {
      tags: video?.tags,
      columnSlug: video.columnSlug,
      columnTitle: video.columnTitle,
      primaryColumn: {
        id: video.columnId as string,
        title: video.columnTitle as string,
        slug: video.columnSlug as string,
        columnEmphasizeOnArticleCard: true,
      },
      title: video.title,
      excerpt: video.lead,
    } as Article;
  }

  private setMetaData(video: Video): void {
    if (!video) {
      return;
    }

    const canonical = createCanonicalUrlForPageablePage('video', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);
    const title = createVilaggazdasagTitle(video.title);
    const description = video.description || video.lead;
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title,
      ogTitle: title,
      description,
      ogDescription: description,
    };
    this.seo.setMetaData(metaData);
  }
}
