@use 'shared' as *;

:host {
  display: block;

  .wrapper {
    margin: 24px auto 48px;
  }

  kesma-article-video {
    ::ng-deep {
      .video-tn {
        padding-top: 0;
        margin-top: -2px;
        height: 555px;
      }
    }
  }

  app-article-header {
    ::ng-deep {
      .article-lead {
        font-size: 18px;
        line-height: 24px;
      }

      .article-bottom {
        justify-content: flex-start;
      }

      @include media-breakpoint-down(md) {
        app-social-share {
          &,
          .share kesma-icon {
            display: block !important;
          }
        }
      }
    }
  }

  .description {
    font-weight: 400;
    font-size: 18px;
    line-height: 24px;
    letter-spacing: 0.2px;
  }
}
