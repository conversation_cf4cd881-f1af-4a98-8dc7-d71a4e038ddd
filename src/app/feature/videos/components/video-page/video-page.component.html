<link rel="stylesheet" href="video-page.component.scss" />
<section *ngIf="video$ | async as video">
  <kesma-article-video [data]="convertVideoToKesmaVideo(video)"></kesma-article-video>

  <div class="wrapper">
    <app-article-header customShareText="Megosztom" [data]="convertVideoToArticle(video)"></app-article-header>
    <div class="description">{{ video.description }}</div>
  </div>
</section>
