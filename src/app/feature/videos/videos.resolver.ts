import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { ApiResponseMetaList, ApiResult, ArticleCard, RedirectService, searchResultToArticleCard } from '@trendency/kesma-ui';
import { Observable, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { ApiService, ListPageService, maxItemsPerPage } from '../../shared';

@Injectable()
export class VideosResolver {
  constructor(
    private readonly searchPageService: ListPageService,
    private readonly router: Router,
    private readonly apiService: ApiService
  ) {}

  kesmaRedirectService = inject(RedirectService);

  resolve(snapshot: ActivatedRouteSnapshot): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    const queryParams = snapshot.queryParams as { page: string };
    const currentPage = queryParams?.page ? parseInt(queryParams.page, 10) - 1 : 0;

    const filterParams = {
      'content_types[]': ['articleVideo'],
    };

    const articles$ = this.searchPageService.searchArticle(currentPage, maxItemsPerPage, filterParams).pipe(
      map((searchResult) => {
        return { ...searchResult, data: searchResult.data.map((searchResultData) => searchResultToArticleCard(searchResultData)) };
      })
    );

    const videos$ = this.apiService.getVideos(currentPage).pipe(
      map((videoResult) => {
        return {
          ...videoResult,
          data: videoResult.data.videos.map(
            (videoResultData) =>
              ({
                title: videoResultData.title,
                thumbnail: {
                  url: videoResultData.coverImage,
                },
                lead: videoResultData.lead,
                tags: videoResultData.tags,
                columnSlug: videoResultData.columnSlug,
                category: videoResultData.category,
                slug: videoResultData.slug,
                length: videoResultData.length,
                publishDate: videoResultData.publishDate,
                thumbnailFocusedImages: videoResultData?.coverImageFocusedImages,
              }) as ArticleCard
          ),
        };
      })
    );

    const isOld = snapshot.data?.['isOld'];

    const req$ = isOld ? videos$ : articles$;

    return req$.pipe(
      tap((res) => {
        if (this.kesmaRedirectService.shouldBeRedirect(currentPage, res?.data)) {
          this.kesmaRedirectService.redirectOldUrl(`videok`, false, 302);
        }
      }),
      catchError((error: HttpErrorResponse) => {
        this.router.navigate(['/404'], { skipLocationChange: true }).then();
        return throwError(() => error);
      })
    );
  }
}
