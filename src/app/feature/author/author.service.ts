import { Injectable } from '@angular/core';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { ApiResponseMetaList, ApiResult } from '@trendency/kesma-ui';
import { AuthorData, BackendAuthorData } from './author.definitions';

@Injectable({
  providedIn: 'root',
})
export class AuthorService {
  private readonly AUTHOR_PER_PAGE: number = 21;

  constructor(private readonly reqService: ReqService) {}

  getAuthors(pageLimit: number, rowCountLimit = this.AUTHOR_PER_PAGE, isInner = true): Observable<ApiResult<BackendAuthorData[], ApiResponseMetaList>> {
    return this.reqService.get<ApiResult<BackendAuthorData[], ApiResponseMetaList>>('user/authors', {
      params: {
        page_limit: pageLimit.toString(),
        rowCount_limit: rowCountLimit.toString(),
        is_inner: isInner ? '1' : '0',
        is_active: '1',
      },
    });
  }

  getPublicAuthorSocial(global_filter?: string, options?: IHttpOptions): Observable<ApiResult<AuthorData, ApiResponseMetaList>> {
    let params = { ...options?.params };
    params = global_filter ? { ...params, global_filter: global_filter } : params;
    return this.reqService.get(`/user/author_social`, { params });
  }
}
