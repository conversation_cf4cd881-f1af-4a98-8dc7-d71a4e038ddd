import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subject } from 'rxjs';
import { BackendAuthorData } from '../author.definitions';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { map, takeUntil } from 'rxjs/operators';
import { createCanonicalUrlForPageablePage, LimitableMeta } from '@trendency/kesma-ui';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { createVilaggazdasagTitle, defaultMetaInfo, VgPagerComponent } from '../../../shared';
import { NgFor, NgIf } from '@angular/common';

@Component({
  selector: 'app-author-list',
  templateUrl: './author-list.component.html',
  styleUrls: ['./author-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, Ng<PERSON><PERSON>, RouterLink, VgPagerComponent],
})
export class AuthorListComponent implements OnInit, OnDestroy {
  authors?: BackendAuthorData[];
  limitableMeta?: LimitableMeta;

  readonly #destroy$: Subject<boolean> = new Subject();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.route.data
      .pipe(
        map(({ data }) => data),
        takeUntil(this.#destroy$)
      )
      .subscribe(({ data, meta }) => {
        this.authors = data;
        this.limitableMeta = meta?.limitable;
        this.#setMetaData();
        this.cdr.markForCheck();
      });
  }

  ngOnDestroy(): void {
    this.#destroy$.next(true);
    this.#destroy$.complete();
  }

  #setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('szerzo', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);

    const title = createVilaggazdasagTitle('Szerzők');
    const description = 'Szerzőink - ' + defaultMetaInfo.description;
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      description,
      ogDescription: description,
    };
    this.seo.setMetaData(metaData);
  }
}
