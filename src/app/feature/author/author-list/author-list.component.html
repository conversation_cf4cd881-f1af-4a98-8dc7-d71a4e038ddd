<section>
  <div class="wrapper">
    <h1 class="author-title font-bold-24"><PERSON><PERSON><PERSON><PERSON>k</h1>
    <div *ngIf="authors?.length" class="author-list">
      <ng-container *ngFor="let author of authors">
        <a [routerLink]="['/', 'szerzo', author?.slug]" class="author-link">
          <div class="author-avatar-box">
            <img alt="" [src]="author?.avatar?.thumbnailUrl || author?.avatar?.fullSizeUrl || './assets/images/logo/logo-avatar.svg'" class="author-avatar" />
          </div>
          <div class="author-data">
            <h2 class="author-name">{{ author?.public_author_name }}</h2>
            <div *ngIf="author?.rank as rank" class="author-rank">{{ rank }}</div>
          </div>
        </a>
      </ng-container>
    </div>
    <ng-container *ngIf="limitableMeta">
      <vg-pager
        *ngIf="limitableMeta?.pageMax && limitableMeta?.pageMax! > 0"
        [allowAutoScrollToTop]="true"
        [hasSkipButton]="true"
        [isListPager]="true"
        [maxDisplayedPages]="3"
        [rowAllCount]="limitableMeta?.rowAllCount || 0"
        [rowOnPageCount]="limitableMeta?.rowOnPageCount || 0"
        [showFirstPage]="true"
        [showLastPage]="true"
      >
      </vg-pager>
    </ng-container>
  </div>
</section>
