import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { ApiResponseMetaList, ApiResult, RedirectService } from '@trendency/kesma-ui';
import { AuthorData } from '../author.definitions';
import { AuthorService } from '../author.service';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { SeoService } from '@trendency/kesma-core';

@Injectable()
export class AuthorListResolver {
  constructor(
    private readonly authorService: AuthorService,
    private readonly router: Router,
    private readonly seoService: SeoService,
    private readonly redirectService: RedirectService
  ) {}

  resolve(snapshot: ActivatedRouteSnapshot): Observable<ApiResult<AuthorData[], ApiResponseMetaList>> {
    // Redirect plural url to singular
    if (this.seoService.currentUrl.includes('szerzok')) {
      this.redirectService.redirectOldUrl(`szerzo`);
    }

    const queryParams = snapshot.queryParams as { page: string };
    const currentPage = queryParams?.page ? parseInt(queryParams.page, 10) - 1 : 0;

    return this.authorService.getAuthors(currentPage).pipe(
      tap((res) => {
        if (this.redirectService.shouldBeRedirect(currentPage, res?.data)) {
          this.redirectService.redirectOldUrl(`szerzo`, false, 302);
        }
      }),
      catchError((error: HttpErrorResponse) => {
        this.router.navigate(['/404'], { skipLocationChange: true }).then();
        return throwError(() => error);
      })
    );
  }
}
