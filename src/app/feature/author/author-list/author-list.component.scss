@use 'shared' as *;

:host {
  display: block;
  padding: 24px 0 32px 0;
  width: 100%;

  .author {
    &-title {
      color: var(--kui-deep-teal-900);
      margin-bottom: 24px;

      @include media-breakpoint-down(md) {
        font-size: 18px;
        line-height: normal;
        letter-spacing: 0.36px;
      }
    }

    &-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(#{'min(312px, 100%)'}, 1fr));
      grid-gap: 32px 80px;

      @include media-breakpoint-down(md) {
        grid-row-gap: 24px;
      }
    }

    &-link {
      display: flex;
      align-items: center;
      gap: 24px;

      &:hover {
        color: var(--kui-deep-teal-400);

        .author-avatar {
          transform: scale(1.1);

          &-box {
            outline-width: 3px;
          }
        }
      }
    }

    &-name {
      font-size: 18px;
      font-weight: 700;
      line-height: 24px;
      letter-spacing: 0.36px;
    }

    &-rank {
      font-size: 12px;
      font-weight: 400;
      line-height: 14px;
    }

    &-avatar {
      width: 56px;
      height: 56px;
      object-fit: cover;
      transition: transform 0.5s;

      &-box {
        flex-shrink: 0;
        outline: 1px solid var(--kui-deep-teal-400);
        border-radius: 50%;
        overflow: hidden;
      }
    }
  }

  vg-pager {
    margin-top: 32px;
  }
}
