import { Routes } from '@angular/router';
import { AuthorListComponent } from './author-list/author-list.component';
import { AuthorListResolver } from './author-list/author-list.resolver';
import { AuthorPageComponent } from './author-page/components/author-page/author-page.component';
import { AuthorPageResolver } from './author-page/api/author-page.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const AUTHOR_ROUTES: Routes = [
  {
    path: '',
    component: AuthorListComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    pathMatch: 'full',
    providers: [AuthorListResolver],
    resolve: {
      data: AuthorListResolver,
    },
    canActivate: [PageValidatorGuard],
  },
  {
    path: ':authorSlug',
    component: AuthorPageComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    pathMatch: 'full',
    providers: [AuthorPageResolver],
    resolve: {
      data: AuthorPageResolver,
    },
    canActivate: [PageValidatorGuard],
  },
];
