@use 'shared' as *;

:host {
  width: 100%;
  display: block;
  column-gap: 24px;

  .author {
    column-gap: 24px;
    display: flex;

    @include media-breakpoint-down(md) {
      flex-direction: column-reverse;
    }

    &-left-block {
      display: flex;
      flex-direction: column;
      row-gap: 8px;
      flex: 1;

      @include media-breakpoint-down(md) {
        margin-top: 24px;
        row-gap: 16px;
      }
    }

    &-divider {
      height: 1px;
      background: var(--kui-deep-teal-400);
      margin: 8px 0;

      @include media-breakpoint-down(md) {
        margin: 0;
      }
    }

    &-description {
      font-size: 18px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      letter-spacing: 0.36px;
    }

    &-right-block {
      display: flex;
      position: relative;
    }

    &-img {
      width: 424px;
      object-fit: cover;
      aspect-ratio: 16/9;

      @include media-breakpoint-down(md) {
        width: 100%;
      }
    }

    &-number-of-articles {
      position: absolute;
      right: 0;
      bottom: 0;
      padding: 16px 24px;
      border: 1px solid var(--kui-deep-teal-400);
      background: var(--kui-deep-teal-400-o80);
      color: var(--kui-white);
      display: flex;
      align-items: center;
    }
  }
}
