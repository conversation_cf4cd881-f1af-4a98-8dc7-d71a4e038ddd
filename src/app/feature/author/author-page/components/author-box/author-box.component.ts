import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { BaseComponent } from '@trendency/kesma-ui';
import { AuthorData } from '../../../author.definitions';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-author-box',
  templateUrl: './author-box.component.html',
  styleUrls: ['./author-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf],
})
export class AuthorBoxComponent extends BaseComponent<AuthorData> {
  @Input() numberOfArticles: number;
}
