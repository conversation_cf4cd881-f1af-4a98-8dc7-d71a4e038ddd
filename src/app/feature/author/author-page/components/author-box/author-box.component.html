<div class="author" *ngIf="data as author">
  <div class="author-left-block">
    <h1 class="font-bold-36">{{ author.publicAuthorName }}</h1>
    <div class="font-bold-24">
      {{ author.rank }}
    </div>
    <div class="author-divider"></div>
    <p class="author-description">{{ author.publicAuthorDescription }}</p>
  </div>

  <div class="author-right-block">
    <ng-container *ngIf="author.avatar.bigUrl || author.avatar.fullSizeUrl || author.avatar.thumbnailUrl">
      <img
        class="author-img"
        *ngIf="author.avatar as authorThumbnail"
        [src]="authorThumbnail.bigUrl || authorThumbnail.fullSizeUrl || authorThumbnail.thumbnailUrl"
        [alt]="author.publicAuthorName ? author.publicAuthorName + ' szerző profilképe' : '<PERSON><PERSON><PERSON><PERSON> kép'"
      />
    </ng-container>
    <div *ngIf="numberOfArticles" class="author-number-of-articles font-bold-20">{{ numberOfArticles }} cikk</div>
  </div>
</div>
