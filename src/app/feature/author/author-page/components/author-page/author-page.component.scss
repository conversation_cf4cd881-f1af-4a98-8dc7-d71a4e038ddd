@use 'shared' as *;

:host {
  app-author-box {
    margin-top: 26px;
  }

  vg-newsletter-box {
    margin-top: 27px;
  }

  .article-list {
    margin: 24px 0;
  }
}

.first-article {
  margin: 24px 0 27px 0;
  @include media-breakpoint-down(md) {
    margin: 40 0 11px 0;
  }

  vg-article-card::ng-deep {
    .article-card {
      &-thumbnail,
      &-thumbnail-card {
        height: 238px !important;
        width: 100%;
      }

      &-title {
        font-size: 20px !important;
      }

      &-tag {
        font-size: 14px !important;
        line-height: 18px;
      }

      @include media-breakpoint-up(md) {
        &-thumbnail-card {
          flex: 0 0 424px !important;
          height: 238px;
        }
      }

      @include media-breakpoint-down(sm) {
        &-data {
          flex-direction: column;
        }

        &-title {
          font-size: 18px !important;
        }

        &-row {
          gap: 16px;
        }

        &-thumbnail-card {
          flex-basis: content;
        }
      }
    }
  }
}
