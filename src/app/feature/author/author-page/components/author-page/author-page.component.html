<section *ngIf="data$ | async as data">
  <div class="wrapper narrow-wrapper">
    <app-author-box [data]="data.author" [numberOfArticles]="numberOfArticles"></app-author-box>
    <div class="first-article">
      <vg-article-card
        [data]="data.articles[0]"
        [displayLead]="true"
        [hideAuthor]="true"
        [showLeadInsteadOfExcerpt]="true"
        [styleID]="ArticleCardType.FeaturedRightImgDateTagTitle"
      ></vg-article-card>
    </div>

    <kesma-advertisement-adocean
      *ngIf="adverts?.desktop?.roadblock_1 as ad"
      [ad]="ad"
      [style]="{
        margin: 'var(--ad-margin)',
        background: 'var(--ad-bg-light)',
        padding: 'var(--ad-padding)',
      }"
    >
    </kesma-advertisement-adocean>

    <kesma-advertisement-adocean
      *ngIf="adverts?.mobile?.mobilrectangle_1 as ad"
      [ad]="ad"
      [style]="{
        margin: 'var(--ad-margin)',
        background: 'var(--ad-bg-light)',
        padding: 'var(--ad-padding)',
      }"
    >
    </kesma-advertisement-adocean>

    <vg-newsletter-box [styleID]="NewsletterBoxType.SocialRight"></vg-newsletter-box>
  </div>

  <div class="wrapper narrow-wrapper">
    <ng-container *ngIf="data.articles.length && data.articles.length > 0">
      <div *ngFor="let article of data.articles | slice: 1; let i = index" class="article-list">
        <vg-article-card [data]="article" [hideAuthor]="true" [styleID]="ArticleCardType.FeaturedRightImgDateTagTitle"></vg-article-card>
        <!-- Ads -->
        <ng-container *ngIf="i === 3">
          <kesma-advertisement-adocean
            *ngIf="adverts?.desktop?.roadblock_2 as ad"
            [ad]="ad"
            [style]="{
              margin: 'var(--ad-margin)',
              background: 'var(--ad-bg-light)',
              padding: 'var(--ad-padding)',
            }"
          >
          </kesma-advertisement-adocean>
          <kesma-advertisement-adocean
            *ngIf="adverts?.mobile?.mobilrectangle_2 as ad"
            [ad]="ad"
            [style]="{
              margin: 'var(--ad-margin)',
              background: 'var(--ad-bg-light)',
              padding: 'var(--ad-padding)',
            }"
          >
          </kesma-advertisement-adocean>
        </ng-container>
      </div>
    </ng-container>
    <ng-container *ngIf="limitables$ | async as limitable">
      <vg-pager
        *ngIf="limitable?.pageMax! > 0"
        [allowAutoScrollToTop]="true"
        [hasFirstLastButton]="false"
        [hasSkipButton]="true"
        [isListPager]="true"
        [maxDisplayedPages]="3"
        [rowAllCount]="limitable?.rowAllCount!"
        [rowOnPageCount]="limitable?.rowOnPageCount!"
      >
      </vg-pager>
    </ng-container>
  </div>

  <kesma-advertisement-adocean
    *ngIf="adverts?.desktop?.roadblock_3 as ad"
    [ad]="ad"
    [style]="{
      margin: 'var(--ad-margin)',
      background: 'var(--ad-bg-light)',
      padding: 'var(--ad-padding)',
      width: '100%',
    }"
  >
  </kesma-advertisement-adocean>
  <kesma-advertisement-adocean
    *ngIf="adverts?.mobile?.mobilrectangle_3 as ad"
    [ad]="ad"
    [style]="{
      margin: 'var(--ad-margin)',
      background: 'var(--ad-bg-light)',
      padding: 'var(--ad-padding)',
    }"
  >
  </kesma-advertisement-adocean>
</section>
