import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { catchError, forkJoin, map, Observable, share, switchMap, take, throwError } from 'rxjs';
import { IAuthorPage } from '../../author.definitions';
import { AuthorService } from '../../author.service';
import { mapSocialAuthorToAuthor } from '../../author-mapper';
import { calculateFilters, calculatePageTypeFilters, ListPageService, maxItemsPerPage } from '../../../../shared';
import { ArticleSearchResult, RedirectService, searchResultToArticleCard } from '@trendency/kesma-ui';
import { tap } from 'rxjs/operators';
import { SeoService } from '@trendency/kesma-core';

@Injectable()
export class AuthorPageResolver {
  constructor(
    private readonly searchPageService: ListPageService,
    private readonly router: Router,
    private readonly authorService: AuthorService,
    private readonly seoService: SeoService,
    private readonly redirectService: RedirectService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<IAuthorPage> {
    const authorSlug = route.paramMap.get('authorSlug');

    // Redirect plural url to singular
    if (this.seoService.currentUrl.includes('szerzok')) {
      this.redirectService.redirectOldUrl(`szerzo/${authorSlug}`);
    }

    const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;

    const publicAuthor$ = this.authorService.getPublicAuthorSocial(authorSlug as string).pipe(
      share(),
      take(1),
      map(({ data }) => mapSocialAuthorToAuthor(data))
    );

    const articlesObservable$ = publicAuthor$.pipe(
      take(1),
      switchMap((author) => {
        if (!author?.id) {
          this.router.navigate(['/404'], { skipLocationChange: true }).then();
          throwError(() => 'Nincs ilyen szerző');
        }
        this.searchPageService.filteredPublicAuthorId = author.id;
        const filters = calculatePageTypeFilters(calculateFilters(route.queryParams), route.params);
        return this.searchPageService.searchArticle(currentPage, maxItemsPerPage, filters);
      })
    );

    return forkJoin({
      articles: articlesObservable$,
      author: publicAuthor$,
    }).pipe(
      map(({ articles, author }) => ({
        articles: articles?.data?.map((sr: ArticleSearchResult) => searchResultToArticleCard(sr)),
        limitable: articles?.meta?.limitable,
        author,
      })),
      tap(({ articles }) => {
        if (this.redirectService.shouldBeRedirect(currentPage, articles)) {
          this.redirectService.redirectOldUrl(`szerzo/${authorSlug}`, false, 302);
        }
      }),
      catchError((err) => {
        this.router.navigate(['/404'], { skipLocationChange: true }).then();
        return throwError(() => err);
      })
    );
  }
}
