import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { ActivatedRoute, ActivatedRouteSnapshot } from '@angular/router';
import { LayoutElementContentConfiguration, LayoutElementRow } from '@trendency/kesma-ui';
import { LayoutComponent } from '../layout/components/layout/layout.component';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-layout-preview',
  templateUrl: './layout-preview.component.html',
  styleUrls: ['./layout-preview.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, LayoutComponent],
})
export class LayoutPreviewComponent implements OnInit {
  layoutApiData: {
    struct: LayoutElementRow[];
    content: LayoutElementContentConfiguration[];
  };
  adPageType: string;

  constructor(private readonly route: ActivatedRoute) {}

  ngOnInit(): void {
    const routeSnapshot: ActivatedRouteSnapshot = this.route.snapshot;
    this.layoutApiData = routeSnapshot.data['layoutData'];
    const { pageType, column } = routeSnapshot.queryParams;

    if (pageType === 'home' || pageType === 'homepage') {
      this.adPageType = 'main_page';
    } else if (pageType === 'column' && column) {
      this.adPageType = `column_${column}`;
    } else if (pageType === 'opinion') {
      this.adPageType = 'opinion';
    } else if (pageType === 'custombuiltpage') {
      this.adPageType = 'all_articles_and_sub_pages';
    }
  }
}
