import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { forkJoin, Observable, throwError } from 'rxjs';
import { RedirectService as KesmaRedirectService } from '@trendency/kesma-ui';
import { TagsPageService } from './tags-page.service';
import { catchError, map } from 'rxjs/operators';
import { ListPageService, maxItemsPerPage } from '../../../shared';
import { TagsPageResponse } from '../definitions/tags-page.definitions';

@Injectable()
export class TagsPageResolver {
  constructor(
    private readonly router: Router,
    private readonly tagsPageService: TagsPageService,
    private readonly searchPageService: ListPageService
  ) {}

  kesmaRedirectService = inject(KesmaRedirectService);

  resolve(activatedRouteSnapshot: ActivatedRouteSnapshot): Observable<TagsPageResponse> {
    const params = activatedRouteSnapshot.params as { tag: string; tagname?: string };
    const queryParams = activatedRouteSnapshot.queryParams as { page: string };

    const slug = params?.tag;
    const tagName = params.tagname;
    const currentPage = queryParams?.page ? parseInt(queryParams.page, 10) - 1 : 0;
    const redirectUrl = tagName ? `cimke/${slug}/${tagName}` : `cimke/${slug}`;

    if (!slug?.match(/^[a-z_0-9-]+$/gi)) {
      // When slug has accents we redirect to unaccented version
      this.router.navigate(['/', 'cimke', slug?.normalize('NFD').replace(/[\u0300-\u036f]/g, '')], { queryParams }).then();
      return throwError(() => new Error('Redirecting to unaccented tag'));
    }
    const tag$ = this.tagsPageService.getTag(slug).pipe(map(({ data }) => data));
    const articles$ = this.searchPageService.searchArticle(currentPage, maxItemsPerPage, {
      'tagSlugs[]': slug,
    });

    return forkJoin({
      tag: tag$,
      articles: articles$,
    }).pipe(
      map(({ tag, articles }) => {
        if (this.kesmaRedirectService.shouldBeRedirect(currentPage, articles?.data)) {
          this.kesmaRedirectService.redirectOldUrl(redirectUrl, false, 302);
        }
        return {
          tag,
          articles,
        };
      }),
      catchError((err) => {
        this.router.navigate(['/', '404'], { skipLocationChange: true }).then();
        return throwError(() => err);
      })
    );
  }
}
