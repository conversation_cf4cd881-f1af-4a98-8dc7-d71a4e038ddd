import { Injectable } from '@angular/core';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiResult, Tag } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class TagsPageService {
  constructor(private readonly reqService: ReqService) {}

  public getTag(slug: string): Observable<ApiResult<Tag>> {
    return this.reqService.get(`/content-group/tags/${slug}`).pipe(
      map((res: any) => {
        if (!res.data?.id) {
          throw new Error('Tag not found!');
        }
        return {
          ...res,
          data: res['data'],
        } as ApiResult<Tag>;
      })
    );
  }

  public getAllTags(options?: IHttpOptions): Observable<ApiResult<Tag[]>> {
    const params = { ...options?.params };
    return this.reqService.get('/source/content-group/tags', { params });
  }
}
