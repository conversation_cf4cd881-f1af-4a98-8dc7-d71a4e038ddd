import { Routes } from '@angular/router';
import { TagsPageComponent } from './components/tags-page/tags-page.component';
import { TagsPageResolver } from './api/tags-page.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const TAGS_PAGE_ROUTES: Routes = [
  {
    path: ':tag/:tagname',
    component: TagsPageComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    providers: [TagsPageResolver],
    resolve: { data: TagsPageResolver },
    canActivate: [PageValidatorGuard],
  },
  {
    path: ':tag',
    component: TagsPageComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    providers: [TagsPageResolver],
    resolve: { data: TagsPageResolver },
    canActivate: [PageValidatorGuard],
  },
];
