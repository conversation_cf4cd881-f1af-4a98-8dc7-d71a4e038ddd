import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ArticleCard,
  createCanonicalUrlForPageablePage,
  searchResultToArticleCard,
  Tag,
} from '@trendency/kesma-ui';
import { map, Subject, takeUntil, tap } from 'rxjs';
import {
  ArticleCardType,
  createVilaggazdasagTitle,
  defaultMetaInfo,
  NewsletterBoxType,
  VgArticleCardComponent,
  VgNewsletterBoxComponent,
  VgPagerComponent,
} from '../../../../shared';
import { SidebarComponent } from '../../../layout/components/sidebar/sidebar.component';
import { AsyncPipe, NgFor, NgIf, SlicePipe } from '@angular/common';

@Component({
  selector: 'app-tags-page',
  templateUrl: './tags-page.component.html',
  styleUrls: ['./tags-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    VgArticleCardComponent,
    NgFor,
    SidebarComponent,
    AsyncPipe,
    SlicePipe,
    VgNewsletterBoxComponent,
    VgPagerComponent,
    AdvertisementAdoceanComponent,
  ],
})
export class TagsPageComponent implements OnDestroy {
  readonly destroy$: Subject<boolean> = new Subject();
  adverts?: AdvertisementsByMedium;
  sidebarExcludedIds: string[] = [];
  ArticleCardType = ArticleCardType;
  NewsletterBoxType = NewsletterBoxType;

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly seo: SeoService
  ) {}

  tagsPageResponse$ = this.activatedRoute.data.pipe(
    map(({ data }) => ({
      ...data,
      articles: data?.articles?.data?.map(searchResultToArticleCard),
      limitable: data?.articles?.meta?.limitable,
    })),
    tap((data) => {
      this.initAds();
      this.#setMetaData(data.tag);
      this.#populateSidebarExcludedIds(data?.articles);
    })
  );

  protected initAds(): void {
    this.resetAds();
    this.adStore.advertisemenets$.pipe(takeUntil(this.destroy$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStore.separateAdsByMedium(ads);

      this.cdr.detectChanges();
    });
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  #populateSidebarExcludedIds(articles: ArticleCard[]): void {
    if (articles?.length) this.sidebarExcludedIds = articles.map((item) => item?.id) as string[];
  }

  trackByFn = (index: number): number => index;

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  #setMetaData(tag: Tag): void {
    const canonical = createCanonicalUrlForPageablePage('cimke', this.activatedRoute.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);
    const title = createVilaggazdasagTitle(`${tag?.title as string}`);
    // TODO: Edit meta tags
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'index, follow',
      description:
        `${tag?.title || ''} címke oldal aktuális tartalmai. Kapcsolódó cikkek, képgalériák,` +
        ` vélemények, egyedi videók és podcastok. Legfrissebb hírek ${tag?.title || ''} témakörben az VILÁGGAZDASÁG oldalán`,
      ogDescription:
        `${tag?.title || ''} címke oldal aktuális tartalmai. Kapcsolódó cikkek, képgalériák,` +
        ` vélemények, egyedi videók és podcastok. Legfrissebb hírek ${tag?.title || ''} témakörben az VILÁGGAZDASÁG oldalán`,
    };
    this.seo.setMetaData(metaData);
  }
}
