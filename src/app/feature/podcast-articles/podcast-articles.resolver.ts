import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { ApiResponseMetaList, ApiResult, ArticleCard, RedirectService, searchResultToArticleCard } from '@trendency/kesma-ui';
import { Observable, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { ListPageService, maxItemsPerPage } from '../../shared';

@Injectable()
export class PodcastArticlesResolver {
  constructor(
    private readonly router: Router,
    private readonly searchPageService: ListPageService
  ) {}

  kesmaRedirectService = inject(RedirectService);

  resolve(snapshot: ActivatedRouteSnapshot): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    const queryParams = snapshot.queryParams as { page: string };
    const currentPage = queryParams?.page ? parseInt(queryParams.page, 10) - 1 : 0;

    const filterParams = {
      'content_types[]': ['articlePodcast'],
    };

    const articles$ = this.searchPageService.searchArticle(currentPage, maxItemsPerPage, filterParams).pipe(
      tap((res) => {
        if (this.kesmaRedirectService.shouldBeRedirect(currentPage, res?.data)) {
          this.kesmaRedirectService.redirectOldUrl(`podcast`, false, 302);
        }
      }),
      map((searchResult) => {
        return {
          data: searchResult.data.map((searchResultData) => searchResultToArticleCard(searchResultData)),
          meta: searchResult.meta,
        };
      })
    );

    return articles$.pipe(
      catchError((error: HttpErrorResponse) => {
        this.router.navigate(['/404'], { skipLocationChange: true }).then();
        return throwError(() => error);
      })
    );
  }
}
