<ng-container *ngIf="articles$ | async as podcasts">
  <section>
    <div class="wider-wrapper">
      <div class="narrow-wrapper">
        <h1 class="podcast-articles-title">
          <span>
            <kesma-icon name="vg-podcast" [size]="24"></kesma-icon>
            Podcastok
          </span>
        </h1>
        <div class="podcast-articles-latest-list" *ngIf="podcasts?.data?.length">
          <vg-article-card
            *ngFor="let podcast of podcasts.data | slice: 0 : 3"
            [data]="$any(podcast)"
            [styleID]="ArticleCardType.Podcast"
            [isInPodcastBox]="true"
            [showHighlightedView]="true"
            [isLight]="true"
          >
          </vg-article-card>
        </div>
      </div>
    </div>

    <div class="ad-block dark full-width">
      <kesma-advertisement-adocean
        *ngIf="adverts?.desktop?.roadblock_1 as ad"
        [style]="{
          margin: 'var(--ad-margin)',
          padding: 'var(--ad-padding)',
        }"
        [ad]="ad"
      >
      </kesma-advertisement-adocean>

      <kesma-advertisement-adocean
        *ngIf="adverts?.mobile?.mobilrectangle_1 as ad"
        [style]="{
          margin: 'var(--ad-margin)',
          padding: 'var(--ad-padding)',
        }"
        [ad]="ad"
      >
      </kesma-advertisement-adocean>
    </div>

    <div class="wrapper">
      <vg-newsletter-box class="newsletter" [styleID]="NewsletterBoxType.SocialRight"></vg-newsletter-box>
    </div>

    <div class="wrapper with-aside">
      <div class="left-column">
        <ng-container *ngTemplateOutlet="podcastBlockTemplate; context: { start: 3, end: 9, showNewsletter: true }"></ng-container>

        <ng-container *ngTemplateOutlet="podcastBlockTemplate; context: { start: 9 }"></ng-container>

        <ng-container *ngIf="podcasts">
          <vg-pager
            *ngIf="podcasts?.meta?.limitable?.pageMax && podcasts?.meta?.limitable?.pageMax! > 0"
            [rowAllCount]="podcasts?.meta?.limitable?.rowAllCount || 0"
            [rowOnPageCount]="podcasts?.meta?.limitable?.rowOnPageCount || 0"
            [isListPager]="true"
            [allowAutoScrollToTop]="true"
            [showFirstPage]="true"
            [showLastPage]="true"
            [maxDisplayedPages]="3"
            [hasSkipButton]="true"
          >
          </vg-pager>

          <div class="ad-block">
            <kesma-advertisement-adocean
              *ngIf="adverts?.desktop?.roadblock_3 as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg-light)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>

            <kesma-advertisement-adocean
              *ngIf="adverts?.mobile?.mobilrectangle_3 as ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg-light)',
                padding: 'var(--ad-padding)',
              }"
              [ad]="ad"
            >
            </kesma-advertisement-adocean>
          </div>
        </ng-container>
      </div>
      <aside>
        <app-sidebar></app-sidebar>
      </aside>
    </div>
  </section>

  <ng-template #podcastBlockTemplate let-start="start" let-end="end" let-showNewsletter="showNewsletter">
    <ng-container *ngIf="(podcasts?.data | slice: start : end)?.length">
      <div class="podcast-articles-list">
        <vg-article-card
          *ngFor="let podcast of podcasts?.data | slice: start : end"
          [data]="$any(podcast)"
          [styleID]="ArticleCardType.Podcast"
          [isInPodcastBox]="true"
          [isLight]="true"
        >
        </vg-article-card>
      </div>

      <div *ngIf="showNewsletter" class="ad-block">
        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.roadblock_2 as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
            background: 'var(--ad-bg-light)',
            padding: 'var(--ad-padding)',
          }"
        >
        </kesma-advertisement-adocean>

        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.mobilrectangle_2 as ad"
          [style]="{
            margin: 'var(--ad-margin)',
            background: 'var(--ad-bg-light)',
            padding: 'var(--ad-padding)',
          }"
          [ad]="ad"
        >
        </kesma-advertisement-adocean>
      </div>

      <vg-newsletter-box *ngIf="showNewsletter" [styleID]="NewsletterBoxType.Basic" [isListPageView]="true"></vg-newsletter-box>
    </ng-container>
  </ng-template>
</ng-container>
