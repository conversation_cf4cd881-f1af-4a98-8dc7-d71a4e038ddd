@use 'shared' as *;

:host {
  display: block;
  margin-bottom: 24px;
  width: 100%;

  ::ng-deep {
    vg-pager .pager {
      margin-bottom: 0;
    }
  }

  .wider-wrapper {
    background-color: var(--kui-gray-950);
    padding: 24px 0;
  }

  .narrow-wrapper {
    width: $global-wrapper-width;
    max-width: calc(100% - 30px);
    margin: 0 auto;
  }

  .left-column {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .podcast-articles {
    &-title {
      color: var(--kui-white);
      font-size: 24px;
      font-weight: 700;
      line-height: 31.75px;
      letter-spacing: 0.04em;
      display: flex;
      align-items: center;
      gap: 16px;
      span {
        display: flex;
        align-items: center;
        gap: 8px;
      }
      &-divider {
        &::before {
          display: inline-block;
          content: '\002F';
        }
      }
      @include media-breakpoint-down(md) {
        align-items: flex-start;
        flex-direction: column;
        &-divider {
          display: none !important;
        }
      }
    }

    &-latest-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(#{'min(312px, 100%)'}, 1fr));
      grid-gap: 32px 40px;
      padding: 24px 0;
    }

    &-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(#{'min(200px, 100%)'}, 1fr));
      background-color: var(--kui-gray-950);
      grid-gap: 32px 40px;
      padding: 24px;

      @include media-breakpoint-down(md) {
        padding: 16px 24px;
        gap: 24px;
        margin: 0 -24px;
      }
    }
  }

  .newsletter {
    margin: 39px 0 30px 0;

    @include media-breakpoint-down(md) {
      margin: 24px 0;
    }
  }

  .ad-block {
    background-color: var(--kui-gray-100);
    padding: 16px 0;
    border-radius: 2px;

    &.dark {
      background-color: var(--kui-gray-800);
    }
  }

  vg-article-card {
    ::ng-deep {
      article {
        height: 100%;
      }
    }
  }
}
