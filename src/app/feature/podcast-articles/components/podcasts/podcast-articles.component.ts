import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ApiResponseMetaList,
  ApiResult,
  ArticleCard,
  createCanonicalUrlForPageablePage,
  IconComponent,
} from '@trendency/kesma-ui';
import { Subject, takeUntil, tap } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  ArticleCardType,
  createVilaggazdasagTitle,
  defaultMetaInfo,
  NewsletterBoxType,
  VgArticleCardComponent,
  VgNewsletterBoxComponent,
  VgPagerComponent,
} from '../../../../shared';
import { SidebarComponent } from '../../../layout/components/sidebar/sidebar.component';
import { AsyncPipe, NgFor, NgIf, NgTemplateOutlet, SlicePipe } from '@angular/common';

@Component({
  selector: 'app-podcast-articles',
  templateUrl: './podcast-articles.component.html',
  styleUrls: ['./podcast-articles.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    NgFor,
    VgArticleCardComponent,
    NgTemplateOutlet,
    SidebarComponent,
    AsyncPipe,
    SlicePipe,
    VgNewsletterBoxComponent,
    VgPagerComponent,
    IconComponent,
    AdvertisementAdoceanComponent,
  ],
})
export class PodcastArticlesComponent implements OnDestroy {
  readonly destroy$: Subject<boolean> = new Subject();
  adverts?: AdvertisementsByMedium;

  sidebarExcludedIds: string[] = [];
  ArticleCardType = ArticleCardType;
  NewsletterBoxType = NewsletterBoxType;
  articles$ = this.route.data.pipe(
    map(({ data }) => ({ data: data.data, meta: data.meta }) as ApiResult<ArticleCard[], ApiResponseMetaList>),
    tap(({ data }) => {
      this.initAds();
      this.#setMetaData();
      this.populateSidebarExcludedIds(data);
    })
  );

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly cdr: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService
  ) {}

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  trackById(_: number, item: ArticleCard): string | undefined {
    return item.id;
  }

  populateSidebarExcludedIds(articles: ArticleCard[]): void {
    if (articles?.length) this.sidebarExcludedIds = articles.map((item) => item?.id) as string[];
  }

  protected initAds(): void {
    this.resetAds();
    this.adStore.advertisemenets$.pipe(takeUntil(this.destroy$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStore.separateAdsByMedium(ads);

      this.cdr.detectChanges();
    });
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  #setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('podcast', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);

    const title = createVilaggazdasagTitle('Podcastok');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
