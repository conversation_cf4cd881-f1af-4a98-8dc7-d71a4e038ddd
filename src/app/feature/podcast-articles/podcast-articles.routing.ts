import { Routes } from '@angular/router';
import { PodcastArticlesComponent } from './components/podcasts/podcast-articles.component';
import { PodcastArticlesResolver } from './podcast-articles.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const PODCASTS_ROUTES: Routes = [
  {
    path: '',
    component: PodcastArticlesComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    providers: [PodcastArticlesResolver],
    resolve: {
      data: PodcastArticlesResolver,
    },
    canActivate: [PageValidatorGuard],
  },
];
