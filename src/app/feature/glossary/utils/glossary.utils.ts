import { Glossary } from '../definitions/glossary.definitions';
import { backendDateToDate } from '@trendency/kesma-core';

export const mapBackendGlossaryToGlossary = ({ id, title, slug, description, startMatch, endMatch, updatedAt }: Glossary<string>): Glossary =>
  id || title
    ? {
        id,
        title,
        slug,
        description,
        startMatch,
        endMatch,
        updatedAt: backendDateToDate(updatedAt as string),
      }
    : ({} as Glossary);

export const hungarianAlphabet = [
  'a',
  'á',
  'b',
  'c',
  'cs',
  'd',
  'e',
  'é',
  'f',
  'g',
  'gy',
  'h',
  'i',
  'í',
  'j',
  'k',
  'l',
  'm',
  'n',
  'o',
  'ó',
  'ö',
  'ő',
  'p',
  'r',
  's',
  'sz',
  't',
  'ty',
  'u',
  'ú',
  'ü',
  'ű',
  'v',
  'z',
  'zs',
];

export const glossaryByLetter = (glossaryList: Glossary[], letter: string): Glossary[] => {
  // Get the two-digit letters from the Hungarian alphabet
  const twoDigitLetters = hungarianAlphabet.filter((alphabet) => alphabet.length === 2);

  // If the provided letter is a single character
  if (letter.length === 1) {
    // Filter out glossary items that start with a two-digit letter
    // to avoid duplicates at one-digit letter that starts with two-digit letter's first char (like z - zs)
    glossaryList = glossaryList.filter((glossary) => !twoDigitLetters.includes(glossary.title!.slice(0, 2).toLowerCase()));
  }

  // Return the glossary items that start with the provided letter (case insensitive)
  return glossaryList.filter(({ title }) => title?.slice(0, letter.length).toLowerCase() === letter) ?? [];
};

export const sortGlossary = (glossaryList: Glossary[]): Glossary[] =>
  (glossaryList ?? []).sort((a: Glossary, b: Glossary): number => {
    if (!a.startMatch?.length || !b.startMatch?.length || !a.endMatch?.length || !b.endMatch?.length) return 0;

    if (a.startMatch.length === b.startMatch.length) {
      return b.endMatch.length - a.endMatch.length;
    }
    return b.startMatch.length - a.startMatch.length;
  });
