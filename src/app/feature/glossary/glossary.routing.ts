import { Routes } from '@angular/router';
import { GlossaryListComponent } from './components/glossary-list/glossary-list.component';
import { GlossaryPageComponent } from './components/glossary-page/glossary-page.component';
import { GlossaryResolver } from './api/glossary.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const GLOSSARY_ROUTES: Routes = [
  {
    path: '',
    component: GlossaryListComponent,
    canActivate: [PageValidatorGuard],
    pathMatch: 'full',
  },
  {
    path: ':slug',
    component: GlossaryPageComponent,
    resolve: { data: GlossaryResolver },
    providers: [GlossaryResolver],
    canActivate: [PageValidatorGuard],
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
  },
];
