import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { ApiService } from '../../../shared';
import { forkJoin, Observable, of, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ApiResponseMetaList, Tag } from '@trendency/kesma-ui';
import { IGlossaryResolverData } from '../definitions/glossary.definitions';
import { TagsPageService } from '../../tags-page/api/tags-page.service';
import { GlossaryService } from './glossary.service';

const GLOSSARY_RELATED_ARTICLES_COUNT = 10;

@Injectable()
export class GlossaryResolver {
  constructor(
    private readonly glossaryService: GlossaryService,
    private readonly apiService: ApiService,
    private readonly tagsService: TagsPageService,
    private readonly router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<IGlossaryResolverData> {
    const pageIndex = (route.queryParams['page'] ?? 1) - 1;
    return forkJoin({
      glossary: this.glossaryService.getGlossary(route.params['slug']),
      articles: this.apiService.getTagsList(route.params['slug'], pageIndex, GLOSSARY_RELATED_ARTICLES_COUNT).pipe(
        catchError(() => {
          return of({ data: [], meta: {} as ApiResponseMetaList });
        })
      ),
      tag: this.tagsService.getTag(route.params['slug']).pipe(
        map(({ data }) => data),
        catchError(() => {
          return of({} as Tag);
        })
      ),
    }).pipe(
      map(({ tag, glossary, articles: { data: articles, meta: listMeta } }) => ({
        tag,
        glossary,
        articles,
        listMeta,
      })),
      catchError((err) => {
        this.router.navigate(['/', '404'], { skipLocationChange: true }).then();
        return throwError(err);
      })
    );
  }
}
