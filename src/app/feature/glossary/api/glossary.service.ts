import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ReqService } from '@trendency/kesma-core';
import { Glossary } from '../definitions/glossary.definitions';
import { ApiResponseMetaList, ApiResult } from '@trendency/kesma-ui';
import { mapBackendGlossaryToGlossary } from '../utils/glossary.utils';

@Injectable({
  providedIn: 'root',
})
export class GlossaryService {
  constructor(private readonly reqService: ReqService) {}

  getGlossaryList(): Observable<Glossary[]> {
    return this.reqService
      .get<ApiResult<Glossary<string>[], ApiResponseMetaList>>('content-page/glossary/list', {
        params: {
          rowCount_limit: '999',
        },
      })
      .pipe(map(({ data }) => data?.map(mapBackendGlossaryToGlossary)));
  }

  getGlossary(slug: string): Observable<Glossary> {
    return this.reqService.get<ApiResult<Glossary<string>>>(`content-page/glossary/${slug}`).pipe(
      map(({ data }) => ({
        ...mapBackendGlossaryToGlossary(data),
      }))
    );
  }
}
