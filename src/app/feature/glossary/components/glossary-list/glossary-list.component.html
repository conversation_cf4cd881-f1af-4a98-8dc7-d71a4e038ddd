<section [offset]="headerHeight + 70" scrollSpy>
  <div class="wrapper">
    <div class="glossary">
      <app-glossary-header></app-glossary-header>
      <div class="glossary-shortcuts-header"><PERSON><PERSON><PERSON>tár ABC</div>

      <div [style.top.px]="headerHeight" class="glossary-shortcuts">
        <a
          (click)="scrollToLetter(glossary?.letter ?? '', $event)"
          *ngFor="let glossary of glossaryList"
          [attr.data-scrollSpyNavItem]="glossary?.letter.toLocaleLowerCase()"
          [class.disabled]="!glossary?.enabled"
          [ngClass]="{ active: activeLetter === glossary?.letter }"
          class="glossary-shortcuts-letter"
        >
          {{ glossary?.letter.toLocaleUpperCase() }}
        </a>
      </div>

      <div class="glossary-sections-wrapper">
        <h1 class="glossary-title">Szószedet</h1>

        <ng-container *ngFor="let glossary of glossaryList">
          <ng-container *ngIf="glossary?.enabled">
            <div class="glossary-section">
              <div class="glossary-section-letter-wrapper">
                <a [id]="'letter-' + glossary?.letter.toLocaleLowerCase()"></a>
                <h2 #indexLetter [attr.data-letter]="glossary?.letter.toLocaleLowerCase()" class="glossary-section-letter">
                  {{ glossary?.letter.toLocaleUpperCase() }}
                </h2>
              </div>

              <div class="glossary-section-letter-list-wrapper">
                <ng-container *ngFor="let element of glossary?.elements">
                  <a
                    *ngIf="element"
                    [attr.data-scrollSpySectionItem]="glossary?.letter.toLocaleLowerCase()"
                    [routerLink]="['/', 'szoszedet', element.slug]"
                    class="glossary-section-letter-list-item"
                  >
                    {{ element.title }}
                    <kesma-icon name="vg-chevron-diagonal-right"></kesma-icon>
                  </a>
                </ng-container>
              </div>
            </div>
          </ng-container>
        </ng-container>
      </div>
    </div>
  </div>
</section>
