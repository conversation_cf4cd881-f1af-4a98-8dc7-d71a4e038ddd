import { ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, Inject, OnDestroy, OnInit, ViewChildren } from '@angular/core';
import { Subject } from 'rxjs';
import { ICalculatedGlossary } from '../../definitions/glossary.definitions';
import { GlossaryService } from '../../api/glossary.service';
import { glossaryByLetter, hungarianAlphabet } from '../../utils/glossary.utils';
import { takeUntil } from 'rxjs/operators';
import { DOCUMENT, NgClass, NgFor, NgIf, ViewportScroller } from '@angular/common';
import { HeaderService } from '../../../../shared';
import { SeoService, UtilService } from '@trendency/kesma-core';
import { RouterLink } from '@angular/router';
import { GlossaryHeaderComponent } from '../glossary-header/glossary-header.component';
import { createCanonicalUrlForPageablePage, IconComponent, ScrollSpyDirective } from '@trendency/kesma-ui';

@Component({
  selector: 'app-glossary-list',
  templateUrl: './glossary-list.component.html',
  styleUrls: ['./glossary-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [GlossaryHeaderComponent, NgFor, NgClass, NgIf, RouterLink, ScrollSpyDirective, IconComponent],
})
export class GlossaryListComponent implements OnInit, OnDestroy {
  @ViewChildren('indexLetter', { read: ElementRef }) indexLetters: ElementRef<HTMLHeadingElement>[];
  glossaryList: ICalculatedGlossary[] = [];
  activeLetter: string;
  headerOffset: number;
  headerHeight: number;
  readonly #destroy$ = new Subject<boolean>();

  constructor(
    private readonly glossaryService: GlossaryService,
    private readonly cdr: ChangeDetectorRef,
    private readonly viewportScroller: ViewportScroller,
    private readonly headerService: HeaderService,
    private readonly utilService: UtilService,
    private readonly seoService: SeoService,
    @Inject(DOCUMENT) private readonly document: Document
  ) {}

  ngOnInit(): void {
    this.getGlossaryList();
    this.getHeaderOffset();
    this.getHeaderHeight();
    this.createCanonicalUrl();
  }

  ngOnDestroy(): void {
    this.#destroy$.next(true);
    this.#destroy$.complete();
  }

  getGlossaryList(): void {
    this.glossaryService
      .getGlossaryList()
      .pipe(takeUntil(this.#destroy$))
      .subscribe((glossaryList) => {
        this.glossaryList = hungarianAlphabet.map((letter) => ({
          letter,
          enabled: glossaryByLetter(glossaryList, letter).length > 0,
          elements: glossaryByLetter(glossaryList, letter),
        }));
        this.activeLetter = this.glossaryList.find((glossary) => glossary?.enabled)?.letter || '';
        this.cdr.markForCheck();
      });
  }

  createCanonicalUrl(): void {
    const canonical = createCanonicalUrlForPageablePage('szoszedet');
    canonical && this.seoService.updateCanonicalUrl(canonical);
  }

  getHeaderOffset(): void {
    this.headerService.headerTopOffset$.pipe(takeUntil(this.#destroy$)).subscribe((top) => {
      this.headerOffset = top;
      this.cdr.detectChanges();
    });
  }

  getHeaderHeight(): void {
    this.headerService.headerHeight$.pipe(takeUntil(this.#destroy$)).subscribe((height) => {
      this.headerHeight = height;
      this.cdr.detectChanges();
    });
  }

  scrollToLetter(letter: string, event: MouseEvent): void {
    event.preventDefault();

    this.indexLetters.forEach(({ nativeElement }) => {
      if (letter === nativeElement.dataset['letter']) {
        const htmlElement = this.document.querySelector('html');
        if (htmlElement) {
          htmlElement.style.scrollBehavior = 'smooth';
        }
        this.activeLetter = letter;
        this.viewportScroller.setOffset([0, this.headerHeight + 130]);
        this.viewportScroller.scrollToAnchor('letter-' + letter);

        // Should call again scrollToAnchor function because at the very first time the header is not "sticky" yet
        // When the header is becoming "sticky", the scrollToAnchor function will not work
        if (this.utilService.isBrowser()) {
          setTimeout(() => {
            this.viewportScroller.scrollToAnchor('letter-' + letter);
            htmlElement?.style.removeProperty('scroll-behavior');
          }, 200);
        }
      }
    });
  }
}
