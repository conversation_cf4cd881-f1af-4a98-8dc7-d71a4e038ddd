@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  margin-top: 35px;
  margin-bottom: 65px;

  @include media-breakpoint-down(md) {
    margin-bottom: 24px;
  }

  .glossary {
    max-width: 872px;
    margin: 0 auto;

    &-shortcuts {
      display: grid;
      grid-template-columns: repeat(18, 1fr);
      row-gap: 24px;
      column-gap: 17px;
      position: sticky;
      z-index: 1;
      //top: 74px; calculated in JS
      background: var(--kui-white);
      padding: 20px 0;
      overflow-x: auto;

      @include media-breakpoint-down(md) {
        padding: 16px 0;
      }

      &-header {
        color: var(--kui-deep-teal-900);
        font-size: 18px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        letter-spacing: 0.36px;
        display: none;

        @include media-breakpoint-down(sm) {
          display: block;
        }
      }

      &-letter {
        @extend %letter;
        transition: 0.3s;
        cursor: pointer;

        &.disabled {
          border-color: var(--kui-gray-200);
          color: var(--kui-gray-200);
          pointer-events: none;
        }

        &:hover {
          background: var(--kui-deep-teal-400);
          color: var(--kui-white);
        }

        &.active {
          background: var(--kui-deep-teal-600);
          border-color: var(--kui-deep-teal-600);
          color: var(--kui-white);
        }
      }
    }

    &-title {
      margin-top: 42px;
      color: var(--kui-deep-teal-900);
      font-size: 24px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      letter-spacing: 0.96px;

      @include media-breakpoint-down(sm) {
        margin-top: 24px;
        font-size: 18px;
        letter-spacing: 0.36px;
      }
    }

    &-sections-wrapper {
      display: flex;
      flex-direction: column;
      gap: 24px;
    }

    &-section {
      display: flex;
      flex-direction: column;
      gap: 16px;

      &-letter {
        @extend %letter;
        margin: 4px 0 8px;
        color: var(--kui-gray-950);
        position: relative;
        pointer-events: none;

        &-wrapper {
          border-bottom: 1px solid var(--kui-deep-teal-400);
        }

        &-list {
          &-wrapper {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 24px;

            @include media-breakpoint-down(md) {
              grid-template-columns: 1fr;
            }
          }

          &-item {
            font-weight: 500;
            line-height: 24px;
            padding: 8px 0;
            border-bottom: 1px solid var(--kui-deep-teal-100);
            transition: 0.3s;
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 20px;

            kesma-icon {
              flex: 0 0 auto;
              opacity: 0;
              color: var(--kui-amethyst-500);
              transition: 0.3s;
            }

            &:hover {
              color: var(--kui-amethyst-500);
              border-color: var(--kui-amethyst-500);

              kesma-icon {
                opacity: 1;
              }
            }
          }
        }
      }

      @include media-breakpoint-down(md) {
        gap: 8px;
      }
    }
  }
}

%letter {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  border: 1px solid var(--kui-deep-teal-100);
  width: 32px;
  height: 32px;
  font-size: 20px;
  font-weight: 700;
  line-height: 26px;
  letter-spacing: 0.4px;
}

// SCROLLBAR
/* width */
::-webkit-scrollbar {
  height: 4px;
}

/* Track */
::-webkit-scrollbar-track {
  background: var(--kui-deep-teal-100);
  border-radius: 100px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: var(--kui-deep-teal-700);
  border-radius: 100px;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: var(--kui-gray-200);
}
