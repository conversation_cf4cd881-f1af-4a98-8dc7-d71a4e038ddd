import { ChangeDetectionStrategy, Component } from '@angular/core';
import { RouterLink } from '@angular/router';
import { IconComponent } from '@trendency/kesma-ui';

@Component({
  selector: 'app-glossary-header',
  templateUrl: './glossary-header.component.html',
  styleUrls: ['./glossary-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, IconComponent],
})
export class GlossaryHeaderComponent {}
