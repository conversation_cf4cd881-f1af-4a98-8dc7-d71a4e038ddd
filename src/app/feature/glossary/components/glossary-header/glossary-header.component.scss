@use 'shared' as *;

.glossary-header {
  display: flex;
  gap: 24px;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--kui-deep-teal-400);
  padding: 8px 12px;
  margin-bottom: 20px;
  cursor: pointer;

  @include media-breakpoint-down(sm) {
    flex-direction: column;
    gap: 7px;
    margin-bottom: 24px;
  }

  img {
    margin-bottom: 6px;
  }

  kesma-icon {
    color: var(--kui-gray-200);
    flex: 0 0 auto;

    @include media-breakpoint-down(sm) {
      display: none;
    }
  }

  .title {
    color: var(--kui-gray-950);
    font-size: 32px;
    font-weight: 300;
    line-height: normal;
    letter-spacing: 1.6px;

    @include media-breakpoint-down(sm) {
      font-size: 16px;
      text-transform: uppercase;
      text-align: center;
    }
  }
}
