import { ChangeDetectionStrategy, Component, OnDestroy } from '@angular/core';
import { Subject } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { map, tap } from 'rxjs/operators';
import { ArticleCard, createCanonicalUrlForPageablePage, IconComponent, PAGE_TYPES } from '@trendency/kesma-ui';
import { IGlossaryResolverData } from '../../definitions/glossary.definitions';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  ArticleCardType,
  createVilaggazdasagTitle,
  defaultMetaInfo,
  NewsletterBoxType,
  VgArticleCardComponent,
  VgNewsletterBoxComponent,
  VgPagerComponent,
  VgWysiwygBoxComponent,
} from '../../../../shared';
import { SidebarComponent } from '../../../layout/components/sidebar/sidebar.component';
import { GlossaryHeaderComponent } from '../glossary-header/glossary-header.component';
import { Async<PERSON>ipe, <PERSON>For, NgIf, SlicePipe } from '@angular/common';

@Component({
  selector: 'app-glossary-page',
  templateUrl: './glossary-page.component.html',
  styleUrls: ['./glossary-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    GlossaryHeaderComponent,
    NgFor,
    SidebarComponent,
    AsyncPipe,
    SlicePipe,
    VgArticleCardComponent,
    VgNewsletterBoxComponent,
    VgPagerComponent,
    VgWysiwygBoxComponent,
    IconComponent,
  ],
})
export class GlossaryPageComponent implements OnDestroy {
  readonly destroy$: Subject<boolean> = new Subject();
  adPageType = PAGE_TYPES.all_articles_and_sub_pages;
  sidebarExcludedIds: string[] = [];
  ArticleCardType = ArticleCardType;
  NewsletterBoxType = NewsletterBoxType;
  rowAllCount = 0;
  rowOnPageCount = 0;
  rowFrom = 0;

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly seo: SeoService
  ) {}

  glossaryPageResponse$ = this.activatedRoute.data.pipe(
    map(({ data }): IGlossaryResolverData => data),
    tap((data) => {
      const limitable = data?.listMeta?.limitable;
      this.rowAllCount = limitable?.rowAllCount ?? 0;
      this.rowOnPageCount = limitable?.rowOnPageCount ?? 0;
      this.rowFrom = limitable?.rowFrom ?? 0;
      this.#setMetaData(data?.glossary?.title ?? '');
      this.#populateSidebarExcludedIds(data?.articles);
    })
  );

  #populateSidebarExcludedIds(articles: ArticleCard[]): void {
    if (articles?.length) this.sidebarExcludedIds = articles.map((item) => item?.id) as string[];
  }

  trackByFn = (index: number): number => index;

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  #setMetaData(glossaryTitle: string): void {
    const canonical = createCanonicalUrlForPageablePage('szoszedet', this.activatedRoute.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);
    const title = createVilaggazdasagTitle(glossaryTitle);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'index, follow',
      description:
        `${glossaryTitle} címke oldal aktuális tartalmai. Kapcsolódó cikkek, képgalériák,` +
        ` vélemények, egyedi videók és podcastok. Legfrissebb hírek ${glossaryTitle} témakörben az VILÁGGAZDASÁG oldalán`,
      ogDescription:
        `${glossaryTitle} címke oldal aktuális tartalmai. Kapcsolódó cikkek, képgalériák,` +
        ` vélemények, egyedi videók és podcastok. Legfrissebb hírek ${glossaryTitle} témakörben az VILÁGGAZDASÁG oldalán`,
    };
    this.seo.setMetaData(metaData);
  }
}
