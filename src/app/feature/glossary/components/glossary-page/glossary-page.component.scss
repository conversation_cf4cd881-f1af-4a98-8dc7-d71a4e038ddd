@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  margin-top: 35px;
  margin-bottom: 65px;

  @include media-breakpoint-down(md) {
    margin-bottom: 24px;
  }

  .glossary {
    &-title {
      display: flex;
      gap: 24px;
      margin: 36px 0;

      @include media-breakpoint-down(md) {
        margin-top: 28px;
        margin-bottom: 20px;
      }

      kesma-icon {
        color: var(--kui-deep-teal-400);
        width: 24px;
      }

      &-text {
        color: var(--kui-deep-teal-900);
        font-size: 24px;
        font-weight: 700;
        line-height: normal;
        letter-spacing: 0.96px;

        @include media-breakpoint-down(md) {
          font-size: 18px;
        }
      }
    }

    &-description {
      vg-wysiwyg-box ::ng-deep {
        &.block-content {
          p > span,
          strong,
          i {
            color: var(--kui-gray-950);
            font-size: 20px;
            font-weight: 500;
            line-height: normal;
            letter-spacing: 0.2px;

            @include media-breakpoint-down(md) {
              line-height: 24px;
              font-size: 18px;
            }
          }

          strong {
            font-weight: 700;
          }
        }
      }
    }

    &-articles {
      color: var(--kui-deep-teal-900);
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
      margin: 32px 0;

      @include media-breakpoint-down(md) {
        margin: 24px 0;
      }
    }

    .first-article ::ng-deep {
      .article-card {
        &-lead {
          font-size: 18px;

          @include media-breakpoint-down(md) {
            font-size: 16px;
          }
        }
        &-tag {
          @include media-breakpoint-down(md) {
            font-size: 14px;
            line-height: 18px;
          }
        }
      }
    }
  }
}

vg-newsletter-box,
vg-pager {
  margin: 32px 0;

  @include media-breakpoint-down(md) {
    margin: 24px 0;
  }
}

.left-column {
  .article-list {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .glossary-article-card {
    padding-bottom: 16px;
    border-bottom: 1px solid var(--kui-deep-teal-200);

    @include media-breakpoint-down(md) {
      padding-bottom: 8px;
    }
  }
}
