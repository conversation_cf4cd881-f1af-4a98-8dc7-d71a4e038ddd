<ng-container *ngIf="glossaryPageResponse$ | async as glossaryData">
  <section class="glossary">
    <div class="wrapper first-article-wrapper">
      <app-glossary-header></app-glossary-header>
      <div class="glossary-title">
        <kesma-icon [name]="'vg-chevron-right'" [size]="24"></kesma-icon>
        <h1 class="glossary-title-text">{{ glossaryData?.glossary.title }}</h1>
      </div>
      <div class="glossary-description">
        <vg-wysiwyg-box [htmlArray]="[glossaryData?.glossary?.description?.[0]?.details?.[0]?.value ?? '']"></vg-wysiwyg-box>
      </div>
      <h2 class="glossary-articles">Cikkek a témában</h2>
      <ng-container *ngIf="glossaryData.articles?.[0] as firstArticle">
        <vg-article-card
          class="first-article"
          [data]="firstArticle"
          [styleID]="ArticleCardType.FeaturedRightImgDateTagTitle"
          [displayLead]="true"
          [showHighlightedView]="true"
          [showYear]="true"
        ></vg-article-card>
      </ng-container>
      <vg-newsletter-box [styleID]="NewsletterBoxType.SocialSorted"></vg-newsletter-box>
    </div>
  </section>
  <section>
    <div class="wrapper with-aside">
      <div class="left-column">
        <div class="inner-wrapper">
          <div class="article-list">
            <ng-container *ngFor="let article of glossaryData.articles | slice: 1; let i = index; trackBy: trackByFn">
              <vg-article-card
                [data]="article"
                [styleID]="ArticleCardType.FeaturedRightImgDateTagTitle"
                [showThumbnail]="false"
                [showYear]="true"
                class="glossary-article-card"
              >
              </vg-article-card>
            </ng-container>
          </div>
        </div>
        <vg-pager
          *ngIf="rowAllCount > rowOnPageCount"
          [rowAllCount]="rowAllCount"
          [rowOnPageCount]="rowOnPageCount"
          [isListPager]="true"
          [hasSkipButton]="true"
          [allowAutoScrollToTop]="true"
          [maxDisplayedPages]="5"
        ></vg-pager>
      </div>
      <aside>
        <app-sidebar [adPageType]="'all_articles_and_sub_pages'" [excludedIds]="sidebarExcludedIds"></app-sidebar>
      </aside>
    </div>
  </section>
</ng-container>
