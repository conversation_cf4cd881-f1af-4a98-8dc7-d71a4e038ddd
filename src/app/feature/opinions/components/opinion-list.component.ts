import { ChangeDetectionStrategy, ChangeDetectorRef, Component, inject, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ArticleCard,
  createCanonicalUrlForPageablePage,
  mapBackendArticleDataToArticleCard,
  RedirectService,
  SelectedArticle,
} from '@trendency/kesma-ui';
import { SeoService } from '@trendency/kesma-core';
import { distinctUntilChanged, Subject } from 'rxjs';
import { filter, map, takeUntil } from 'rxjs/operators';
import {
  ArticleCardType,
  createVilaggazdasagTitle,
  defaultMetaInfo,
  NewsletterBoxType,
  VgArticleCardComponent,
  VgBlockTitleComponent,
  VgNewsletterBoxComponent,
  VgPagerComponent,
} from '../../../shared';
import { OpinionsService } from '../api/opinions.service';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { LayoutComponent } from '../../layout/components/layout/layout.component';
import { NgFor, NgForOf, NgIf, SlicePipe } from '@angular/common';

@Component({
  selector: 'app-opinion-list',
  templateUrl: './opinion-list.component.html',
  styleUrls: ['./opinion-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    LayoutComponent,
    VgArticleCardComponent,
    NgFor,
    NgForOf,
    SidebarComponent,
    SlicePipe,
    VgNewsletterBoxComponent,
    VgPagerComponent,
    VgBlockTitleComponent,
    AdvertisementAdoceanComponent,
  ],
})
export class OpinionListComponent implements OnInit, OnDestroy {
  layoutApiData: {
    struct: any[];
    content: any[];
  };
  simpleArticles: ArticleCard[] = [];
  adverts: AdvertisementsByMedium;
  rowAllCount = 0;
  rowOnPageCount = 12;
  rowFrom = 0;
  ArticleCardType = ArticleCardType;
  NewsletterBoxType = NewsletterBoxType;
  excludedIds: string[] = [];
  kesmaRedirectService = inject(RedirectService);
  private readonly destroy$ = new Subject<boolean>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly seo: SeoService,
    private readonly cdr: ChangeDetectorRef,
    private readonly opinionsService: OpinionsService
  ) {}

  ngOnInit(): void {
    this.excludedIds = [];
    this.layoutApiData = this.route.snapshot.data['layoutData'];
    if (this.layoutApiData?.content) {
      this.layoutApiData.content.forEach((content) => {
        this.setExcludedIds(content?.selectedArticles);
        this.setExcludedIds(content?.selectedOpinions);
      });
    }
    const pageIndex = this.route.snapshot.queryParams['page'] ? this.route.snapshot.queryParams['page'] - 1 : 0;
    this.opinionsService.getOpinionArticles(pageIndex, this.rowOnPageCount, this.excludedIds).subscribe((res) => {
      if (this.kesmaRedirectService.shouldBeRedirect(pageIndex, res?.data)) {
        this.kesmaRedirectService.redirectOldUrl(`velemeny`, false, 302);
      }
      this.simpleArticles = res.data.map(mapBackendArticleDataToArticleCard);
      this.rowAllCount = res.meta['limitable'].rowAllCount;
      this.rowOnPageCount = res.meta['limitable'].rowOnPageCount;
      this.rowFrom = res.meta['limitable'].rowFrom;
      this.cdr.markForCheck();
    });
    this.route.queryParams
      .pipe(
        filter((params) => 'page' in params),
        map((params) => params['page']),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe((page) => {
        this.fetchSimpleArticlesPage(page - 1);
        this.setMetaData();
      });
    this.initAds();
    this.setMetaData();
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  trackByFn = (index: number): number => index;

  private fetchSimpleArticlesPage(nextPage: any): void {
    this.opinionsService.getOpinionArticles(nextPage, this.rowOnPageCount, this.excludedIds).subscribe((res) => {
      this.simpleArticles = res.data.map(mapBackendArticleDataToArticleCard);
      this.cdr.markForCheck();
    });
  }

  private initAds(): void {
    this.adStoreAdo.advertisemenets$.pipe(takeUntil(this.destroy$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStoreAdo.separateAdsByMedium(ads);
      this.cdr.detectChanges();
    });
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('velemeny', this.route.snapshot);
    if (canonical) this.seo.updateCanonicalUrl(canonical);
    const title = createVilaggazdasagTitle('Vélemény');
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    });
  }

  private setExcludedIds(articles: SelectedArticle[]): void {
    if (articles) {
      articles.map((data: SelectedArticle) => {
        if (data?.id) {
          this.excludedIds.push(data.id);
        }
      });
    }
  }
}
