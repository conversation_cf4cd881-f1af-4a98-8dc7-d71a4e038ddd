<section>
  <div class="wrapper">
    <vg-block-title [headingLevel]="1" [data]="{ text: 'Vélemény' }"></vg-block-title>
    <app-layout *ngIf="layoutApiData" [structure]="layoutApiData.struct" [configuration]="layoutApiData.content"></app-layout>
    <div class="ad-block full-width">
      <kesma-advertisement-adocean
        *ngIf="adverts?.desktop?.roadblock_1 as ad"
        [style]="{
          margin: 'var(--ad-margin)',
          padding: 'var(--ad-padding)',
        }"
        [ad]="ad"
      >
      </kesma-advertisement-adocean>

      <kesma-advertisement-adocean
        *ngIf="adverts?.mobile?.mobilrectangle_1 as ad"
        [style]="{
          margin: 'var(--ad-margin)',
          padding: 'var(--ad-padding)',
        }"
        [ad]="ad"
      >
      </kesma-advertisement-adocean>
    </div>
  </div>
  <div class="wrapper with-aside">
    <div class="left-column">
      <div class="inner-wrapper">
        <vg-newsletter-box [styleID]="NewsletterBoxType.Gong"></vg-newsletter-box>
        <ng-container *ngFor="let article of simpleArticles | slice: 0; let i = index; trackBy: trackByFn">
          <vg-article-card [data]="article" [styleID]="ArticleCardType.FeaturedRightImgDateTagTitle"></vg-article-card>
          <div class="ad-block in-left-column" *ngIf="i === 4">
            <kesma-advertisement-adocean
              *ngIf="adverts?.desktop?.roadblock_2 as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg-light)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>

            <kesma-advertisement-adocean
              *ngIf="adverts?.mobile?.mobilrectangle_2 as ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg-light)',
                padding: 'var(--ad-padding)',
              }"
              [ad]="ad"
            >
            </kesma-advertisement-adocean>
          </div>
        </ng-container>
        <vg-pager
          *ngIf="rowAllCount > rowOnPageCount"
          [rowAllCount]="rowAllCount || 0"
          [rowOnPageCount]="rowOnPageCount || 0"
          [isListPager]="true"
          [allowAutoScrollToTop]="true"
          [maxDisplayedPages]="5"
          [hasSkipButton]="true"
        >
        </vg-pager>
        <div class="ad-block in-left-column">
          <kesma-advertisement-adocean
            *ngIf="adverts?.desktop?.roadblock_3 as ad"
            [ad]="ad"
            [style]="{
              margin: 'var(--ad-margin)',
              background: 'var(--ad-bg-light)',
              padding: 'var(--ad-padding)',
            }"
          >
          </kesma-advertisement-adocean>

          <kesma-advertisement-adocean
            *ngIf="adverts?.mobile?.mobilrectangle_3 as ad"
            [style]="{
              margin: 'var(--ad-margin)',
              background: 'var(--ad-bg-light)',
              padding: 'var(--ad-padding)',
            }"
            [ad]="ad"
          >
          </kesma-advertisement-adocean>
        </div>
      </div>
    </div>
    <aside>
      <app-sidebar [excludedIds]="excludedIds"></app-sidebar>
    </aside>
  </div>
</section>
