import { ArticleSocial, Meta } from '@trendency/kesma-ui';

export type OpinionTypeArticleData = ArticleSocial &
  Readonly<{
    readonly avatarThumbnail?: string;
    readonly avatarThumbnailCreatedAt?: Date;
    readonly avatarImage?: string;
    readonly columnId?: string;
    readonly columnSlug?: string;
    readonly columnTitle?: string;
    readonly excerpt?: string;
    readonly lead?: string;
    readonly id: string;
    readonly isActive: boolean;
    readonly isOpinion: boolean;
    readonly primary_column_id?: string;
    readonly publishDate?: string;
    readonly readingLength: number;
    readonly slug: string;
    readonly thumbnail?: string;
    readonly thumbnailCreatedAt?: Date;
    readonly thumbnailUrl?: string;
    readonly title: string;
    readonly preTitleColor: string;
    readonly preTitle: string;
    readonly author: string;
    readonly recommendedTitle: string;
    readonly chLead: string;
    readonly chExcerpt: string;
    readonly articleSource?: string;
    readonly articleMedium?: string;
  }>;

export type OpinionTypeResponse = Readonly<{
  readonly data: readonly OpinionTypeArticleData[];
  readonly meta: Meta;
}>;

export type OpinionTypeResolverResponse = Readonly<{
  readonly opinions: OpinionTypeResponse;
}>;

export type OpinionTypeParams = Readonly<{
  readonly rowCount_limit?: string;
  readonly 'excludedArticleIds[]'?: readonly string[];
  readonly 'content_types[]'?: string[];
}>;
