import { Injectable } from '@angular/core';
import { IHttpOptions, ReqService } from '@trendency/kesma-core';
import { Observable } from 'rxjs';
import { OpinionTypeParams } from './opinions.definitions';
import { ApiResponseMetaList, ApiResult, BackendArticleSearchResult, LayoutApiData } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class OpinionsService {
  constructor(private readonly reqService: ReqService) {}

  getOpinions(): Observable<ApiResult<LayoutApiData, ApiResponseMetaList>> {
    const params: OpinionTypeParams = {} as OpinionTypeParams;
    return this.reqService.get(`/opinion-page`, { params });
  }

  getOpinionArticles(page = 0, itemsPerPage = 12, excludedArticleIds: string[] = [], columnSlug?: string): Observable<ApiResult<BackendArticleSearchResult[]>> {
    let { params }: IHttpOptions = {
      params: {
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
        'excludedArticleIds[]': excludedArticleIds,
      },
    };
    params = columnSlug ? { ...params, columnSlug } : params;
    return this.reqService.get('/content-page/articles-by-opinion-type', { params: params });
  }
}
