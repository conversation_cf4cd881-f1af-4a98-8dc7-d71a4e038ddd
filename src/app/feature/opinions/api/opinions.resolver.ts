import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { OpinionsService } from './opinions.service';
import { catchError, map } from 'rxjs/operators';
import { LayoutApiData } from '@trendency/kesma-ui';

@Injectable()
export class OpinionsResolver {
  constructor(
    private readonly opinionsService: OpinionsService,
    private readonly router: Router
  ) {}

  resolve(): Observable<LayoutApiData> {
    return this.opinionsService.getOpinions().pipe(
      catchError((err) => {
        this.router.navigate(['/', '404'], {
          skipLocationChange: true,
        });
        return throwError(err);
      }),
      map((res) => res.data)
    );
  }
}
