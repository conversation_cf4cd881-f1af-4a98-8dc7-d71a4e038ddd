<section>
  <div class="wrapper with-aside foundation-content">
    <div class="left-column">
      <h1 class="foundation-content-title">Tartalmaink</h1>

      <div *ngIf="searchResponse$ | async as searchResponse" class="foundation-content-list">
        <ng-container *ngFor="let article of articles$ | async; let i = index; trackBy: trackByFn">
          <vg-article-card [data]="article" [styleID]="ArticleCardType.FeaturedRightImgDateTagTitle"></vg-article-card>

          <ng-container *ngIf="i === 3">
            <kesma-advertisement-adocean
              *ngIf="adverts?.desktop?.roadblock_1 as ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg-light)',
                padding: 'var(--ad-padding)',
              }"
              [ad]="ad"
            >
            </kesma-advertisement-adocean>

            <kesma-advertisement-adocean
              *ngIf="adverts?.mobile?.mobilrectangle_1 as ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg-light)',
                padding: 'var(--ad-padding)',
              }"
              [ad]="ad"
            >
            </kesma-advertisement-adocean>
          </ng-container>

          <ng-container *ngIf="i === 11">
            <kesma-advertisement-adocean
              *ngIf="adverts?.desktop?.roadblock_2 as ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg-light)',
                padding: 'var(--ad-padding)',
              }"
              [ad]="ad"
            >
            </kesma-advertisement-adocean>
            <kesma-advertisement-adocean
              *ngIf="adverts?.mobile?.mobilrectangle_2 as ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg-light)',
                padding: 'var(--ad-padding)',
              }"
              [ad]="ad"
            >
            </kesma-advertisement-adocean>
          </ng-container>
        </ng-container>

        <vg-pager
          *ngIf="searchResponse.meta.limitable.pageMax && searchResponse.meta.limitable.pageMax > 0"
          [allowAutoScrollToTop]="true"
          [hasSkipButton]="true"
          [isListPager]="true"
          [maxDisplayedPages]="5"
          [rowAllCount]="searchResponse.meta.limitable?.rowAllCount || 0"
          [rowOnPageCount]="searchResponse.meta.limitable?.rowOnPageCount || 0"
        >
        </vg-pager>

        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.roadblock_3 as ad"
          [style]="{
            margin: 'var(--ad-margin)',
            background: 'var(--ad-bg-light)',
            padding: 'var(--ad-padding)',
            width: '100%',
          }"
          [ad]="ad"
        >
        </kesma-advertisement-adocean>
        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.mobilrectangle_3 as ad"
          [style]="{
            margin: 'var(--ad-margin)',
            background: 'var(--ad-bg-light)',
            padding: 'var(--ad-padding)',
          }"
          [ad]="ad"
        >
        </kesma-advertisement-adocean>
      </div>
    </div>
    <aside>
      <app-sidebar [excludedIds]="sidebarExcludedIds"></app-sidebar>
    </aside>
  </div>
</section>
