import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ApiListResult,
  ArticleCard,
  ArticleSearchResult,
  createCanonicalUrlForPageablePage,
  searchResultToArticleCard,
} from '@trendency/kesma-ui';
import { Observable, Subject, takeUntil, tap } from 'rxjs';
import { map } from 'rxjs/operators';
import { ArticleCardType, createVilaggazdasagTitle, defaultMetaInfo, VgArticleCardComponent, VgPagerComponent } from '../../shared';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { AsyncPipe, NgFor, NgIf } from '@angular/common';

@Component({
  selector: 'app-foundation-content',
  templateUrl: './foundation-content.component.html',
  styleUrls: ['./foundation-content.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, VgArticleCardComponent, SidebarComponent, AsyncPipe, VgPagerComponent, AdvertisementAdoceanComponent],
})
export class FoundationContentComponent implements OnInit, OnDestroy {
  searchResponse$: Observable<ApiListResult<ArticleSearchResult>> = this.route.data.pipe(map((res) => res['searchResponse']));
  articles$: Observable<ArticleCard[]> = this.searchResponse$.pipe(
    map((res) => res.data.map((sr) => searchResultToArticleCard(sr))),
    tap((data) => this.populateSidebarExcludedIds(data))
  );

  adverts: AdvertisementsByMedium;
  ArticleCardType = ArticleCardType;

  sidebarExcludedIds: string[] = [];

  private readonly destroy$: Subject<void> = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly cd: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService
  ) {}

  ngOnInit(): void {
    this.setMetaData();

    this.adStore.advertisemenets$
      .pipe(
        map((ads: Advertisement[]) => this.adStore.separateAdsByMedium(ads)),
        takeUntil(this.destroy$)
      )
      .subscribe((ads: AdvertisementsByMedium) => {
        this.adverts = ads;
        this.cd.markForCheck();
      });
  }

  trackByFn(index: number, item: ArticleCard): string {
    return item.id ?? item.slug ?? index?.toString();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private populateSidebarExcludedIds(articles: ArticleCard[]): void {
    if (articles?.length) this.sidebarExcludedIds = articles.map((item) => item?.id) as string[];
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage(this.seo.currentUrl, this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);
    const title = createVilaggazdasagTitle(`Alapkőtartalom`);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'index, follow',
      description: defaultMetaInfo.description,
      ogDescription: defaultMetaInfo.description,
    };
    this.seo.setMetaData(metaData);
  }
}
