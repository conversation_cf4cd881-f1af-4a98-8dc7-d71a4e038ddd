@use 'shared' as *;

.foundation-content {
  padding-bottom: 32px;

  .left-column {
    @include media-breakpoint-down(md) {
      width: 100%;
    }
  }

  aside {
    @include media-breakpoint-down(md) {
      width: 100%;
    }
  }

  &-title {
    color: var(--kui-deep-teal-900);
    font-size: 24px;
    font-weight: 700;
    line-height: normal;
    letter-spacing: 0.96px;
    margin-bottom: 24px;
  }

  &-list {
    gap: 24px;
    display: flex;
    flex-direction: column;
  }

  vg-pager {
    margin-top: 24px;
  }
}
