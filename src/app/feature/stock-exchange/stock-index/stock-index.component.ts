import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import {
  createVilaggazdasagTitle,
  defaultMetaInfo,
  FinancialAPIStockIndexTopData,
  FinancialAPIStockIndexTopResult,
  isStockDailyDataAvailable,
  NewsletterBoxType,
  StockExchangeService,
  VgNewsletterBoxComponent,
} from '../../../shared';
import { Observable, of } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { createCanonicalUrlForPageablePage, IconComponent, PAGE_TYPES } from '@trendency/kesma-ui';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { AsyncPipe } from '@angular/common';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { RouterLink } from '@angular/router';
import { StockIndexTop5Component } from './stock-index-top-5/stock-index-top-5.component';
import { StockIndexChartComponent } from './stock-index-chart/stock-index-chart.component';
import { StockSelectComponent } from '../stock-select/stock-select.component';

@Component({
  selector: 'app-stock-index',
  templateUrl: './stock-index.component.html',
  styleUrls: ['./stock-index.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    StockSelectComponent,
    StockIndexChartComponent,
    StockIndexTop5Component,
    RouterLink,
    SidebarComponent,
    AsyncPipe,
    VgNewsletterBoxComponent,
    IconComponent,
  ],
})
export class StockIndexComponent implements OnInit {
  stockIndexTop5Winners$: Observable<FinancialAPIStockIndexTopData[]> = this.stockExchangeService.autoRefresh$.pipe(
    switchMap(() =>
      isStockDailyDataAvailable()
        ? this.stockExchangeService.getStockIndexTop5WinnerData().pipe(map((result: FinancialAPIStockIndexTopResult) => result.data))
        : of([])
    )
  );

  stockIndexTop5Losers$: Observable<FinancialAPIStockIndexTopData[]> = this.stockExchangeService.autoRefresh$.pipe(
    switchMap(() =>
      isStockDailyDataAvailable()
        ? this.stockExchangeService.getStockIndexTop5LoserData().pipe(map((result: FinancialAPIStockIndexTopResult) => result.data))
        : of([])
    )
  );

  stockIndexTop5Turnovers$: Observable<FinancialAPIStockIndexTopData[]> = this.stockExchangeService.autoRefresh$.pipe(
    switchMap(() =>
      isStockDailyDataAvailable()
        ? this.stockExchangeService.getStockIndexTop5TurnoverData().pipe(map((result: FinancialAPIStockIndexTopResult) => result.data))
        : of([])
    )
  );

  adPageType: string = PAGE_TYPES.all_articles_and_sub_pages;

  NewsletterBoxType = NewsletterBoxType;

  get selectedStockIndex(): string | null {
    return this.stockExchangeService.selectedStockIndex;
  }

  constructor(
    private readonly stockExchangeService: StockExchangeService,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    this.stockExchangeService.selectedStock$.next(null);
    this.setMetaData();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('reszvenyek/indexek');
    canonical && this.seo.updateCanonicalUrl(canonical);
    const title = createVilaggazdasagTitle(`Részvényindexek`);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'index, follow',
      description:
        'BÉT részvényindexek (BUX, BUMIX, CETOP), utolsó ár, árfolyam változások, grafikonok, napi, heti, havi, éves adatok. ' +
        '15 perccel késleltetett adatok.',
      ogDescription:
        'BÉT részvényindexek (BUX, BUMIX, CETOP), utolsó ár, árfolyam változások, grafikonok, napi, heti, havi, éves adatok. ' +
        '15 perccel késleltetett adatok.',
    };
    this.seo.setMetaData(metaData);
  }
}
