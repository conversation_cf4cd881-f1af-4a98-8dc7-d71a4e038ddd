import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { FinancialAPIStockIndexListData, FinancialAPIStockIndexListResult, StockExchangeService } from '../../../shared';

@Injectable()
export class StockIndexResolver {
  constructor(
    private readonly stockExchangeService: StockExchangeService,
    private readonly router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<FinancialAPIStockIndexListResult> {
    const stockIndex = route.params['stockIndex']?.toUpperCase() ?? '';

    return this.stockExchangeService.getStockIndexListData().pipe(
      tap((result: FinancialAPIStockIndexListResult) => {
        const stockIndexData: FinancialAPIStockIndexListData | undefined =
          result?.data?.find((data: FinancialAPIStockIndexListData) => data.sid === stockIndex) ?? result?.data?.[0];

        if (!stockIndexData) {
          this.router.navigate(['/404'], { skipLocationChange: true }).then();
        } else {
          this.stockExchangeService.selectedStockIndex$.next(stockIndexData.sid);
        }
      }),
      catchError((err) => {
        this.router.navigate(['/404'], { skipLocationChange: true }).then();
        return throwError(() => err);
      })
    );
  }
}
