import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FinancialAPIStockIndexTopData, VgSpinnerComponent } from '../../../../shared';
import { RouterLink } from '@angular/router';
import { DecimalPipe, LowerCasePipe, NgClass, NgFor, NgIf } from '@angular/common';

@Component({
  selector: 'app-stock-index-top-5',
  templateUrl: './stock-index-top-5.component.html',
  styleUrls: ['./stock-index-top-5.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, NgClass, RouterLink, LowerCasePipe, DecimalPipe, VgSpinnerComponent],
})
export class StockIndexTop5Component {
  @Input() name: string;
  @Input() top5Data: FinancialAPIStockIndexTopData[] | null;
}
