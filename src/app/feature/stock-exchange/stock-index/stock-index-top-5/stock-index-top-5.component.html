<div class="stock-index-top-5">
  <div class="stock-index-top-5-name">{{ name }}</div>
  <div *ngIf="top5Data; else loading" class="stock-index-top-5-table stock-list-table">
    <table>
      <thead>
        <th>Név</th>
        <th><PERSON><PERSON><PERSON><PERSON></th>
        <th>Vált.</th>
        <th>Vált.<br />(%)</th>
        <th>Forg.<br />(m Ft)</th>
      </thead>
      <tbody>
        <ng-container *ngIf="top5Data?.length !== 0; else noData">
          <tr
            *ngFor="let data of top5Data"
            [ngClass]="{
              up: data.changePercentage && data.changePercentage > 0,
              down: data.changePercentage && data.changePercentage < 0,
            }"
            [routerLink]="['/reszvenyek', data.name | lowercase]"
          >
            <td>
              <a [routerLink]="['/reszvenyek', data.name | lowercase]">{{ data.name }}</a>
            </td>
            <td>{{ data.lastPrice | number: '1.0-4' }}</td>
            <td class="color">{{ data.changeDiffLastPrice > 0 ? '+' : '' }}{{ data.changeDiffLastPrice | number: '1.0-4' }}</td>
            <td class="color">{{ data.changePercentage > 0 ? '+' : '' }}{{ data.changePercentage | number: '1.0-2' }}</td>
            <td>{{ data.accumulatedTurnover / 1000000 | number: '1.0-2' }}</td>
          </tr>
        </ng-container>
      </tbody>
    </table>
  </div>
</div>

<ng-template #loading>
  <vg-spinner [height]="50" [width]="50"></vg-spinner>
</ng-template>

<ng-template #noData>
  <tr>
    <td class="no-data" colspan="5">Nincs adat</td>
  </tr>
</ng-template>
