<section>
  <div class="wrapper with-aside stock-index">
    <div class="left-column">
      <div class="stock-index-header">
        <div>
          <h1 class="stock-exchange-title">Részvényindexek</h1>
          <div class="stock-exchange-subtitle">Forrás: Budapesti Értéktőzsde Nyrt.</div>
        </div>
        <app-stock-select></app-stock-select>
      </div>
      <app-stock-index-chart [selectedStockIndex]="selectedStockIndex"></app-stock-index-chart>
      <div class="stock-index-tables">
        <div class="stock-index-tables-group">
          <app-stock-index-top-5 [top5Data]="stockIndexTop5Winners$ | async" name="Top 5 részvény nyertes"></app-stock-index-top-5>
          <app-stock-index-top-5 [top5Data]="stockIndexTop5Losers$ | async" name="Top 5 részvény vesztes"></app-stock-index-top-5>
        </div>
        <div class="stock-index-tables-group">
          <app-stock-index-top-5 [top5Data]="stockIndexTop5Turnovers$ | async" name="Top 5 részvény forgalom"></app-stock-index-top-5>
          <div class="stock-index-link">
            <a routerLink="/reszvenyek">
              <span>Összes részvény megtekintése</span>
              <kesma-icon [size]="24" name="vg-chevron-diagonal-right"></kesma-icon>
            </a>
          </div>
        </div>
      </div>
      <vg-newsletter-box [styleID]="NewsletterBoxType.SocialSorted"></vg-newsletter-box>
    </div>
    <aside>
      <app-sidebar [adPageType]="adPageType"></app-sidebar>
    </aside>
  </div>
</section>
