import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { StockIndexChartComponent } from '../stock-index-chart/stock-index-chart.component';

@Component({
  selector: 'app-stock-index-detail-article',
  templateUrl: './stock-index-detail-article.component.html',
  styleUrls: ['./stock-index-detail-article.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [StockIndexChartComponent],
})
export class StockIndexDetailArticleComponent {
  selectedStockIndex = input<string | null>(null);
}
