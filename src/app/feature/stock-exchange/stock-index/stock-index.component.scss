@use 'shared' as *;

.stock-index {
  .left-column {
    @include media-breakpoint-down(lg) {
      width: 100%;
    }
  }

  aside {
    @include media-breakpoint-down(lg) {
      width: 100%;
    }
  }

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    padding-bottom: 24px;

    @include media-breakpoint-down(sm) {
      flex-direction: column;
      align-items: flex-start;
    }
  }

  &-tables {
    margin: 32px 0;
    display: flex;
    gap: 32px;
    flex-direction: column;

    @include media-breakpoint-down(sm) {
      gap: 24px;
      margin: 24px 0 32px;
    }

    &-group {
      display: flex;
      gap: 24px;

      @include media-breakpoint-down(sm) {
        flex-direction: column;
      }

      > * {
        flex: 1;
      }
    }
  }

  &-link {
    a {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      border-bottom: 1px solid var(--kui-gray-950);
      padding: 0 0 8px;
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
      transition: 0.3s;

      @media (hover: hover) {
        &:hover {
          color: var(--kui-amethyst-500);
          border-bottom: 1px solid var(--kui-amethyst-500);
        }
      }
    }
  }
}
