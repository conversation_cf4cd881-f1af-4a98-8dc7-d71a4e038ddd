import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, input, On<PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { BehaviorSubject, combineLatest, forkJoin, Observable, of, Subject } from 'rxjs';
import { format } from 'date-fns';
import { createChart, CustomData, IChartApi, IPriceLine, ISeriesApi, MouseEventParams, TickMarkType, UTCTimestamp } from 'lightweight-charts';
import { AsyncPipe, DecimalPipe, Location, NgClass } from '@angular/common';
import {
  convertUTCTimestampToDate,
  financialAPIStockIndexChartDataMapper,
  FinancialAPIStockIndexGraphPoint,
  FinancialAPIStockIndexListData,
  FinancialAPIStockIndexListResult,
  getMinMaxPrice,
  isStockDailyDataAvailable,
  StockExchangeService,
  stockIndexChartDefaultConfig,
  stockIndexChartSeriesConfig,
  StockIndexChartType,
  VgSpinnerComponent,
} from '../../../../shared';
import { debounceTime, distinctUntilChanged, filter, map, switchMap, takeUntil, tap } from 'rxjs/operators';
import { UtilService } from '@trendency/kesma-core';
import { toObservable } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-stock-index-chart',
  templateUrl: './stock-index-chart.component.html',
  styleUrls: ['./stock-index-chart.component.scss'],
  providers: [DecimalPipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgClass, AsyncPipe, DecimalPipe, VgSpinnerComponent],
})
export class StockIndexChartComponent implements AfterViewInit, OnDestroy {
  showHeaderInfo = input<boolean>(true);
  selectedStockIndex = input<string | null>(null);
  selectedStockIndex$ = toObservable(this.selectedStockIndex);

  stockIndexOptions$: Observable<FinancialAPIStockIndexListData[]> = this.stockExchangeService
    .getStockIndexListData()
    .pipe(map((result: FinancialAPIStockIndexListResult) => result.data));
  chartType$: BehaviorSubject<StockIndexChartType> = new BehaviorSubject<StockIndexChartType>(StockIndexChartType.DAY);

  chart: IChartApi;
  dataSeries: ISeriesApi<'Area'>;
  isChartLoaded = false;
  isChartDataLoading = false;
  isChartEmpty = false;

  StockIndexChartType = StockIndexChartType;

  buxSumCapitalization: number | null = null;

  unsubscribe$: Subject<void> = new Subject<void>();

  @ViewChild('chartContainer', { static: false }) chartContainer: ElementRef<HTMLDivElement>;
  @ViewChild('chartTooltip', { static: false }) chartTooltip: ElementRef<HTMLDivElement>;

  constructor(
    private readonly stockExchangeService: StockExchangeService,
    private readonly decimalPipe: DecimalPipe,
    private readonly cdr: ChangeDetectorRef,
    private readonly utilsService: UtilService,
    private readonly location: Location
  ) {}

  ngAfterViewInit(): void {
    if (this.utilsService.isBrowser()) {
      this.handleChartData();
    }
  }

  initChart(): void {
    this.chart = createChart(this.chartContainer.nativeElement, stockIndexChartDefaultConfig);
    this.dataSeries = this.chart.addAreaSeries(stockIndexChartSeriesConfig);
  }

  handleChartData(): void {
    let closingPriceLine: IPriceLine | null = null;

    combineLatest([this.chartType$.pipe(distinctUntilChanged()), this.selectedStockIndex$.pipe(distinctUntilChanged()), this.stockExchangeService.autoRefresh$])
      .pipe(
        tap(([chartType, stockIndex]) => {
          this.isChartDataLoading = true;

          if (stockIndex === 'CETO' && (chartType === StockIndexChartType.DAY || chartType === StockIndexChartType.WEEK)) {
            this.chartType$.next(StockIndexChartType.MONTH);
          }
        }),
        filter(([chartType, stockIndex]) => stockIndex !== 'CETO' || (chartType !== StockIndexChartType.DAY && chartType !== StockIndexChartType.WEEK)),
        debounceTime(10), // Select different stock sets chart type to day immediately, don't trigger API call twice
        switchMap(([chartType, stockIndex]) =>
          forkJoin([
            of(stockIndex),
            isStockDailyDataAvailable() || chartType !== StockIndexChartType.DAY
              ? this.stockExchangeService.getStockIndexGraphData(stockIndex ?? '', chartType)
              : of(null),
          ])
        ),
        takeUntil(this.unsubscribe$)
      )
      .subscribe(([stockIndex, chartData]) => {
        if (!this.chart) {
          this.initChart();
          this.handleChartTooltip();
          this.isChartLoaded = true;
        }

        if (isStockDailyDataAvailable()) {
          this.buxSumCapitalization = chartData?.data?.buxSumCapitalization ?? null;
        }
        this.dataSeries.setData(financialAPIStockIndexChartDataMapper(chartData?.data?.points ?? [], this.chartType$.value, stockIndex));

        const minMaxPrice: {
          min: number;
          max: number;
        } = getMinMaxPrice(
          (chartData?.data?.points ?? []).map((p) => p.currentValue),
          chartData?.data?.closingPrice ?? 0
        );
        this.dataSeries.applyOptions({
          autoscaleInfoProvider: () => ({
            priceRange: {
              minValue: minMaxPrice.min,
              maxValue: minMaxPrice.max,
            },
          }),
        });

        if (closingPriceLine) {
          this.dataSeries.removePriceLine(closingPriceLine);
          closingPriceLine = null;
        }
        if (this.chartType$.value === StockIndexChartType.DAY && chartData?.data?.closingPrice) {
          closingPriceLine = this.dataSeries.createPriceLine({
            price: chartData.data.closingPrice,
            color: '#2dd4b7',
            axisLabelTextColor: '#fff',
            title: 'Előző záróár',
          });
        }

        this.applyChartTimeFormats();
        this.chart.timeScale().fitContent();

        if (this.utilsService.isBrowser()) {
          setTimeout(() => {
            this.isChartDataLoading = false;
            this.cdr.detectChanges();
          }, 300);
        } else {
          this.isChartDataLoading = false;
        }

        this.isChartEmpty = (chartData?.data?.points?.filter((p) => !!p.currentValue)?.length ?? 0) === 0;
        this.cdr.detectChanges();
      });
  }

  handleChartTooltip(): void {
    this.chart.subscribeCrosshairMove((param: MouseEventParams) => {
      const tooltip: HTMLDivElement = this.chartTooltip.nativeElement;

      if (param.point === undefined || !param.time || param.point.x < 0 || param.point.y < 0) {
        tooltip.style.display = 'none';
      } else {
        const pointDate: Date = convertUTCTimestampToDate(param.time as UTCTimestamp);
        let dateStr: string = format(pointDate, 'yyyy.MM.dd.');
        const data: CustomData | undefined = param.seriesData.get(this.dataSeries);

        // Display tooltip only if custom values are available
        if (data?.customValues) {
          const aggregatedData: FinancialAPIStockIndexGraphPoint & {
            stockIndex: string;
            graphType: StockIndexChartType;
          } = data.customValues as any;

          if (aggregatedData.graphType === StockIndexChartType.DAY) {
            dateStr = format(pointDate, 'HH:mm:ss');
          }
          if (aggregatedData.graphType === StockIndexChartType.WEEK) {
            dateStr = format(pointDate, 'yyyy.MM.dd. HH:mm');
          }

          tooltip.style.display = 'block';
          tooltip.innerHTML = `
            <div>Idő: <strong>${dateStr}</strong></div>
            <div>Árfolyam: <strong>${this.decimalPipe.transform(aggregatedData.currentValue ?? 0, '1.0-4')}</strong></div>
        `;

          // Position tooltip according to mouse cursor position
          tooltip.style.left = param.point.x + 'px';
          tooltip.style.top = param.point.y + 'px';
        }
      }
    });
  }

  applyChartTimeFormats(): void {
    this.chart.applyOptions({
      localization: {
        // Format datetime labels on cross-hair hover (x axis hover label)
        timeFormatter: (timestamp: UTCTimestamp) => {
          const date: Date = convertUTCTimestampToDate(timestamp);

          switch (this.chartType$.value) {
            case StockIndexChartType.DAY:
            case StockIndexChartType.WEEK:
              return format(date, 'MM.dd. HH:mm');
            case StockIndexChartType.MONTH:
              return format(date, 'MM.dd.');
            case StockIndexChartType.YEAR:
            case StockIndexChartType.THREE_YEARS:
            case StockIndexChartType.ALL:
              return format(date, 'yyyy.MM.dd.');
            default:
              return '';
          }
        },
        // Format price with angular number formatter (decimal precision 2-4)
        priceFormatter: (priceValue: number): string => {
          return this.decimalPipe.transform(priceValue, '1.2-4') ?? '';
        },
      },
      timeScale: {
        // Format datetime labels on x axis
        tickMarkFormatter: (timestamp: UTCTimestamp, tickMarkType: TickMarkType) => {
          const date: Date = convertUTCTimestampToDate(timestamp);

          switch (this.chartType$.value) {
            case StockIndexChartType.DAY:
              return format(date, 'HH:mm');
            case StockIndexChartType.WEEK:
            case StockIndexChartType.MONTH:
              if (tickMarkType === TickMarkType.Time) {
                return '';
              }
              return format(date, 'MM.dd.');
            case StockIndexChartType.YEAR:
            case StockIndexChartType.THREE_YEARS:
            case StockIndexChartType.ALL:
              return format(date, 'yyyy.MM.');
            default:
              return '';
          }
        },
      },
    });
  }

  selectStockIndex(stockIndex: string): void {
    this.stockExchangeService.selectedStockIndex$.next(stockIndex);
    this.chartType$.next(StockIndexChartType.DAY);
    this.location.replaceState(`/reszvenyek/indexek/${stockIndex.toLowerCase()}`, '', {});
  }

  changeChartType(type: StockIndexChartType): void {
    this.chartType$.next(type);
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
