@if (showHeaderInfo()) {
  @if (isChartLoaded) {
    <div class="stock-index-data">
      BUX kapitalizáció:
      <strong>
        {{ buxSumCapitalization !== null ? (buxSumCapitalization ?? 0 / 1000000000 | number: '1.0-0') + ' mrd Ft' : 'N.A.' }}
      </strong>
    </div>
  } @else {
    <vg-spinner [height]="50" [width]="50"></vg-spinner>
  }

  @if (stockIndexOptions$ | async; as stockIndexOptions) {
    <div class="stock-index-types">
      @for (stockIndex of stockIndexOptions; track stockIndex.sid) {
        <div (click)="selectStockIndex(stockIndex.sid)" [ngClass]="{ active: selectedStockIndex() === stockIndex.sid }" class="stock-index-type">
          {{ stockIndex.displayName }}
        </div>
      }
    </div>
  }
}

<div class="stock-index-chart">
  @if (chartType$ | async; as chartType) {
    <div class="stock-index-chart-types">
      @if (selectedStockIndex() !== 'CETO') {
        <div (click)="changeChartType(StockIndexChartType.DAY)" [ngClass]="{ active: chartType === StockIndexChartType.DAY }" class="stock-index-chart-type">
          Napi
        </div>
        <div (click)="changeChartType(StockIndexChartType.WEEK)" [ngClass]="{ active: chartType === StockIndexChartType.WEEK }" class="stock-index-chart-type">
          Heti
        </div>
      }
      <div (click)="changeChartType(StockIndexChartType.MONTH)" [ngClass]="{ active: chartType === StockIndexChartType.MONTH }" class="stock-index-chart-type">
        Havi
      </div>
      <div (click)="changeChartType(StockIndexChartType.YEAR)" [ngClass]="{ active: chartType === StockIndexChartType.YEAR }" class="stock-index-chart-type">
        1 év
      </div>
      <div
        (click)="changeChartType(StockIndexChartType.THREE_YEARS)"
        [ngClass]="{ active: chartType === StockIndexChartType.THREE_YEARS }"
        class="stock-index-chart-type"
      >
        3 év
      </div>
      <div (click)="changeChartType(StockIndexChartType.ALL)" [ngClass]="{ active: chartType === StockIndexChartType.ALL }" class="stock-index-chart-type">
        Mind
      </div>
    </div>
  }
  <div class="stock-index-chart-wrapper">
    @if (isChartLoaded && isChartDataLoading) {
      <div class="stock-index-chart-data-loader">
        <vg-spinner [height]="30" [width]="30"></vg-spinner>
      </div>
    }
    @if (!isChartLoaded) {
      <div>
        <vg-spinner [height]="50" [width]="50"></vg-spinner>
      </div>
    }
    @if (isChartEmpty) {
      <div class="stock-index-chart-no-data">Nincs adat</div>
    }
    <div #chartContainer class="stock-index-chart-chart"></div>
    <div #chartTooltip class="stock-index-chart-tooltip"></div>
  </div>
</div>
