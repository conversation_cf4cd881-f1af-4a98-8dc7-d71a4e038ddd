import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { IMetaData, SeoService, UtilService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  createCanonicalUrlForPageablePage,
  IconComponent,
} from '@trendency/kesma-ui';
import { interval, Observable, of, shareReplay, Subject } from 'rxjs';
import { map, startWith, switchMap, takeUntil } from 'rxjs/operators';
import {
  createVilaggazdasagTitle,
  defaultMetaInfo,
  FinancialAPIStockListData,
  FinancialAPIStockListResult,
  formatStockDate,
  isStockDailyDataAvailable,
  StockExchangeService,
  VgSpinnerComponent,
} from '../../../shared';
import { RouterLink } from '@angular/router';
import { StockSelectComponent } from '../stock-select/stock-select.component';
import { AsyncPipe, DecimalPipe, LowerCasePipe, NgClass, NgFor, NgIf, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-stock-list',
  templateUrl: './stock-list.component.html',
  styleUrls: ['./stock-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    StockSelectComponent,
    NgTemplateOutlet,
    RouterLink,
    NgFor,
    NgClass,
    AsyncPipe,
    LowerCasePipe,
    DecimalPipe,
    VgSpinnerComponent,
    AdvertisementAdoceanComponent,
    IconComponent,
  ],
})
export class StockListComponent implements OnInit, OnDestroy {
  readonly autoRefreshTime: number = this.stockExchangeService.autoRefreshTime;
  autoRefresh$: Observable<number> = this.utilsService.isBrowser() ? interval(this.autoRefreshTime * 1000).pipe(startWith(0)) : of(0);
  adverts?: AdvertisementsByMedium;
  unsubscribe$: Subject<void> = new Subject<void>();

  listData$: Observable<FinancialAPIStockListData[]> = this.autoRefresh$.pipe(
    switchMap(() =>
      this.stockExchangeService.getStockListData().pipe(
        map((result: FinancialAPIStockListResult) =>
          result.data.map((data: FinancialAPIStockListData) => ({
            ...data,
            ...(isStockDailyDataAvailable()
              ? {}
              : ({
                  lastPrice: data.closingPrice,
                  changeDiffLastPrice: null,
                  changePercentage: null,
                  accumulatedTurnover: null,
                  accumulatedVolume: null,
                  bidPriceAtLevel: null,
                  bidVolumeAtLevel: null,
                  askPriceAtLevel: null,
                  askVolumeAtLevel: null,
                  firstPrice: null,
                  lowPrice: null,
                  highPrice: null,
                  closingPrice: null,
                } as FinancialAPIStockListData)),
          }))
        )
      )
    ),
    shareReplay({
      bufferSize: 1,
      refCount: true,
    })
  );

  xtendListData$: Observable<FinancialAPIStockListData[]> = this.autoRefresh$.pipe(
    switchMap(() =>
      this.stockExchangeService.getXtendStockListData().pipe(
        map((result: FinancialAPIStockListResult) =>
          result.data.map((data: FinancialAPIStockListData) => ({
            ...data,
            ...(isStockDailyDataAvailable()
              ? {}
              : ({
                  lastPrice: data.closingPrice,
                  changeDiffLastPrice: null,
                  changePercentage: null,
                  accumulatedTurnover: null,
                  accumulatedVolume: null,
                  bidPriceAtLevel: null,
                  bidVolumeAtLevel: null,
                  askPriceAtLevel: null,
                  askVolumeAtLevel: null,
                  firstPrice: null,
                  lowPrice: null,
                  highPrice: null,
                  closingPrice: null,
                } as FinancialAPIStockListData)),
          }))
        )
      )
    ),
    shareReplay({
      bufferSize: 1,
      refCount: true,
    })
  );

  formatStockDate = formatStockDate;

  constructor(
    private readonly stockExchangeService: StockExchangeService,
    private readonly utilsService: UtilService,
    private readonly seo: SeoService,
    private readonly adStore: AdvertisementAdoceanStoreService,
    protected readonly cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.stockExchangeService.selectedStock$.next(null);
    this.setMetaData();
    this.initAds();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  initAds(): void {
    this.resetAds();
    this.adStore.advertisemenets$.pipe(takeUntil(this.unsubscribe$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStore.separateAdsByMedium(ads);
      this.cdr.detectChanges();
    });
  }

  resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('reszvenyek');
    canonical && this.seo.updateCanonicalUrl(canonical);
    const title = createVilaggazdasagTitle(`Részvények`);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'index, follow',
      description: 'BÉT részvény árfolyamok listája, utolsó ár, árfolyam változások, forgalmi ár, napi adatok. 15 perccel késleltetett adatok.',
      ogDescription: 'BÉT részvény árfolyamok listája, utolsó ár, árfolyam változások, forgalmi ár, napi adatok. 15 perccel késleltetett adatok.',
    };
    this.seo.setMetaData(metaData);
  }
}
