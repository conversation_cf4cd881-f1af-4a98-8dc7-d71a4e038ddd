<section>
  <div class="wrapper stock-list">
    <!-- Hagy<PERSON><PERSON><PERSON><PERSON>k -->
    <div class="stock-list-header">
      <div>
        <h1 class="stock-exchange-title">Részvények</h1>
        <div class="stock-exchange-subtitle">Forrás: Budapesti Értéktőzsde Nyrt.</div>
      </div>
      <app-stock-select *ngIf="listData$ | async as listData" [isSearchStyle]="true" [stockOptions]="listData"></app-stock-select>
    </div>
    <div class="stock-list-table">
      <ng-container *ngTemplateOutlet="stockListTable; context: { stockListData: listData$ }"></ng-container>
    </div>

    <kesma-advertisement-adocean
      *ngIf="adverts?.desktop?.roadblock_1 as ad"
      [style]="{
        margin: 'var(--ad-margin)',
        background: 'var(--ad-bg-light)',
        padding: 'var(--ad-padding)',
      }"
      [ad]="ad"
    >
    </kesma-advertisement-adocean>

    <kesma-advertisement-adocean
      *ngIf="adverts?.mobile?.mobilrectangle_1 as ad"
      [style]="{
        margin: 'var(--ad-margin)',
        background: 'var(--ad-bg-light)',
        padding: 'var(--ad-padding)',
      }"
      [ad]="ad"
    >
    </kesma-advertisement-adocean>

    <!-- Xtend részvények -->
    <div class="stock-list-header xtend">
      <div>
        <h1 class="stock-exchange-title">Xtend részvények</h1>
      </div>
      <app-stock-select *ngIf="xtendListData$ | async as xtendListData" [isSearchStyle]="true" [stockOptions]="xtendListData"></app-stock-select>
    </div>

    <kesma-advertisement-adocean
      *ngIf="adverts?.desktop?.roadblock_2 as ad"
      [style]="{
        margin: 'var(--ad-margin)',
        background: 'var(--ad-bg-light)',
        padding: 'var(--ad-padding)',
      }"
      [ad]="ad"
    >
    </kesma-advertisement-adocean>
    <kesma-advertisement-adocean
      *ngIf="adverts?.mobile?.mobilrectangle_2 as ad"
      [style]="{
        margin: 'var(--ad-margin)',
        background: 'var(--ad-bg-light)',
        padding: 'var(--ad-padding)',
      }"
      [ad]="ad"
    >
    </kesma-advertisement-adocean>
    <div class="stock-list-table">
      <ng-container *ngTemplateOutlet="stockListTable; context: { stockListData: xtendListData$ }"></ng-container>
    </div>

    <div class="stock-list-link">
      <a routerLink="/reszvenyek/indexek">
        <span>Részvényindexek megtekintése</span>
        <kesma-icon [size]="24" name="vg-chevron-diagonal-right"></kesma-icon>
      </a>
    </div>
  </div>

  <kesma-advertisement-adocean
    *ngIf="adverts?.desktop?.roadblock_3 as ad"
    [style]="{
      margin: 'var(--ad-margin)',
      background: 'var(--ad-bg-light)',
      padding: 'var(--ad-padding)',
      width: '100%',
    }"
    [ad]="ad"
  >
  </kesma-advertisement-adocean>
  <kesma-advertisement-adocean
    *ngIf="adverts?.mobile?.mobilrectangle_3 as ad"
    [style]="{
      margin: 'var(--ad-margin)',
      background: 'var(--ad-bg-light)',
      padding: 'var(--ad-padding)',
    }"
    [ad]="ad"
  >
  </kesma-advertisement-adocean>
</section>

<ng-template #noData>N.A.</ng-template>

<ng-template #loading>
  <vg-spinner [height]="50" [width]="50"></vg-spinner>
</ng-template>

<ng-template #stockListTable let-stockListData="stockListData">
  <table *ngIf="stockListData | async as listData; else loading">
    <thead>
      <tr>
        <th>Név</th>
        <th>Idő</th>
        <th>Utolsó ár</th>
        <th>Deviza</th>
        <th>Változás<br />árfolyam</th>
        <th>Változás<br />%</th>
        <th>Forgalom<br />érték</th>
        <th>Forgalom<br />db</th>
        <th>Vétel<br />ár</th>
        <th>Vétel<br />db</th>
        <th>Eladás<br />ár</th>
        <th>Eladás<br />db</th>
        <th>Nyitó ár</th>
        <th>Napi<br />min.</th>
        <th>Napi<br />max.</th>
        <th>Bázisár</th>
      </tr>
    </thead>
    <tbody>
      <tr
        *ngFor="let data of $any(listData)"
        [ngClass]="{
          up: data.changePercentage && data.changePercentage > 0,
          down: data.changePercentage && data.changePercentage < 0,
        }"
        [routerLink]="['/reszvenyek', data.name | lowercase]"
      >
        <td>
          <a [routerLink]="['/reszvenyek', data.name | lowercase]">{{ data.name }}</a>
        </td>
        <td>{{ formatStockDate(data.date) }}</td>
        <td>{{ (data.lastPrice ? data.lastPrice : data.closingPrice) | number: '1.0-4' }}</td>
        <td>{{ data.currency }}</td>
        <td [ngClass]="{ na: data.changeDiffLastPrice === null }" class="color">
          <ng-container *ngIf="data.changeDiffLastPrice !== null; else noData">
            {{ data.changeDiffLastPrice > 0 ? '+' : '' }}{{ data.changeDiffLastPrice | number: '1.0-4' }}
          </ng-container>
        </td>
        <td [ngClass]="{ na: data.changePercentage === null }" class="color">
          <ng-container *ngIf="data.changePercentage !== null; else noData">
            {{ data.changePercentage > 0 ? '+' : '' }}{{ data.changePercentage | number: '1.0-2' }}
          </ng-container>
        </td>
        <td [ngClass]="{ na: data.accumulatedTurnover === null }">
          {{ data.accumulatedTurnover !== null ? (data.accumulatedTurnover | number: '1.0-4') : 'N.A.' }}
        </td>
        <td [ngClass]="{ na: data.accumulatedVolume === null }">
          {{ data.accumulatedVolume !== null ? (data.accumulatedVolume | number: '1.0-0') : 'N.A.' }}
        </td>
        <td [ngClass]="{ na: data.bidPriceAtLevel === null }">
          {{ data.bidPriceAtLevel !== null ? (data.bidPriceAtLevel | number: '1.0-4') : 'N.A.' }}
        </td>
        <td [ngClass]="{ na: data.bidVolumeAtLevel === null }">
          {{ data.bidVolumeAtLevel !== null ? (data.bidVolumeAtLevel | number: '1.0-0') : 'N.A.' }}
        </td>
        <td [ngClass]="{ na: data.askPriceAtLevel === null }">
          {{ data.askPriceAtLevel !== null ? (data.askPriceAtLevel | number: '1.0-4') : 'N.A.' }}
        </td>
        <td [ngClass]="{ na: data.askVolumeAtLevel === null }">
          {{ data.askVolumeAtLevel !== null ? (data.askVolumeAtLevel | number: '1.0-0') : 'N.A.' }}
        </td>
        <td [ngClass]="{ na: data.firstPrice === null }">
          {{ data.firstPrice !== null ? (data.firstPrice | number: '1.0-4') : 'N.A.' }}
        </td>
        <td [ngClass]="{ na: data.lowPrice === null }">{{ data.lowPrice !== null ? (data.lowPrice | number: '1.0-4') : 'N.A.' }}</td>
        <td [ngClass]="{ na: data.highPrice === null }">
          {{ data.highPrice !== null ? (data.highPrice | number: '1.0-4') : 'N.A.' }}
        </td>
        <td [ngClass]="{ na: data.closingPrice === null }">
          {{ data.closingPrice !== null ? (data.closingPrice | number: '1.0-4') : 'N.A.' }}
        </td>
      </tr>
    </tbody>
  </table>
</ng-template>
