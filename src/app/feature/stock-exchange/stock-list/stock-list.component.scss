@use 'shared' as *;
@use 'stock-list-table.component' as *;

.stock-list {
  margin-top: 40px;

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    padding-bottom: 32px;

    &.xtend {
      margin-top: 32px;
    }

    @include media-breakpoint-down(sm) {
      flex-direction: column;
      align-items: flex-start;
      padding-bottom: 24px;
    }
  }

  &-link {
    padding: 32px 0 16px;

    a {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      border-bottom: 1px solid var(--kui-gray-950);
      padding: 0 0 8px;
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
      transition: 0.3s;

      @media (hover: hover) {
        &:hover {
          color: var(--kui-amethyst-500);
          border-bottom: 1px solid var(--kui-amethyst-500);
        }
      }
    }
  }
}
