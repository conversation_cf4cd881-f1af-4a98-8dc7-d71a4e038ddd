@use 'shared' as *;

.stock-list-table {
  overflow-y: auto;

  table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;

    th,
    td {
      border-radius: 2px;
      padding: 8px;
      font-size: 12px;
      line-height: 14px;
    }

    th {
      border: 1px solid var(--kui-deep-teal-200);
      background: var(--kui-deep-teal-50);
      padding: 5px 8px;
      font-weight: 400;
      text-align: center;
    }

    td {
      background-color: var(--kui-gray-50);
      border: 1px solid var(--kui-white);
      padding: 10px 8px;
      font-weight: 700;
      cursor: pointer;
      transition: 0.3s;
      text-align: right;

      &:first-child {
        text-align: left;
      }

      &.no-data {
        text-align: center;
        cursor: default;
      }

      &.na {
        color: var(--kui-gray-500);
      }
    }

    tr {
      &.up td.color {
        color: var(--kui-green-500);
      }

      &.down td.color {
        color: var(--kui-red-500);
      }

      &:nth-child(even) {
        td {
          background-color: var(--kui-gray-100);
        }
      }

      @media (hover: hover) {
        &:hover {
          td:not(.no-data) {
            background-color: var(--kui-deep-teal-200);
          }
        }
      }
    }
  }
}
