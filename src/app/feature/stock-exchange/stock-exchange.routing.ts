import { Routes } from '@angular/router';
import { StockExchangeComponent } from './stock-exchange.component';
import { StockListComponent } from './stock-list/stock-list.component';
import { StockDetailComponent } from './stock-detail/stock-detail.component';
import { StockIndexComponent } from './stock-index/stock-index.component';
import { StockDetailResolver } from './stock-detail/stock-detail.resolver';
import { StockIndexResolver } from './stock-index/stock-index.resolver';

export const STOCK_EXCHANGE_ROUTING: Routes = [
  {
    path: '',
    component: StockExchangeComponent,
    children: [
      {
        path: '',
        component: StockListComponent,
      },
      {
        path: 'indexek',
        component: StockIndexComponent,
        runGuardsAndResolvers: 'paramsOrQueryParamsChange',
        providers: [StockIndexResolver],
        resolve: { data: StockIndexResolver },
      },
      {
        path: 'indexek/:stockIndex',
        component: StockIndexComponent,
        runGuardsAndResolvers: 'paramsOrQueryParamsChange',
        providers: [StockIndexResolver],
        resolve: { data: StockIndexResolver },
      },
      {
        path: ':stock',
        component: StockDetailComponent,
        runGuardsAndResolvers: 'paramsOrQueryParamsChange',
        providers: [StockDetailResolver],
        resolve: { data: StockDetailResolver },
      },
    ],
  },
];
