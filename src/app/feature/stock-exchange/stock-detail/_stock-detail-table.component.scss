@use 'shared' as *;

.stock-detail-table {
  table {
    width: 100%;

    th {
      font-size: 18px;
      font-weight: 700;
      line-height: normal;
      letter-spacing: 0.36px;
      color: var(--kui-deep-teal-900);
      padding-bottom: 8px;
    }

    td {
      border-bottom: 1px solid var(--kui-gray-700);
      background: var(--kui-gray-900);
      color: var(--kui-white);
      text-align: left;
      font-size: 14px;
      font-weight: 400;
      line-height: 18px;
      vertical-align: middle;
      padding: 8px 12px;

      span {
        color: var(--kui-gray-500);
        padding: 0 6px;
      }

      @include media-breakpoint-down(md) {
        min-width: 60px;
      }

      &:last-child {
        text-align: right;
        font-weight: 700;
      }
    }

    tr {
      &:nth-child(even) {
        td {
          background-color: var(--kui-gray-800);
        }
      }

      &:last-child {
        td {
          border-bottom: none;
        }
      }
    }
  }
}
