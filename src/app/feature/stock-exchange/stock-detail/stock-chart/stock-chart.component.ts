import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, input, On<PERSON><PERSON><PERSON>, ViewChild } from '@angular/core';
import { BehaviorSubject, combineLatest, forkJoin, of, Subject } from 'rxjs';
import { format } from 'date-fns';
import { createChart, CustomData, IChartApi, IPriceLine, ISeriesApi, MouseEventParams, TickMarkType, UTCTimestamp } from 'lightweight-charts';
import { AsyncPipe, DecimalPipe, NgClass, NgIf } from '@angular/common';
import {
  convertUTCTimestampToDate,
  financialAPIStockChartDataMapper,
  FinancialAPIStockGraphPoint,
  getMinMaxPrice,
  isStockDailyDataAvailable,
  stockChartDefaultConfig,
  stockChartSeriesConfig,
  StockChartType,
  StockExchangeService,
  VgSpinnerComponent,
} from '../../../../shared';
import { distinctUntilChanged, switchMap, takeUntil, tap } from 'rxjs/operators';
import { UtilService } from '@trendency/kesma-core';
import { toObservable } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-stock-chart',
  templateUrl: './stock-chart.component.html',
  styleUrls: ['./stock-chart.component.scss'],
  providers: [DecimalPipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgClass, AsyncPipe, VgSpinnerComponent],
})
export class StockChartComponent implements AfterViewInit, OnDestroy {
  selectedStock = input<string | null>(null);
  selectedStock$ = toObservable(this.selectedStock);

  chartType$: BehaviorSubject<StockChartType> = new BehaviorSubject<StockChartType>(StockChartType.DAY);

  chart: IChartApi;
  dataSeries: ISeriesApi<'Area'>;
  isChartLoaded = false;
  isChartDataLoading = false;
  isChartEmpty = false;

  StockChartType = StockChartType;

  unsubscribe$: Subject<void> = new Subject<void>();

  @ViewChild('chartContainer', { static: false }) chartContainer: ElementRef<HTMLDivElement>;
  @ViewChild('chartTooltip', { static: false }) chartTooltip: ElementRef<HTMLDivElement>;

  constructor(
    private readonly stockExchangeService: StockExchangeService,
    private readonly decimalPipe: DecimalPipe,
    private readonly cdr: ChangeDetectorRef,
    private readonly utilsService: UtilService
  ) {}

  ngAfterViewInit(): void {
    if (this.utilsService.isBrowser()) {
      this.handleChartData();
    }
  }

  initChart(): void {
    this.chart = createChart(this.chartContainer.nativeElement, stockChartDefaultConfig);
    this.dataSeries = this.chart.addAreaSeries(stockChartSeriesConfig);
  }

  handleChartData(): void {
    let closingPriceLine: IPriceLine | null = null;

    combineLatest([this.chartType$.pipe(distinctUntilChanged()), this.selectedStock$.pipe(distinctUntilChanged()), this.stockExchangeService.autoRefresh$])
      .pipe(
        tap(() => {
          this.isChartDataLoading = true;
        }),
        switchMap(([chartType, stock]) =>
          forkJoin([
            of(stock),
            isStockDailyDataAvailable() || chartType !== StockChartType.DAY ? this.stockExchangeService.getStockGraphData(stock ?? '', chartType) : of(null),
          ])
        ),
        takeUntil(this.unsubscribe$)
      )
      .subscribe(([stock, chartData]) => {
        if (!stock) {
          return;
        }

        if (!this.chart) {
          this.initChart();
          this.handleChartTooltip();
          this.isChartLoaded = true;
        }

        this.dataSeries.setData(financialAPIStockChartDataMapper(chartData?.data?.points ?? [], this.chartType$.value, stock));

        const minMaxPrice: {
          min: number;
          max: number;
        } = getMinMaxPrice(
          (chartData?.data?.points ?? []).map((p) => p.lastPrice),
          chartData?.data?.closingPrice ?? 0
        );
        this.dataSeries.applyOptions({
          autoscaleInfoProvider: () => ({
            priceRange: {
              minValue: minMaxPrice.min,
              maxValue: minMaxPrice.max,
            },
          }),
        });

        if (closingPriceLine) {
          this.dataSeries.removePriceLine(closingPriceLine);
          closingPriceLine = null;
        }
        if (this.chartType$.value === StockChartType.DAY && chartData?.data?.closingPrice) {
          closingPriceLine = this.dataSeries.createPriceLine({
            price: chartData?.data?.closingPrice,
            color: '#2dd4b7',
            axisLabelTextColor: '#fff',
            title: 'Előző záróár',
          });
        }

        this.applyChartTimeFormats();
        this.chart.timeScale().fitContent();

        if (this.utilsService.isBrowser()) {
          setTimeout(() => {
            this.isChartDataLoading = false;
            this.cdr.detectChanges();
          }, 300);
        } else {
          this.isChartDataLoading = false;
        }

        this.isChartEmpty = (chartData?.data?.points?.filter((p) => !!p.lastPrice)?.length ?? 0) === 0;
        this.cdr.detectChanges();
      });
  }

  handleChartTooltip(): void {
    this.chart.subscribeCrosshairMove((param: MouseEventParams) => {
      const tooltip: HTMLDivElement = this.chartTooltip.nativeElement;

      if (param.point === undefined || !param.time || param.point.x < 0 || param.point.y < 0) {
        tooltip.style.display = 'none';
      } else {
        const pointDate: Date = convertUTCTimestampToDate(param.time as UTCTimestamp);
        const data: CustomData | undefined = param.seriesData.get(this.dataSeries);

        // Display tooltip only if custom values are available
        if (data?.customValues) {
          const aggregatedData: FinancialAPIStockGraphPoint & {
            stock: string;
            graphType: StockChartType;
          } = data.customValues as any;

          const dateStr: string = this.getFormattedDate(pointDate, aggregatedData.graphType);

          tooltip.style.display = 'block';
          tooltip.innerHTML = `
            <div>Idő: <strong>${dateStr}</strong></div>
            <div>Árfolyam: <strong>${this.decimalPipe.transform(aggregatedData.lastPrice ?? 0, '1.0-4')}</strong></div>
          `;

          this.appendVolumeInfo(tooltip, aggregatedData);

          // Position tooltip according to mouse cursor position
          tooltip.style.left = param.point.x + 'px';
          tooltip.style.top = param.point.y + 'px';
        }
      }
    });
  }

  private getFormattedDate(date: Date, type: StockChartType): string {
    if (type === StockChartType.DAY) return format(date, 'HH:mm:ss');
    if (type === StockChartType.WEEK) return format(date, 'yyyy.MM.dd. HH:mm');
    return format(date, 'yyyy.MM.dd.');
  }

  private appendVolumeInfo(tooltip: HTMLElement, data: FinancialAPIStockGraphPoint): void {
    if (data.accumulatedVolume) {
      tooltip.innerHTML += `<div>Kumulált kötések: <strong>${this.decimalPipe.transform(data.accumulatedVolume, '1.0-0')} db</strong></div>`;
    }
    if (data.numberOfSharesInTrade) {
      tooltip.innerHTML += `<div>Kötések száma: <strong>${this.decimalPipe.transform(data.numberOfSharesInTrade, '1.0-0')} db</strong></div>`;
    }
  }

  applyChartTimeFormats(): void {
    this.chart.applyOptions({
      localization: {
        // Format datetime labels on cross-hair hover (x axis hover label)
        timeFormatter: (timestamp: UTCTimestamp) => {
          const date: Date = convertUTCTimestampToDate(timestamp);

          switch (this.chartType$.value) {
            case StockChartType.DAY:
            case StockChartType.WEEK:
              return format(date, 'MM.dd. HH:mm');
            case StockChartType.MONTH:
              return format(date, 'MM.dd.');
            case StockChartType.YEAR:
            case StockChartType.THREE_YEARS:
            case StockChartType.ALL:
              return format(date, 'yyyy.MM.dd.');
            default:
              return '';
          }
        },
        // Format price with angular number formatter (decimal precision 2-4)
        priceFormatter: (priceValue: number): string => {
          return this.decimalPipe.transform(priceValue, '1.2-4') ?? '';
        },
      },
      timeScale: {
        // Format datetime labels on x axis
        tickMarkFormatter: (timestamp: UTCTimestamp, tickMarkType: TickMarkType) => {
          const date: Date = convertUTCTimestampToDate(timestamp);

          switch (this.chartType$.value) {
            case StockChartType.DAY:
              return format(date, 'HH:mm');
            case StockChartType.WEEK:
            case StockChartType.MONTH:
              if (tickMarkType === TickMarkType.Time) {
                return '';
              }
              return format(date, 'MM.dd.');
            case StockChartType.YEAR:
            case StockChartType.THREE_YEARS:
            case StockChartType.ALL:
              return format(date, 'yyyy.MM.');
            default:
              return '';
          }
        },
      },
    });
  }

  changeChartType(type: StockChartType): void {
    this.chartType$.next(type);
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
