@use 'shared' as *;

:host {
  display: block;
}

.stock-chart {
  &-types {
    display: flex;
    align-items: center;
    gap: 24px;
    margin-top: 32px;

    @include media-breakpoint-down(sm) {
      overflow-y: auto;
      width: 100%;
      gap: 16px;
    }
  }

  &-type {
    border-radius: 2px;
    border: 1px solid var(--kui-deep-teal-400);
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: 700;
    line-height: 18px;
    color: var(--kui-gray-950);
    width: 100%;
    cursor: pointer;
    transition:
      background-color 0.3s,
      color 0.3s;

    @include media-breakpoint-down(sm) {
      height: 32px;
      min-width: 70px;
      font-size: 12px;
      line-height: 14px;
    }

    &.active {
      background-color: var(--kui-deep-teal-400);
      color: var(--kui-white);
    }

    @media (hover: hover) {
      &:hover {
        background-color: var(--kui-deep-teal-400);
        color: var(--kui-white);
      }
    }
  }

  &-wrapper {
    padding-top: 16px;
    position: relative;
  }

  &-chart {
    // Margin hack for chart
    margin-left: -20px;
    width: calc(100% + 20px);
  }

  &-no-data {
    position: absolute;
    width: 100%;
    height: calc(100% - 16px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 3;
    color: var(--kui-deep-teal-400);
    background-color: var(--kui-white);
    border: 1px solid var(--kui-gray-200);
    font-size: 36px;
    line-height: 40px;
    font-weight: 700;
    border-radius: 2px;
  }

  &-tooltip {
    border-radius: 2px;
    background: var(--kui-deep-teal-950-o65);
    padding: 10px 8px;
    position: absolute;
    display: none;
    font-family: var(--kui-font-primary);
    font-size: 10px;
    line-height: 15px;
    font-weight: 400;
    text-align: left;
    z-index: 1000;
    color: var(--kui-white);
    pointer-events: none; // Fix flickering
  }

  &-data-loader {
    position: absolute;
    top: 21px;
    right: 5px;
    z-index: 1001;
    border: 1px solid var(--kui-gray-200);
    background: var(--kui-white);
    border-radius: 50%;
    padding: 10px;
  }
}
