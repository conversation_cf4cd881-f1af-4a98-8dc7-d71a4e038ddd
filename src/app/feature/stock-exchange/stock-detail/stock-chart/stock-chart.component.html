<div class="stock-chart">
  <div *ngIf="chartType$ | async as chartType" class="stock-chart-types">
    <div (click)="changeChartType(StockChartType.DAY)" [ngClass]="{ active: chartType === StockChartType.DAY }" class="stock-chart-type">Napi</div>
    <div (click)="changeChartType(StockChartType.WEEK)" [ngClass]="{ active: chartType === StockChartType.WEEK }" class="stock-chart-type"><PERSON><PERSON></div>
    <div (click)="changeChartType(StockChartType.MONTH)" [ngClass]="{ active: chartType === StockChartType.MONTH }" class="stock-chart-type">Havi</div>
    <div (click)="changeChartType(StockChartType.YEAR)" [ngClass]="{ active: chartType === StockChartType.YEAR }" class="stock-chart-type">1 év</div>
    <div (click)="changeChartType(StockChartType.THREE_YEARS)" [ngClass]="{ active: chartType === StockChartType.THREE_YEARS }" class="stock-chart-type">
      3 év
    </div>
    <div (click)="changeChartType(StockChartType.ALL)" [ngClass]="{ active: chartType === StockChartType.ALL }" class="stock-chart-type">Mind</div>
  </div>
  <div class="stock-chart-wrapper">
    <div *ngIf="isChartLoaded && isChartDataLoading" class="stock-chart-data-loader">
      <vg-spinner [height]="30" [width]="30"></vg-spinner>
    </div>
    <div *ngIf="!isChartLoaded">
      <vg-spinner [height]="50" [width]="50"></vg-spinner>
    </div>
    <div *ngIf="isChartEmpty" class="stock-chart-no-data">Nincs adat</div>
    <div #chartContainer class="stock-chart-chart"></div>
    <div #chartTooltip class="stock-chart-tooltip"></div>
  </div>
</div>
