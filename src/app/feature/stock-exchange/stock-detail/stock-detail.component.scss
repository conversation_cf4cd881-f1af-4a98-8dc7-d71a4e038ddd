@use 'shared' as *;

.stock-detail {
  .left-column {
    @include media-breakpoint-down(lg) {
      width: 100%;
    }
  }

  aside {
    @include media-breakpoint-down(lg) {
      width: 100%;
    }
  }

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;
    padding-bottom: 24px;

    @include media-breakpoint-down(sm) {
      flex-direction: column;
      align-items: flex-start;
    }
  }

  h2 {
    color: var(--kui-deep-teal-900);
    font-size: 18px;
    line-height: normal;
    font-weight: 700;
    letter-spacing: 0.36px;
    margin-bottom: 8px;
  }

  &-data {
    border-radius: 2px;
    border: 1px solid var(--kui-gray-800);
    background-color: var(--kui-gray-900);
    color: var(--kui-white);
    padding: 10px 16px;
    font-size: 14px;
    line-height: 18px;
    display: flex;
    align-items: center;
    gap: 16px;
    justify-content: space-between;

    @include media-breakpoint-down(md) {
      flex-direction: column;
      align-items: flex-start;
    }

    &-group {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    &-name {
      font-weight: 700;
    }

    .color {
      color: var(--kui-deep-teal-400);
      font-weight: 700;

      &.up {
        color: var(--kui-green-500);
      }

      &.down {
        color: var(--kui-red-500);
      }
    }
  }

  &-tables {
    margin: 32px 0;
    display: flex;
    gap: 32px;
    flex-direction: column;

    @include media-breakpoint-down(sm) {
      gap: 32px;
      margin: 32px 0;
    }

    &-group {
      display: flex;
      gap: 24px;

      @include media-breakpoint-down(sm) {
        flex-direction: column;
        gap: 24px;
      }

      > * {
        flex: 1;
      }
    }
  }

  &-link {
    @include media-breakpoint-down(sm) {
      display: inline-flex;
      flex-direction: column;
      align-items: flex-start;
    }

    a {
      display: inline-flex;
      align-items: center;
      gap: 8px;
      border-bottom: 1px solid var(--kui-gray-950);
      padding: 0 0 8px;
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
      transition: 0.3s;

      @media (hover: hover) {
        &:hover {
          color: var(--kui-amethyst-500);
          border-bottom: 1px solid var(--kui-amethyst-500);
        }
      }

      + a {
        margin-top: 32px;
      }
    }
  }
}
