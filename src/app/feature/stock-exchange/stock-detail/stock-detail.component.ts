import { ChangeDetectionStrategy, Component, OnDestroy, OnInit } from '@angular/core';
import { combineLatest, distinctUntilChanged, Observable, shareReplay, Subject, takeUntil } from 'rxjs';
import { filter, map, switchMap } from 'rxjs/operators';
import {
  createVilaggazdasagTitle,
  defaultMetaInfo,
  FinancialAPIStockDetailData,
  FinancialAPIStockDetailResult,
  FinancialAPIStockInfoData,
  FinancialAPIStockInfoResult,
  FinancialAPIStockStatisticsData,
  FinancialAPIStockStatisticsResult,
  formatStockDate,
  isStockDailyDataAvailable,
  NewsletterBoxType,
  StockExchangeService,
  VgNewsletterBoxComponent,
  VgSpinnerComponent,
} from '../../../shared';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { createCanonicalUrlForPageablePage, IconComponent, PAGE_TYPES } from '@trendency/kesma-ui';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { StockInfoComponent } from './stock-info/stock-info.component';
import { StockStatisticsComponent } from './stock-statistics/stock-statistics.component';
import { StockChartComponent } from './stock-chart/stock-chart.component';
import { AsyncPipe, DecimalPipe, LowerCasePipe, NgClass, NgIf } from '@angular/common';
import { StockSelectComponent } from '../stock-select/stock-select.component';

@Component({
  selector: 'app-stock-detail',
  templateUrl: './stock-detail.component.html',
  styleUrls: ['./stock-detail.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    StockSelectComponent,
    NgIf,
    NgClass,
    StockChartComponent,
    StockStatisticsComponent,
    StockInfoComponent,
    RouterLink,
    SidebarComponent,
    AsyncPipe,
    LowerCasePipe,
    DecimalPipe,
    VgSpinnerComponent,
    VgNewsletterBoxComponent,
    IconComponent,
  ],
})
export class StockDetailComponent implements OnInit, OnDestroy {
  stockInfo$: Observable<FinancialAPIStockInfoData> = combineLatest([
    this.stockExchangeService.selectedStock$.pipe(distinctUntilChanged()),
    this.stockExchangeService.autoRefresh$,
  ]).pipe(
    filter(([stock]) => !!stock),
    switchMap(([stock]) => this.stockExchangeService.getStockInfo(stock ?? '').pipe(map((result: FinancialAPIStockInfoResult) => result.data)))
  );

  stockDetail$: Observable<FinancialAPIStockDetailData> = combineLatest([
    this.stockExchangeService.selectedStock$.pipe(distinctUntilChanged()),
    this.stockExchangeService.autoRefresh$,
  ]).pipe(
    filter(([stock]) => !!stock),
    switchMap(([stock]) =>
      this.stockExchangeService.getStockDetailData(stock ?? '').pipe(
        map((result: FinancialAPIStockDetailResult) => ({
          ...result.data,
          ...(isStockDailyDataAvailable()
            ? {}
            : {
                lastPrice: null,
                changeDiffLastPrice: null,
                changePercentage: null,
                accumulatedTurnover: null,
              }),
        }))
      )
    )
  );

  stockStatistics$: Observable<FinancialAPIStockStatisticsData> = combineLatest([
    this.stockExchangeService.selectedStock$.pipe(distinctUntilChanged()),
    this.stockExchangeService.autoRefresh$,
  ]).pipe(
    filter(([stock]) => !!stock),
    switchMap(([stock]) =>
      this.stockExchangeService.getStockStatistics(stock ?? '').pipe(
        map((result: FinancialAPIStockStatisticsResult) => ({
          ...result.data,
          ...(isStockDailyDataAvailable()
            ? {}
            : {
                changeDiffLastPrice: null,
                changePercentage: null,
                accumulatedTurnover: null,
                accumulatedVolume: null,
                bidPriceAtLevel: null,
                bidVolumeAtLevel: null,
                askPriceAtLevel: null,
                askVolumeAtLevel: null,
                firstPrice: null,
                lowPrice: null,
                highPrice: null,
                averagePrice: null,
                numberOfTrades: null,
              }),
        }))
      )
    ),
    shareReplay({
      bufferSize: 1,
      refCount: true,
    })
  );

  unsubscribe$: Subject<void> = new Subject<void>();

  adPageType: string = PAGE_TYPES.all_articles_and_sub_pages;

  formatStockDate = formatStockDate;

  NewsletterBoxType = NewsletterBoxType;

  constructor(
    private readonly stockExchangeService: StockExchangeService,
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService
  ) {}

  get selectedStock(): string | null {
    return this.stockExchangeService.selectedStock;
  }

  ngOnInit(): void {
    this.route.params.pipe(takeUntil(this.unsubscribe$)).subscribe(() => {
      this.setMetaData();
    });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage(`reszvenyek/${this.selectedStock?.toLowerCase()}`);
    canonical && this.seo.updateCanonicalUrl(canonical);
    const title = createVilaggazdasagTitle(`${this.selectedStock} részvény`);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'index, follow',
      description:
        `${this.selectedStock?.toUpperCase()} részvény kereskedési adatok, utolsó ár, árfolyam változások, statisztikák, ` +
        `grafikonok. 15 perccel késleltetett adatok.`,
      ogDescription:
        `${this.selectedStock?.toUpperCase()} részvény kereskedési adatok, utolsó ár, árfolyam változások, statisztikák, ` +
        `grafikonok. 15 perccel késleltetett adatok.`,
    };
    this.seo.setMetaData(metaData);
  }
}
