import { ChangeDetectionStrategy, Component, effect, inject, input, signal } from '@angular/core';
import { DecimalPipe, LowerCasePipe, NgClass } from '@angular/common';
import {
  FinancialAPIStockDetailData,
  FinancialAPIStockDetailResult,
  FinancialAPIStockStatisticsData,
  FinancialAPIStockStatisticsResult,
  formatStockDate,
  isStockDailyDataAvailable,
  StockExchangeService,
  VgSpinnerComponent,
} from '../../../../shared';
import { IconComponent } from '@trendency/kesma-ui';
import { StockChartComponent } from '../stock-chart/stock-chart.component';
import { map } from 'rxjs/operators';
import { toSignal } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-stock-detail-article',
  templateUrl: './stock-detail-article.component.html',
  styleUrls: ['./stock-detail-article.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [LowerCasePipe, DecimalPipe, NgClass, IconComponent, StockChartComponent, VgSpinnerComponent],
})
export class StockDetailArticleComponent {
  readonly #stockExchangeService = inject(StockExchangeService);

  selectedStock = input<string | null>(null);
  autoRefresh = toSignal(this.#stockExchangeService.autoRefresh$);

  stockDetail = signal<FinancialAPIStockDetailData | undefined>(undefined);
  stockStatistics = signal<FinancialAPIStockStatisticsData | undefined>(undefined);

  readonly formatStockDate = formatStockDate;

  constructor() {
    effect(() => {
      const stock = this.selectedStock();
      const autoRefresh = this.autoRefresh();

      if (stock && autoRefresh !== undefined) {
        this.getStockDetail(stock);
        this.getStockStatistics(stock);
      }
    });
  }

  getStockDetail(stock: string): void {
    this.#stockExchangeService
      .getStockDetailData(stock)
      .pipe(
        map((result: FinancialAPIStockDetailResult) => ({
          ...result.data,
          ...(isStockDailyDataAvailable()
            ? {}
            : {
                lastPrice: null,
                changeDiffLastPrice: null,
                changePercentage: null,
                accumulatedTurnover: null,
              }),
        }))
      )
      .subscribe((stockDetail) => {
        this.stockDetail.set(stockDetail);
      });
  }

  getStockStatistics(stock: string): void {
    this.#stockExchangeService
      .getStockStatistics(stock)
      .pipe(
        map((result: FinancialAPIStockStatisticsResult) => ({
          ...result.data,
          ...(isStockDailyDataAvailable()
            ? {}
            : {
                changeDiffLastPrice: null,
                changePercentage: null,
                accumulatedTurnover: null,
                accumulatedVolume: null,
                bidPriceAtLevel: null,
                bidVolumeAtLevel: null,
                askPriceAtLevel: null,
                askVolumeAtLevel: null,
                firstPrice: null,
                lowPrice: null,
                highPrice: null,
                averagePrice: null,
                numberOfTrades: null,
              }),
        }))
      )
      .subscribe((stockStatistics) => {
        this.stockStatistics.set(stockStatistics);
      });
  }
}
