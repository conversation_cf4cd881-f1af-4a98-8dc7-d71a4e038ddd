@use 'shared' as *;

:host {
  display: block;
  margin-bottom: 30px;
}

.title {
  color: var(--kui-deep-teal-900);
  font-size: 24px;
  font-weight: 700;
  line-height: normal;
  letter-spacing: 0.96px;

  @include media-breakpoint-down(sm) {
    font-size: 18px;
    letter-spacing: 0.36px;
  }
}

.subtitle {
  color: var(--kui-gray-500);
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  margin-bottom: 30px;
}

.stock-detail {
  &-data {
    border-radius: 2px;
    border: 1px solid var(--kui-gray-800);
    background-color: var(--kui-gray-900);
    color: var(--kui-white);
    padding: 10px 16px;
    font-size: 14px;
    line-height: 18px;
    display: flex;
    align-items: center;
    gap: 16px;
    justify-content: space-between;

    @include media-breakpoint-down(md) {
      flex-direction: column;
      align-items: flex-start;
    }

    &-group {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    &-name {
      font-weight: 700;
    }

    .color {
      color: var(--kui-deep-teal-400);
      font-weight: 700;

      &.up {
        color: var(--kui-green-500);
      }

      &.down {
        color: var(--kui-red-500);
      }
    }
  }
}
