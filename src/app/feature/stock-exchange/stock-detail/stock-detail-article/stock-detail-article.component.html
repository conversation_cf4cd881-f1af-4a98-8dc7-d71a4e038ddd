@if (selectedStock(); as selectedStock) {
  <div class="title">{{ selectedStock }} részvény</div>
  <div class="subtitle">Forrás: Budapesti Értéktőzsde Nyrt.</div>

  @if ({ stockData: stockDetail(), statData: stockStatistics() }; as data) {
    @if (data.stockData && data.statData) {
      <div class="stock-detail-data">
        <div class="stock-detail-data-group">
          <span class="stock-detail-data-name">{{ selectedStock }} részvény</span>
          @if (data.stockData?.date) {
            <span>{{ formatStockDate(data.stockData?.date) }}</span>
          }
        </div>
        @if (data.stockData?.lastPrice !== null || data.statData?.closingPrice !== null) {
          <div class="stock-detail-data-group">
            <span
              >{{ data.stockData?.lastPrice !== null ? 'Árfolyam' : 'Utolsó ár' }}:
              <strong>
                {{ (data.stockData?.lastPrice !== null ? data.stockData?.lastPrice : data.statData?.closingPrice) | number: '1.0-4' }}
                {{ data.statData?.currency }}
              </strong>
            </span>
            @if (data.stockData?.changeDiffLastPrice !== null && data.stockData?.changePercentage !== null) {
              <span [ngClass]="{ up: (data.stockData?.changePercentage ?? 0) > 0, down: (data.stockData?.changePercentage ?? 0) < 0 }" class="color">
                {{ (data.stockData?.changeDiffLastPrice ?? 0) > 0 ? '+' : '' }}{{ data.stockData?.changeDiffLastPrice | number: '1.0-4' }}
                /
                {{ (data.stockData?.changePercentage ?? 0) > 0 ? '+' : '' }}{{ data.stockData?.changePercentage | number: '1.0-2' }}
                %
              </span>
            }
            @if (data.stockData?.lastPrice !== null) {
              <kesma-icon
                [name]="'vg-exchange-' + (data.stockData?.changeDirection ?? 'no-data') | lowercase"
                [ngClass]="{ up: (data.stockData?.changePercentage ?? 0) > 0, down: (data.stockData?.changePercentage ?? 0) < 0 }"
                [size]="16"
                class="color"
              ></kesma-icon>
            }
          </div>
        }
        @if (data.stockData?.accumulatedTurnover !== null) {
          <div class="stock-detail-data-group">
            <span>
              Forgalom: <strong>{{ data.stockData?.accumulatedTurnover | number: '1.0-4' }} {{ data.statData?.currency }}</strong>
            </span>
          </div>
        }
      </div>
    } @else {
      <vg-spinner [height]="50" [width]="50"></vg-spinner>
    }
  }

  <app-stock-chart [selectedStock]="selectedStock"></app-stock-chart>
}
