<div *ngIf="statisticsData; else loading" class="stock-statistics">
  <div class="stock-statistics-table stock-detail-table">
    <table>
      <tbody>
        <tr>
          <td>Vételi ár</td>
          <td>{{ statisticsData.bidPriceAtLevel !== null ? (statisticsData.bidPriceAtLevel | number: '1.0-4') : 'N.A.' }}</td>
        </tr>
        <tr>
          <td>Vételi mennyiség</td>
          <td>{{ statisticsData.bidVolumeAtLevel !== null ? (statisticsData.bidVolumeAtLevel | number: '1.0-0') : 'N.A.' }}</td>
        </tr>
        <tr>
          <td>Eladási ár</td>
          <td>{{ statisticsData.askPriceAtLevel !== null ? (statisticsData.askPriceAtLevel | number: '1.0-4') : 'N.A.' }}</td>
        </tr>
        <tr>
          <td>Eladási mennyiség</td>
          <td>{{ statisticsData.askVolumeAtLevel !== null ? (statisticsData.askVolumeAtLevel | number: '1.0-0') : 'N.A.' }}</td>
        </tr>
        <tr>
          <td>Nyitó ár</td>
          <td>{{ statisticsData.firstPrice !== null ? (statisticsData.firstPrice | number: '1.0-4') : 'N.A.' }}</td>
        </tr>
        <tr>
          <td>Előző záróár</td>
          <td>{{ statisticsData.closingPrice | number: '1.0-4' }}</td>
        </tr>
        <tr>
          <td>Átlagár</td>
          <td>{{ statisticsData.averagePrice !== null ? (statisticsData.averagePrice | number: '1.0-4') : 'N.A.' }}</td>
        </tr>
        <tr>
          <td>180 napos átlagár</td>
          <td>{{ statisticsData.average180DaysPrice | number: '1.0-4' }}</td>
        </tr>
        <tr>
          <td>360 napos átlagár</td>
          <td>{{ statisticsData.average360DaysPrice | number: '1.0-4' }}</td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="stock-statistics-table stock-detail-table">
    <table>
      <tbody>
        <tr>
          <td>Napi minimum</td>
          <td>{{ statisticsData.lowPrice !== null ? (statisticsData.lowPrice | number: '1.0-4') : 'N.A.' }}</td>
        </tr>
        <tr>
          <td>Napi maximum</td>
          <td>{{ statisticsData.highPrice !== null ? (statisticsData.highPrice | number: '1.0-4') : 'N.A.' }}</td>
        </tr>
        <tr>
          <td>Éves min.<span>|</span>{{ formatDate(statisticsData.lowPriceYearDate) }}</td>
          <td>{{ statisticsData.lowPriceYear | number: '1.0-4' }}</td>
        </tr>
        <tr>
          <td>Éves max.<span>|</span>{{ formatDate(statisticsData.highPriceYearDate) }}</td>
          <td>{{ statisticsData.highPriceYear | number: '1.0-4' }}</td>
        </tr>
        <tr>
          <td>Történelmi min.<span>|</span>{{ formatDate(statisticsData.lowPriceLifeDate) }}</td>
          <td>{{ statisticsData.lowPriceLife | number: '1.0-4' }}</td>
        </tr>
        <tr>
          <td>Történelmi max.<span>|</span>{{ formatDate(statisticsData.highPriceLifeDate) }}</td>
          <td>{{ statisticsData.highPriceLife | number: '1.0-4' }}</td>
        </tr>
        <tr>
          <td>Napi forgalom (db)</td>
          <td>
            {{ statisticsData.accumulatedVolume !== null ? (statisticsData.accumulatedVolume | number: '1.0-0') : 'N.A.' }}
          </td>
        </tr>
        <tr>
          <td>Napi forgalom</td>
          <td>
            <ng-container *ngIf="statisticsData.accumulatedTurnover !== null; else noData"
              >{{ statisticsData.accumulatedTurnover | number: '1.0-4' }} {{ statisticsData.currency }}</ng-container
            >
          </td>
        </tr>
        <tr>
          <td>Kötések száma</td>
          <td>{{ statisticsData.numberOfTrades !== null ? (statisticsData.numberOfTrades | number: '1.0-0') : 'N.A.' }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<ng-template #loading>
  <vg-spinner [height]="50" [width]="50"></vg-spinner>
</ng-template>

<ng-template #noData>N.A.</ng-template>
