import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FinancialAPIStockStatisticsData, VgSpinnerComponent } from '../../../../shared';
import { format } from 'date-fns';
import { hu } from 'date-fns/locale';
import { DecimalPipe, NgIf } from '@angular/common';

@Component({
  selector: 'app-stock-statistics',
  templateUrl: './stock-statistics.component.html',
  styleUrls: ['./stock-statistics.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, DecimalPipe, VgSpinnerComponent],
})
export class StockStatisticsComponent {
  @Input() statisticsData: FinancialAPIStockStatisticsData | null;

  formatDate(isoDate: string | null): string {
    if (!isoDate) {
      return '';
    }

    return format(new Date(isoDate), 'yyyy.MM.dd', { locale: hu });
  }
}
