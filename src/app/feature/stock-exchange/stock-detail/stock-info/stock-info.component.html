<div *ngIf="infoData; else loading" class="stock-info">
  <div class="stock-info-table stock-detail-table">
    <table>
      <thead>
        <th colspan="2">Alapvető információk</th>
      </thead>
      <tbody>
        <tr>
          <td>Ticker</td>
          <td>{{ infoData.ticker }}</td>
        </tr>
        <tr>
          <td>ISIN</td>
          <td>{{ infoData.isinCode }}</td>
        </tr>
        <tr>
          <td>Bevezetés időpontja</td>
          <td>{{ formatDate(infoData.dateOfIntroduction) }}</td>
        </tr>
        <tr>
          <td>Kereskedés pénzneme</td>
          <td>{{ infoData.currency }}</td>
        </tr>
        <tr>
          <td>Névérték</td>
          <td>{{ infoData.nominalValue | number: '1.0-4' }} {{ infoData.currency }}</td>
        </tr>
        <tr>
          <td>Bevezetett mennyiség (db)</td>
          <td>{{ infoData.introducedQuantity | number: '1.0-0' }}</td>
        </tr>
        <tr>
          <td>Kapitalizáció (m Ft)</td>
          <td>{{ infoData.capitalization / 1000000 | number: '1.0-0' }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<ng-template #loading>
  <vg-spinner [height]="50" [width]="50"></vg-spinner>
</ng-template>
