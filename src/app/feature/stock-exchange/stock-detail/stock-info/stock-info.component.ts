import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FinancialAPIStockInfoData, VgSpinnerComponent } from '../../../../shared';
import { format } from 'date-fns';
import { hu } from 'date-fns/locale';
import { DecimalPipe, NgIf } from '@angular/common';

@Component({
  selector: 'app-stock-info',
  templateUrl: './stock-info.component.html',
  styleUrls: ['./stock-info.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, DecimalPipe, VgSpinnerComponent],
})
export class StockInfoComponent {
  @Input() infoData: FinancialAPIStockInfoData | null;

  formatDate(isoDate: string | null): string {
    if (!isoDate) {
      return '';
    }

    return format(new Date(isoDate), 'yyyy. MMM dd.', { locale: hu });
  }
}
