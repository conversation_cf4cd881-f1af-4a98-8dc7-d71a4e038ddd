<section>
  <div class="wrapper with-aside stock-detail">
    <div class="left-column">
      <div class="stock-detail-header">
        <div>
          <h1 class="stock-exchange-title">{{ selectedStock }} részvény</h1>
          <div class="stock-exchange-subtitle">Forrás: Budapesti Értéktőzsde Nyrt.</div>
        </div>
        <app-stock-select></app-stock-select>
      </div>
      <h2>Kereskedési adatok</h2>
      <ng-container *ngIf="{ stockData: stockDetail$ | async, statData: stockStatistics$ | async } as data">
        <ng-container *ngIf="data.stockData && data.statData; else loading">
          <div class="stock-detail-data">
            <div class="stock-detail-data-group">
              <span class="stock-detail-data-name">{{ selectedStock }} részvény</span>
              <span *ngIf="data.stockData?.date">{{ formatStockDate(data.stockData?.date) }}</span>
            </div>
            <div *ngIf="data.stockData?.lastPrice !== null || data.statData?.closingPrice !== null" class="stock-detail-data-group">
              <span
                >{{ data.stockData?.lastPrice !== null ? 'Árfolyam' : 'Utolsó ár' }}:
                <strong>
                  {{ (data.stockData?.lastPrice !== null ? data.stockData?.lastPrice : data.statData?.closingPrice) | number: '1.0-4' }}
                  {{ data.statData?.currency }}
                </strong>
              </span>
              <span
                *ngIf="data.stockData?.changeDiffLastPrice !== null && data.stockData?.changePercentage !== null"
                [ngClass]="{ up: (data.stockData?.changePercentage ?? 0) > 0, down: (data.stockData?.changePercentage ?? 0) < 0 }"
                class="color"
              >
                {{ (data.stockData?.changeDiffLastPrice ?? 0) > 0 ? '+' : '' }}{{ data.stockData?.changeDiffLastPrice | number: '1.0-4' }}
                /
                {{ (data.stockData?.changePercentage ?? 0) > 0 ? '+' : '' }}{{ data.stockData?.changePercentage | number: '1.0-2' }}
                %
              </span>
              <kesma-icon
                *ngIf="data.stockData?.lastPrice !== null"
                [name]="'vg-exchange-' + (data.stockData?.changeDirection ?? 'no-data') | lowercase"
                [ngClass]="{ up: (data.stockData?.changePercentage ?? 0) > 0, down: (data.stockData?.changePercentage ?? 0) < 0 }"
                [size]="16"
                class="color"
              ></kesma-icon>
            </div>
            <div *ngIf="data.stockData?.accumulatedTurnover !== null" class="stock-detail-data-group">
              <span>
                Forgalom: <strong>{{ data.stockData?.accumulatedTurnover | number: '1.0-4' }} {{ data.statData?.currency }}</strong>
              </span>
            </div>
          </div>
        </ng-container>
      </ng-container>
      <app-stock-chart [selectedStock]="selectedStock"></app-stock-chart>
      <div class="stock-detail-tables">
        <div class="stock-detail-tables-group">
          <app-stock-statistics [statisticsData]="stockStatistics$ | async"></app-stock-statistics>
        </div>
        <div class="stock-detail-tables-group">
          <app-stock-info [infoData]="stockInfo$ | async"></app-stock-info>
          <div class="stock-detail-link">
            <a routerLink="/reszvenyek">
              <span>Összes részvény megtekintése</span>
              <kesma-icon [size]="24" name="vg-chevron-diagonal-right"></kesma-icon>
            </a>
            <a routerLink="/reszvenyek/indexek">
              <span>Részvényindexek megtekintése</span>
              <kesma-icon [size]="24" name="vg-chevron-diagonal-right"></kesma-icon>
            </a>
          </div>
        </div>
      </div>
      <vg-newsletter-box [styleID]="NewsletterBoxType.SocialSorted"></vg-newsletter-box>
    </div>
    <aside>
      <app-sidebar [adPageType]="adPageType"></app-sidebar>
    </aside>
  </div>
</section>

<ng-template #loading>
  <vg-spinner [height]="50" [width]="50"></vg-spinner>
</ng-template>
