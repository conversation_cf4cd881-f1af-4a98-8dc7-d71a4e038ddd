import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { FinancialAPIStockInfoResult, StockExchangeService } from '../../../shared';

@Injectable()
export class StockDetailResolver {
  constructor(
    private readonly stockExchangeService: StockExchangeService,
    private readonly router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<FinancialAPIStockInfoResult> {
    const stock = route.params['stock']?.toUpperCase() ?? '';

    return this.stockExchangeService.getStockInfo(stock).pipe(
      tap((result: FinancialAPIStockInfoResult) => {
        if (!result?.data?.isActiveProduct) {
          this.router.navigate(['/404'], { skipLocationChange: true }).then();
        } else {
          this.stockExchangeService.selectedStock$.next(stock);
        }
      }),
      catchError((err) => {
        this.router.navigate(['/404'], { skipLocationChange: true }).then();
        return throwError(() => err);
      })
    );
  }
}
