@use 'shared' as *;

:host {
  display: block;
  max-width: 350px;
  min-width: 300px;

  @include media-breakpoint-down(sm) {
    max-width: 100%;
    width: 100%;
  }
}

.stock-select {
  margin: 0;

  &.search-style ::ng-deep {
    .ng-select-container .ng-arrow-wrapper {
      width: 24px;
      height: 24px;
      background-image: url('/assets/images/icons/vg-search-gray-950.svg');
    }
  }

  &.search-style.ng-select-opened ::ng-deep .ng-select-container .ng-arrow-wrapper {
    transform: none !important;
  }

  ::ng-deep .ng-placeholder {
    color: var(--kui-gray-950) !important;
    font-size: 16px;
    font-weight: 700;
    line-height: 22px;
  }

  ::ng-deep .ng-value {
    font-weight: 700 !important;
  }
}
