import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { distinctUntilChanged, forkJoin, Subject } from 'rxjs';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup } from '@angular/forms';
import { FinancialAPIStockListData, StockExchangeService } from '../../../shared';
import { takeUntil } from 'rxjs/operators';
import { Router } from '@angular/router';
import { sortBy } from 'lodash-es';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgClass, NgIf } from '@angular/common';

@Component({
  selector: 'app-stock-select',
  templateUrl: './stock-select.component.html',
  styleUrls: ['./stock-select.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, FormsModule, ReactiveFormsModule, NgSelectModule, NgClass],
})
export class StockSelectComponent implements OnInit, OnDestroy {
  @Input() stockOptions: FinancialAPIStockListData[] = [];
  @Input() isSearchStyle = false;

  stockOptionsLoading = false;
  allStockOptions: FinancialAPIStockListData[] = [];
  typeahead$: Subject<string | null> = new Subject<string | null>();

  formGroup: UntypedFormGroup;

  unsubscribe$: Subject<void> = new Subject<void>();

  constructor(
    private readonly stockExchangeService: StockExchangeService,
    private readonly formBuilder: UntypedFormBuilder,
    private readonly router: Router,
    private readonly cdr: ChangeDetectorRef
  ) {}

  get selectedStock(): string | null {
    return this.stockExchangeService.selectedStock;
  }

  ngOnInit(): void {
    this.getStockOptions();
    this.handleStockSearch();
  }

  getStockOptions(): void {
    this.allStockOptions = this.stockOptions;

    if (this.stockOptions.length !== 0) {
      this.initForm();
      return;
    }

    this.stockOptionsLoading = true;
    forkJoin([this.stockExchangeService.getStockListData(), this.stockExchangeService.getXtendStockListData()])
      .pipe(takeUntil(this.unsubscribe$))
      .subscribe(([listData, xtendListData]) => {
        this.stockOptions = sortBy([...listData.data, ...xtendListData.data], 'name');
        this.allStockOptions = this.stockOptions;
        this.stockOptionsLoading = false;
        this.initForm();
        this.cdr.detectChanges();
      });
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      search: [this.selectedStock],
    });

    // If form changes, then we need to update selected stock
    this.formGroup
      ?.get('search')
      ?.valueChanges.pipe(distinctUntilChanged(), takeUntil(this.unsubscribe$))
      .subscribe((stock: string | null) => {
        this.selectStock(stock);
      });

    // If globally selected currency changes, we need to update form
    this.stockExchangeService.selectedStock$.pipe(distinctUntilChanged(), takeUntil(this.unsubscribe$)).subscribe((selectedStock: string | null) => {
      this.formGroup?.get('search')?.patchValue(selectedStock, { emitEvent: false });
    });
  }

  handleStockSearch(): void {
    this.typeahead$.pipe(takeUntil(this.unsubscribe$)).subscribe((term: string | null) => {
      if (!term) {
        this.stockOptions = this.allStockOptions;
        return;
      }

      this.stockOptions = this.allStockOptions.filter(
        (stockOption: FinancialAPIStockListData) =>
          stockOption.name.toLowerCase().includes(term.toLowerCase()) || stockOption.fullName.toLowerCase().includes(term.toLowerCase())
      );
    });
  }

  selectStock(stock: string | null): void {
    this.stockExchangeService.selectedStock$.next(stock);
    this.router.navigate(stock ? ['/reszvenyek', stock.toLowerCase()] : ['/reszvenyek']);
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
