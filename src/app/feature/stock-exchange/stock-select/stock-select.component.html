<ng-container *ngIf="formGroup" [formGroup]="formGroup">
  <ng-select
    [clearable]="true"
    [formControlName]="'search'"
    [id]="'stockSelector'"
    [items]="stockOptions"
    [loading]="stockOptionsLoading"
    [ngClass]="{ 'search-style': isSearchStyle }"
    [placeholder]="isSearchStyle ? 'Mit keres?' : 'Részvénykibocsátók'"
    [searchable]="true"
    [selectOnTab]="true"
    [typeahead]="$any(typeahead$)"
    [virtualScroll]="true"
    [ariaLabel]="'Keresés'"
    bindValue="name"
    class="vg-form-select stock-select"
    clearAllText="Törlés"
    loadingText="Kérjük várjon..."
    notFoundText="Nincs találat a keresésre"
    typeToSearchText="Keresés..."
  >
    <ng-template let-item="item" ng-option-tmp>
      <span title="{{ item.name }} - {{ item.fullName }}">{{ item.name }} - {{ item.fullName }}</span>
    </ng-template>
    <ng-template let-item="item" ng-label-tmp>
      <span title="{{ item.name }} - {{ item.fullName }}">{{ item.name }} - {{ item.fullName }}</span>
    </ng-template>
  </ng-select>
</ng-container>
