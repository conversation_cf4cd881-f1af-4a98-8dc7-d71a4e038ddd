@use 'shared' as *;

.latest-news {
  padding-bottom: 32px;

  .left-column {
    @include media-breakpoint-down(md) {
      width: 100%;
    }
  }

  aside {
    @include media-breakpoint-down(md) {
      width: 100%;
    }
  }

  &-title {
    color: var(--kui-deep-teal-900);
    font-size: 24px;
    font-weight: 700;
    line-height: normal;
    letter-spacing: 0.96px;
  }

  &-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;

    &-elem {
      flex: 1 1 50%;
      margin-top: 10px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--kui-deep-teal-400);
      display: flex;
      align-items: center;

      vg-article-card {
        height: 100%;
        display: flex;
        align-items: center;
      }

      &.left {
        vg-article-card {
          margin-right: 20px;
        }
      }

      &.right {
        vg-article-card {
          padding-left: 20px;
          border-left: 1px solid var(--kui-deep-teal-400);
        }
      }
    }

    &-divider {
      width: 100%;
      border-bottom: 1px solid var(--kui-deep-teal-400);
      padding-bottom: 24px;
      margin: 40px 0 0;
      color: var(--kui-deep-teal-400);
      font-size: 20px;
      font-weight: 700;
      line-height: 26px;
      letter-spacing: 0.4px;
    }
  }

  vg-pager {
    margin-top: 24px;
  }
}
