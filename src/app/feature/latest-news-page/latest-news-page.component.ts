import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ApiResponseMetaList,
  ApiResult,
  ArticleCard,
  backendDateToDate,
  createCanonicalUrlForPageablePage,
  mapBackendArticleDataToArticleCard,
} from '@trendency/kesma-ui';
import { BackendArticleSearchResult } from '@trendency/kesma-ui/lib/definitions/article-card.definitions';
import { format } from 'date-fns';
import { Observable, Subject, tap } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { ArticleCardType, createVilaggazdasagTitle, defaultMetaInfo, VgArticleCardComponent, VgPagerComponent } from '../../shared';
import { CountedArticleCard } from './latest-news-page.definitions';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { AsyncPipe, NgClass, NgFor, NgIf } from '@angular/common';
import { DateFnsModule } from 'ngx-date-fns';

@Component({
  selector: 'app-latest-news-page',
  templateUrl: './latest-news-page.component.html',
  styleUrls: ['./latest-news-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, NgClass, VgArticleCardComponent, VgPagerComponent, SidebarComponent, AsyncPipe, DateFnsModule, AdvertisementAdoceanComponent],
})
export class LatestNewsPageComponent implements OnInit, OnDestroy {
  latestNewsResponse$: Observable<ApiResult<CountedArticleCard[], ApiResponseMetaList>> = this.route.data.pipe(
    map(({ data }) => ({
      ...data,
      data: this.mapArticleCardsToCountedArticleCards(
        data?.data.map((articleCard: BackendArticleSearchResult) => mapBackendArticleDataToArticleCard(articleCard))
      ),
    })),
    tap(({ data }) => {
      this.populateSidebarExcludedIds(data);
      this.setMetaData();
    })
  );

  sidebarExcludedIds: string[] = [];

  articleCardType = ArticleCardType;

  adverts?: AdvertisementsByMedium;
  unsubscribe$: Subject<void> = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly adStore: AdvertisementAdoceanStoreService,
    protected readonly cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.initAds();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }

  stringToDate(date: string | Date | undefined): Date | null {
    return backendDateToDate(String(date));
  }

  initAds(): void {
    this.resetAds();
    this.adStore.advertisemenets$.pipe(takeUntil(this.unsubscribe$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStore.separateAdsByMedium(ads);
      this.cdr.detectChanges();
    });
  }

  resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  private mapArticleCardsToCountedArticleCards(articleCards: ArticleCard[]): CountedArticleCard[] {
    let groupCount = 0;

    return articleCards.map((articleCard: ArticleCard, index: number) => {
      if (
        index === 0 ||
        format(backendDateToDate(articleCard.publishDate as string) as Date, 'yyyy-MM-dd') !==
          format(backendDateToDate(articleCards[index - 1].publishDate as string) as Date, 'yyyy-MM-dd')
      ) {
        groupCount = 0;
      }

      const countedArticleCard: CountedArticleCard = {
        ...articleCard,
        groupCount,
      };

      groupCount++;

      return countedArticleCard;
    });
  }

  private populateSidebarExcludedIds(articles: CountedArticleCard[]): void {
    if (articles?.length) this.sidebarExcludedIds = articles.map((item) => item?.id) as string[];
  }

  private setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage(this.seo.hostUrl + '/friss-hirek', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical, { addHostUrl: false, skipSeoMetaCheck: false });
    const title = createVilaggazdasagTitle(`Friss hírek`);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'index, follow',
      description: `Friss híreink összegyűjtve egy helyen. Kövesse nyomon a világgazdaság híreit oldalunkon.`,
      ogDescription: `Friss híreink összegyűjtve egy helyen. Kövesse nyomon a világgazdaság híreit oldalunkon.`,
    };
    this.seo.setMetaData(metaData);
  }
}
