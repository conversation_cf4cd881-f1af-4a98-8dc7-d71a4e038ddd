import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot } from '@angular/router';
import { Observable } from 'rxjs';
import { ApiService } from '../../shared';
import { tap } from 'rxjs/operators';
import { ApiResponseMetaList, ApiResult, BackendArticleSearchResult, buildPhpArrayParam, RedirectService } from '@trendency/kesma-ui';

@Injectable()
export class LatestNewsPageResolver {
  constructor(private readonly apiService: ApiService) {}

  kesmaRedirectService = inject(RedirectService);

  resolve(route: ActivatedRouteSnapshot): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>> {
    const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;
    return this.apiService
      .searchByKeyword(route.queryParams['page'] ? route.queryParams['page'] - 1 : 0, 38, {
        sponsorshipId: 'null',
        ...buildPhpArrayParam(['article', 'articleVideo', 'articleGallery', 'articlePodcast'], 'content_types'),
      })
      .pipe(
        tap((response) => {
          if (this.kesmaRedirectService.shouldBeRedirect(currentPage, response.data)) {
            this.kesmaRedirectService.redirectOldUrl(`friss-hirek`, false, 302);
          }
        })
      );
  }
}
