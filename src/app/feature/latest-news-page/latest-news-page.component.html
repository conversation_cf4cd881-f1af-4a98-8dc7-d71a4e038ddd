<section>
  <div class="wrapper with-aside latest-news">
    <div class="left-column">
      <h1 class="latest-news-title">Friss hírek</h1>

      <ng-container *ngIf="latestNewsResponse$ | async as latestNews">
        <div class="latest-news-list">
          <ng-container *ngFor="let article of latestNews.data; let i = index">
            <div *ngIf="article.groupCount === 0" class="latest-news-list-divider">
              {{ stringToDate(article?.publishDate) | dfnsFormat: 'yyyy. LLLL d. EEEE' }}
            </div>
            <div
              [ngClass]="{
                left: article.groupCount % 2 === 0,
                right: article.groupCount % 2 === 1,
              }"
              class="latest-news-list-elem"
            >
              <vg-article-card [data]="article" [styleID]="articleCardType.FreshNews"></vg-article-card>
            </div>

            <ng-container *ngIf="i === 4">
              <kesma-advertisement-adocean
                *ngIf="adverts?.desktop?.roadblock_1 as ad"
                [style]="{
                  margin: 'var(--ad-margin)',
                  background: 'var(--ad-bg-light)',
                  padding: 'var(--ad-padding)',
                }"
                [ad]="ad"
              >
              </kesma-advertisement-adocean>

              <kesma-advertisement-adocean
                *ngIf="adverts?.mobile?.mobilrectangle_1 as ad"
                [style]="{
                  margin: 'var(--ad-margin)',
                  background: 'var(--ad-bg-light)',
                  padding: 'var(--ad-padding)',
                }"
                [ad]="ad"
              >
              </kesma-advertisement-adocean>
            </ng-container>

            <ng-container *ngIf="i === 9">
              <kesma-advertisement-adocean
                *ngIf="adverts?.desktop?.roadblock_2 as ad"
                [style]="{
                  margin: 'var(--ad-margin)',
                  background: 'var(--ad-bg-light)',
                  padding: 'var(--ad-padding)',
                }"
                [ad]="ad"
              >
              </kesma-advertisement-adocean>
              <kesma-advertisement-adocean
                *ngIf="adverts?.mobile?.mobilrectangle_2 as ad"
                [style]="{
                  margin: 'var(--ad-margin)',
                  background: 'var(--ad-bg-light)',
                  padding: 'var(--ad-padding)',
                }"
                [ad]="ad"
              >
              </kesma-advertisement-adocean>
            </ng-container>
          </ng-container>
        </div>

        <vg-pager
          *ngIf="latestNews.meta.limitable.pageMax && latestNews.meta.limitable.pageMax > 0"
          [allowAutoScrollToTop]="true"
          [hasSkipButton]="true"
          [isListPager]="true"
          [maxDisplayedPages]="5"
          [rowAllCount]="latestNews.meta.limitable?.rowAllCount || 0"
          [rowOnPageCount]="latestNews.meta.limitable?.rowOnPageCount || 0"
        >
        </vg-pager>

        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.roadblock_3 as ad"
          [style]="{
            margin: 'var(--ad-margin)',
            background: 'var(--ad-bg-light)',
            padding: 'var(--ad-padding)',
            width: '100%',
          }"
          [ad]="ad"
        >
        </kesma-advertisement-adocean>
        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.mobilrectangle_3 as ad"
          [style]="{
            margin: 'var(--ad-margin)',
            background: 'var(--ad-bg-light)',
            padding: 'var(--ad-padding)',
          }"
          [ad]="ad"
        >
        </kesma-advertisement-adocean>
      </ng-container>
    </div>

    <aside>
      <app-sidebar [excludedIds]="sidebarExcludedIds"></app-sidebar>
    </aside>
  </div>
</section>
