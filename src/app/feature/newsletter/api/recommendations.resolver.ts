import { Injectable } from '@angular/core';
import { Resolve } from '@angular/router';
import { Observable, of } from 'rxjs';
import { ArticleCard, BackendArticleSearchResult, mapBackendArticleDataToArticleCard } from '@trendency/kesma-ui';
import { catchError, map } from 'rxjs/operators';
import { ApiService } from '../../../shared';

@Injectable()
export class RecommendationsResolver implements Resolve<ArticleCard[]> {
  constructor(private readonly apiService: ApiService) {}

  resolve(): Observable<any> {
    return this.apiService.getArticles(4).pipe(
      map(({ data }) => data.map((article: BackendArticleSearchResult) => mapBackendArticleDataToArticleCard(article))),
      catchError(() => {
        return of({});
      })
    ) as Observable<ArticleCard[]>;
  }
}
