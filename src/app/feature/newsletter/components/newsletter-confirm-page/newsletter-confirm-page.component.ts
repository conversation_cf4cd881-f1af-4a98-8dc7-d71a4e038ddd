import { ChangeDetectionStrategy, Component, OnInit } from '@angular/core';
import { SeoService } from '@trendency/kesma-core';
import { createVilaggazdasagTitle, defaultMetaInfo } from '../../../../shared';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-newsletter-confirm-page',
  templateUrl: './newsletter-confirm-page.component.html',
  styleUrls: ['./newsletter-confirm-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink],
})
export class NewsletterConfirmPageComponent implements OnInit {
  constructor(private readonly seo: SeoService) {}

  ngOnInit(): void {
    const title = createVilaggazdasagTitle('Hírlevél megerősítés');
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    });
  }
}
