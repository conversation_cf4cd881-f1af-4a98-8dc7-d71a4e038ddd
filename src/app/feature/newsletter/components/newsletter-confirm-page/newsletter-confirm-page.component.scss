@use 'shared' as *;

.newsletter-confirm {
  padding: 44px 0;

  @include media-breakpoint-down(sm) {
    padding: 24px 0 48px;
  }
}

.content {
  width: 100%;
  max-width: 760px;
  margin: 0 auto;
  text-align: center;

  h1 {
    color: var(--kui-deep-teal-900);
    font-size: 24px;
    font-weight: 700;
    line-height: normal;
    letter-spacing: 0.96px;
    margin-bottom: 44px;
    padding: 4px 0;

    @include media-breakpoint-down(sm) {
      font-size: 20px;
      line-height: 26px;
      letter-spacing: 0.4px;
    }
  }

  p {
    color: var(--kui-gray-950);
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    letter-spacing: 0.36px;
    margin-bottom: 44px;

    span {
      color: var(--kui-slate-950);
      font-weight: 700;
    }
  }

  a {
    color: var(--kui-deep-teal-400);
    font-size: 20px;
    font-style: normal;
    font-weight: 700;
    line-height: 26px;
    letter-spacing: 0.4px;
    display: block;
    padding: 8px;
    border-radius: 2px;
    border: 1px solid var(--kui-deep-teal-400);
    transition: 0.3s;

    &:hover {
      color: var(--kui-gray-950);
      background: var(--kui-deep-teal-400);
    }
  }
}
