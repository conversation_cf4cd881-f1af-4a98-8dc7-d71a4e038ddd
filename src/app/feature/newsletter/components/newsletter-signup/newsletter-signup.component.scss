@use 'shared' as *;

:host {
  padding: 44px 0;
  display: block;
  margin-bottom: 16px;

  @include media-breakpoint-down(sm) {
    padding: 24px 0;
    margin-bottom: 24px;
  }
}

.content {
  max-width: 870px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 44px;

  @include media-breakpoint-down(sm) {
    gap: 24px;
  }
}

.newsletter-signup-header {
  &-title {
    color: var(--kui-deep-teal-900);
    font-size: 24px;
    line-height: normal;
    letter-spacing: 0.96px;
    text-align: center;
    padding: 4px 0;
    margin-bottom: 44px;

    @include media-breakpoint-down(sm) {
      padding: 0;
      font-size: 20px;
      letter-spacing: 0.4px;
      margin-bottom: 24px;
    }
  }

  &-text {
    color: var(--kui-gray-950);
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    letter-spacing: 0.36px;
    text-align: center;

    @include media-breakpoint-down(sm) {
      font-size: 16px;
      line-height: 22px;
      text-align: left;
    }

    span {
      color: var(--kui-deep-teal-700);
    }
  }
}

.automizy-form-form {
  .automizy-form-fields {
    display: flex;
    flex-direction: column;
    gap: 44px;

    @include media-breakpoint-down(sm) {
      gap: 24px;
    }

    .flex-wrapper {
      display: flex;
      gap: 24px;

      @include media-breakpoint-down(sm) {
        flex-direction: column;
      }
    }

    .automizy-form-input-box {
      width: 100%;

      &.active {
        label {
          color: var(--kui-deep-teal-600);
        }
      }

      .automizy-form-input {
        width: 100%;
        border-radius: 2px;
        padding: 10px 8px;
        border: 1px solid var(--kui-gray-950);
        font-size: 16px;
        line-height: 24px;
      }

      &.marketing {
        display: flex;
        gap: 8px;

        input {
          margin: 2px 0;
          width: 20px;
          height: 20px;
          accent-color: var(--kui-deep-teal-600);
          flex: 0 0 auto;

          @include media-breakpoint-down(sm) {
            width: 16px;
            height: 16px;
          }
        }

        label {
          color: var(--gray-950);
          font-size: 16px;
          font-style: normal;
          font-weight: 500;
          line-height: 24px;

          @include media-breakpoint-down(sm) {
            font-size: 14px;
            font-weight: 400;
            line-height: 18px;
          }

          a {
            color: var(--kui-deep-teal-600);
            font-weight: 700;

            @include media-breakpoint-down(sm) {
              font-size: 16px;
              line-height: 22px;
            }
          }
        }
      }
    }

    .automizy-form-button-box {
      input {
        color: var(--kui-deep-teal-400);
        font-size: 20px;
        font-style: normal;
        font-weight: 700;
        line-height: 26px;
        letter-spacing: 0.4px;
        background: none;
        width: 100%;
        border-radius: 2px;
        border: 1px solid var(--kui-deep-teal-400);
        padding: 8px;
        cursor: pointer;
        transition: 0.3s;

        @include media-breakpoint-down(sm) {
          font-size: 16px;
          line-height: 22px;
        }

        &:hover {
          color: var(--kui-gray-950);
          background: var(--kui-deep-teal-400);
        }
      }
    }
  }
}

.automizy-form-privacy {
  color: var(--kui-gray-950);
  font-size: 16px;
  font-style: normal;
  font-weight: 500;
  line-height: 24px;
  padding-left: 28px;

  @include media-breakpoint-down(sm) {
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;
  }

  a {
    color: var(--kui-deep-teal-600);
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 22px;

    @include media-breakpoint-down(sm) {
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: 18px;
    }
  }
}

.radios {
  & > * {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  input {
    width: 24px !important;
    height: 24px;
    margin: 0 !important;
    accent-color: var(--kui-deep-teal-600);
    cursor: pointer;
    flex: 0 0 auto;

    @include media-breakpoint-down(sm) {
      width: 20px !important;
      height: 20px !important;
    }
  }

  label {
    color: var(--kui-gray-950);
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    cursor: pointer;
  }
}
