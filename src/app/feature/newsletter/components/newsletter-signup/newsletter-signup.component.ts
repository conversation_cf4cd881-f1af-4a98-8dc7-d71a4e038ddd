import { ChangeDetectionStrategy, Component, ElementRef, Inject, OnInit, Renderer2, ViewChildren } from '@angular/core';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { createVilaggazdasagTitle, defaultMetaInfo, NewsletterType } from '../../../../shared';
import { DOCUMENT } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { BypassPipe, createCanonicalUrlForPageablePage } from '@trendency/kesma-ui';

@Component({
  selector: 'app-newsletter-signup',
  templateUrl: './newsletter-signup.component.html',
  styleUrls: ['./newsletter-signup.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [BypassPipe],
})
export class NewsletterSignupComponent implements OnInit {
  @ViewChildren('radio') radios: ElementRef<HTMLHeadingElement>[];

  isPopupVisible = false;
  recaptcha = 'https://www.google.com/recaptcha/api.js';
  action = 'https://api.automizy.com/v2/forms/submit/Kac_ByhBJ3xtgxBBJOkZi-NeJBeAZO1WNF7YEQVsGcE/F0xPOusQ8XGHPcvIdku3HTSynXg';
  newsletterType: NewsletterType;
  readonly NewsletterType = NewsletterType;

  constructor(
    private readonly seo: SeoService,
    private readonly renderer2: Renderer2,
    private readonly route: ActivatedRoute,
    @Inject(DOCUMENT) private readonly _document: Document
  ) {}

  ngOnInit(): void {
    this.newsletterType = this.route?.snapshot?.data?.['type'];
    this.renderRecaptcha();
    this.setMetaData();
  }

  setRadioActive(radioId: string): void {
    this.radios.forEach((radio) => {
      radio.nativeElement.classList.remove('active');

      if (radio.nativeElement.id === radioId) {
        radio.nativeElement.classList.add('active');
      }
    });
  }

  private renderRecaptcha(): void {
    const s = this.renderer2.createElement('script');
    s.type = 'text/javascript';
    s.src = this.recaptcha;
    s.text = ``;
    this.renderer2.appendChild(this._document.body, s);
  }

  private setMetaData(): void {
    const title = createVilaggazdasagTitle('Hírlevél-feliratkozás');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
    const newsletterType = this.route.snapshot.data['type'];
    const pageUrl = newsletterType === NewsletterType.reggeli ? 'hirlevel-feliratkozas' : 'hirlevel-feliratkozas-2';
    const canonical = createCanonicalUrlForPageablePage(pageUrl);
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }
}
