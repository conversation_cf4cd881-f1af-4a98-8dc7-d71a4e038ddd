@use 'shared' as *;

.newsletter-success {
  padding: 44px 0 53px;

  @include media-breakpoint-down(sm) {
    padding: 24px 0;
  }
}

.content {
  width: 100%;
  max-width: 760px;
  margin: 0 auto;
  text-align: center;

  h1 {
    color: var(--kui-deep-teal-900);
    font-size: 24px;
    font-weight: 700;
    line-height: normal;
    letter-spacing: 0.96px;
    margin-bottom: 24px;

    @include media-breakpoint-down(sm) {
      font-size: 20px;
      line-height: 26px;
      letter-spacing: 0.4px;
    }
  }

  p {
    color: var(--kui-slate-950);
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    letter-spacing: 0.36px;
    margin-bottom: 44px;

    @include media-breakpoint-down(sm) {
      font-size: 16px;
      line-height: 22px;
    }
  }
}
