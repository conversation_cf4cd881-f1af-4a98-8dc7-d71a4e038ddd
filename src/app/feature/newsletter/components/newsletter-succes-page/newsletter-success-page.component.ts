import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Observable } from 'rxjs';
import { ArticleCard } from '@trendency/kesma-ui';
import { map } from 'rxjs/operators';
import { RecommendationBoxComponent } from '../../../../shared';
import { Async<PERSON>ip<PERSON>, NgIf, NgSwitch, NgSwitchCase } from '@angular/common';

@Component({
  selector: 'app-newsletter-success-page',
  templateUrl: './newsletter-success-page.component.html',
  styleUrls: ['./newsletter-success-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgSwitch, NgSwitchCase, RecommendationBoxComponent, AsyncPipe],
})
export class NewsletterSuccessPageComponent {
  recommendations$: Observable<ArticleCard[]> = this.route.data.pipe(map((data) => data['recommendations']));
  pageTextSuffix$: Observable<string> = this.route.data.pipe(map((data) => data['pageTextSuffix']));

  constructor(private readonly route: ActivatedRoute) {}
}
