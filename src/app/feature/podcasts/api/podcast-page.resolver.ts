import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { BackendPodcast } from '@trendency/kesma-ui';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ApiService } from '../../../shared';

@Injectable({
  providedIn: 'root',
})
export class PodcastPageResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<BackendPodcast> {
    const slug = route.params['podcastSlug'];

    return this.apiService.getPodcast(slug).pipe(
      catchError((error) => {
        this.router.navigate(['/', '404'], {
          state: { errorResponse: JSON.stringify(error) },
          skipLocationChange: true,
        });
        return throwError(error);
      })
    );
  }
}
