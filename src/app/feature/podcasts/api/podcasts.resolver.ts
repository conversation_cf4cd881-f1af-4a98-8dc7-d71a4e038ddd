import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { ApiResponseMetaList, ApiResult, ArticleCard, RedirectService } from '@trendency/kesma-ui';
import { Observable, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { ApiService } from '../../../shared';

@Injectable()
export class PodcastsResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router
  ) {}

  kesmaRedirectService = inject(RedirectService);

  resolve(snapshot: ActivatedRouteSnapshot): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    const queryParams = snapshot.queryParams as { page: string };
    const currentPage = queryParams?.page ? parseInt(queryParams.page, 10) - 1 : 0;

    const podcasts$ = this.apiService.getPodcasts(currentPage).pipe(
      tap((res) => {
        if (this.kesmaRedirectService.shouldBeRedirect(currentPage, res?.data)) {
          this.kesmaRedirectService.redirectOldUrl(`podcastok`, false, 302);
        }
      }),
      map((podcastResult) => {
        return {
          ...podcastResult,
          data: podcastResult.data.map(
            (podcastResultData) =>
              ({
                title: podcastResultData.title,
                thumbnail: {
                  url: podcastResultData.thumbnail,
                },
                lead: podcastResultData.lead,
                slug: podcastResultData.slug,
                length: podcastResultData.length,
                publishDate: podcastResultData.publishDate,
                isPodcastType: !!podcastResultData?.videaUrl,
                thumbnailFocusedImages: podcastResultData?.thumbnailFocusedImages,
              }) as ArticleCard
          ),
        };
      })
    );

    return podcasts$.pipe(
      catchError((error: HttpErrorResponse) => {
        this.router.navigate(['/404'], { skipLocationChange: true }).then();
        return throwError(() => error);
      })
    );
  }
}
