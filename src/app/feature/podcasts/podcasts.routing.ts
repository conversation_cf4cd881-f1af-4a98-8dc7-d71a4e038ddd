import { Routes } from '@angular/router';
import { PodcastsComponent } from './components/podcasts/podcasts.component';
import { PodcastsResolver } from './api/podcasts.resolver';
import { PodcastPageComponent } from './components/podcast-page/podcast-page.component';
import { PodcastPageResolver } from './api/podcast-page.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const PODCASTS_ROUTES: Routes = [
  {
    path: '',
    component: PodcastsComponent,
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    providers: [PodcastsResolver],
    resolve: {
      data: PodcastsResolver,
    },
    canActivate: [PageValidatorGuard],
  },
  {
    path: ':podcastSlug',
    component: PodcastPageComponent,
    providers: [PodcastPageResolver],
    resolve: { podcast: PodcastPageResolver },
  },
];
