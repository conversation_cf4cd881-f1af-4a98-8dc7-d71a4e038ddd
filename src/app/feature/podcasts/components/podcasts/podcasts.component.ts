import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ArticleCard,
  createCanonicalUrlForPageablePage,
} from '@trendency/kesma-ui';
import { map, Subject, takeUntil, tap } from 'rxjs';
import {
  ArticleCardType,
  createVilaggazdasagTitle,
  defaultMetaInfo,
  NewsletterBoxType,
  VgArticleCardComponent,
  VgNewsletterBoxComponent,
  VgPagerComponent,
} from '../../../../shared';
import { SidebarComponent } from '../../../layout/components/sidebar/sidebar.component';
import { AsyncPipe, NgFor, NgIf, SlicePipe } from '@angular/common';

@Component({
  selector: 'app-podcasts',
  templateUrl: './podcasts.component.html',
  styleUrls: ['./podcasts.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    VgArticleCardComponent,
    NgFor,
    SidebarComponent,
    AsyncPipe,
    SlicePipe,
    VgNewsletterBoxComponent,
    VgPagerComponent,
    AdvertisementAdoceanComponent,
  ],
})
export class PodcastsComponent implements OnDestroy {
  readonly destroy$: Subject<boolean> = new Subject();
  adverts?: AdvertisementsByMedium;
  sidebarExcludedIds: string[] = [];
  ArticleCardType = ArticleCardType;
  NewsletterBoxType = NewsletterBoxType;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly cdr: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService
  ) {}

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  articles$ = this.route.data.pipe(
    map(({ data }) => data),
    tap(({ data }) => {
      this.initAds();
      this.#setMetaData();
      this.populateSidebarExcludedIds(data);
    })
  );

  trackById(_: number, item: ArticleCard): string | undefined {
    return item.id;
  }

  protected initAds(): void {
    this.resetAds();
    this.adStore.advertisemenets$.pipe(takeUntil(this.destroy$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStore.separateAdsByMedium(ads);

      this.cdr.detectChanges();
    });
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  populateSidebarExcludedIds(articles: ArticleCard[]): void {
    if (articles?.length) this.sidebarExcludedIds = articles.map((item) => item?.id) as string[];
  }

  #setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('podcastok', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);

    const title = createVilaggazdasagTitle('Podcastok');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
