<ng-container *ngIf="articles$ | async as articles">
  <section class="podcast-page">
    <div class="wrapper first-article-wrapper">
      <h1 class="podcast-page-title font-bold-24">Podcastok</h1>

      <ng-container *ngIf="articles.data[0] as firstArticle">
        <vg-article-card
          class="first-article"
          [displayLead]="true"
          [data]="firstArticle"
          [showHighlightedView]="true"
          [styleID]="ArticleCardType.FeaturedRightImgDateTagTitle"
        ></vg-article-card>
      </ng-container>
    </div>
    <div class="ad-block full-width">
      <kesma-advertisement-adocean
        *ngIf="adverts?.desktop?.roadblock_1 as ad"
        [style]="{
          margin: 'var(--ad-margin)',
          padding: 'var(--ad-padding)',
        }"
        [ad]="ad"
      >
      </kesma-advertisement-adocean>

      <kesma-advertisement-adocean
        *ngIf="adverts?.mobile?.mobilrectangle_1 as ad"
        [style]="{
          margin: 'var(--ad-margin)',
          padding: 'var(--ad-padding)',
        }"
        [ad]="ad"
      >
      </kesma-advertisement-adocean>
    </div>
    <div class="wrapper">
      <vg-newsletter-box [styleID]="NewsletterBoxType.SocialRight"></vg-newsletter-box>
    </div>
  </section>

  <section>
    <div class="wrapper with-aside">
      <div class="left-column">
        <div class="inner-wrapper">
          <ng-container *ngFor="let article of articles.data | slice: 1; let i = index; trackBy: trackById">
            <vg-article-card [data]="$any(article)" [styleID]="ArticleCardType.FeaturedRightImgDateTagTitle"></vg-article-card>
            <div class="ad-block in-left-column" *ngIf="i === 3">
              <kesma-advertisement-adocean
                *ngIf="adverts?.desktop?.roadblock_2 as ad"
                [ad]="ad"
                [style]="{
                  margin: 'var(--ad-margin)',
                  background: 'var(--ad-bg-light)',
                  padding: 'var(--ad-padding)',
                }"
              >
              </kesma-advertisement-adocean>

              <kesma-advertisement-adocean
                *ngIf="adverts?.mobile?.mobilrectangle_2 as ad"
                [style]="{
                  margin: 'var(--ad-margin)',
                  background: 'var(--ad-bg-light)',
                  padding: 'var(--ad-padding)',
                }"
                [ad]="ad"
              >
              </kesma-advertisement-adocean>
            </div>
          </ng-container>
          <ng-container *ngIf="articles.meta.limitable as limitable">
            <vg-pager
              *ngIf="limitable.pageMax && limitable.pageMax > 0"
              [rowAllCount]="limitable?.rowAllCount || 0"
              [rowOnPageCount]="limitable?.rowOnPageCount || 0"
              [isListPager]="true"
              [allowAutoScrollToTop]="true"
              [maxDisplayedPages]="5"
              [hasSkipButton]="true"
            >
            </vg-pager>
          </ng-container>
          <div class="ad-block in-left-column">
            <kesma-advertisement-adocean
              *ngIf="adverts?.desktop?.roadblock_3 as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg-light)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>

            <kesma-advertisement-adocean
              *ngIf="adverts?.mobile?.mobilrectangle_3 as ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg-light)',
                padding: 'var(--ad-padding)',
              }"
              [ad]="ad"
            >
            </kesma-advertisement-adocean>
          </div>
        </div>
      </div>
      <aside>
        <app-sidebar [excludedIds]="sidebarExcludedIds"></app-sidebar>
      </aside>
    </div>
  </section>
</ng-container>
