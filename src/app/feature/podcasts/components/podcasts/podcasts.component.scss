@use 'shared' as *;

:host {
  display: block;
  margin-top: 18px;

  .podcast-page {
    display: flex;
    flex-direction: column;
    gap: 32px;
    @include media-breakpoint-down(sm) {
      gap: 24px;
    }
    &-title {
      color: var(--kui-deep-teal-900);
      margin-bottom: 24px;
    }
  }

  .inner-wrapper {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  //TODO: edit this when ads will ready

  .ad-block {
    background-color: var(--kui-gray-100);
    padding: 24px 0px;

    &.in-left-column {
      padding: 0;
      padding-bottom: 16px;
    }
  }

  .full-width {
    width: calc(100% + 140px);
    margin-inline: -70px;
  }

  .with-aside {
    margin-top: 25px;
  }

  .first-article-wrapper:first-child {
    vg-article-card::ng-deep {
      kesma-icon > svg {
        height: 24px !important;
        width: 24px !important;

        @include media-breakpoint-down(sm) {
          height: 16px !important;
          width: 16px !important;
        }
      }

      .article-card {
        &-title {
          font-size: 20px;
          line-height: 26px;

          @include media-breakpoint-down(sm) {
            font-size: 18px;
            line-height: normal;
          }
        }

        &-lead {
          font-size: 18px;
          font-weight: 400;
          line-height: 24px;

          @include media-breakpoint-down(sm) {
            font-size: 16px;
          }
        }

        @include media-breakpoint-down(sm) {
          &-data {
            flex-direction: column !important;
            gap: 8px;
          }

          &-thumbnail-card {
            flex-basis: content;
          }

          &-thumbnail {
            width: 100%;
            height: auto;
          }
        }

        @include media-breakpoint-up(md) {
          &-row {
            gap: 16px;
          }

          &-thumbnail-card {
            flex: 1;
            height: 238px;
          }

          &-thumbnail {
            height: 238px;
            width: 423px;
          }

          &-row {
            flex: 1;
          }
        }
      }
    }
  }

  vg-newsletter-box.social-right::ng-deep {
    .newsletter-social {
      @include media-breakpoint-up(md) {
        flex-wrap: nowrap;
        flex-direction: column;
      }
    }
  }

  vg-article-card::ng-deep {
    .article-card {
      &-podcast {
        display: none;
      }
    }
  }
}
