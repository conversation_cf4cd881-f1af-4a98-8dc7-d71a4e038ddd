@use 'shared' as *;

:host {
  .wrapper {
    margin: 24px auto 48px;

    app-article-header {
      ::ng-deep {
        .article-lead {
          font-size: 18px;
          line-height: 24px;
        }

        .article-bottom {
          justify-content: flex-start;
        }

        @include media-breakpoint-down(md) {
          app-social-share {
            &,
            .share kesma-icon {
              display: block !important;
            }
          }
        }
      }
    }

    .podcast-description {
      font-weight: 400;
      font-size: 18px;
      line-height: 24px;
      letter-spacing: 0.2px;
    }
  }
}
