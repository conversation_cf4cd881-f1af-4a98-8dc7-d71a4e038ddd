import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { map } from 'rxjs/operators';
import { ArticleHeaderComponent, createVilaggazdasagTitle, defaultMetaInfo } from '../../../../shared';
import { Subject, takeUntil } from 'rxjs';
import { Article, BackendPodcast, createCanonicalUrlForPageablePage, PodcastAudioPlayerComponent } from '@trendency/kesma-ui';
import { IMetaData, SeoService } from '@trendency/kesma-core';

@Component({
  selector: 'app-podcast-page',
  templateUrl: './podcast-page.component.html',
  styleUrls: ['./podcast-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ArticleHeaderComponent, PodcastAudioPlayerComponent],
})
export class PodcastPageComponent implements OnInit, OnD<PERSON>roy {
  podcast: BackendPodcast;
  destroy$: Subject<boolean> = new Subject();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    this.route.data
      .pipe(
        map(({ podcast }) => podcast.data),
        takeUntil(this.destroy$)
      )
      .subscribe((podcast) => {
        this.podcast = podcast;
        this.setMetaData();
        this.cdr.markForCheck();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('podcastok', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);
    const title = createVilaggazdasagTitle(this.podcast.title);
    const description = this.podcast.description || this.podcast.lead;
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title,
      ogTitle: title,
      description,
      ogDescription: description,
    };
    this.seo.setMetaData(metaData);
  }

  convertPodcastToArticle(podcast: BackendPodcast): Partial<Article> {
    return {
      tags: podcast?.tags,
      columnSlug: podcast.columnSlug,
      columnTitle: podcast.columnTitle,
      primaryColumn: {
        id: podcast.columnId as string,
        title: podcast.columnTitle as string,
        slug: podcast.columnSlug as string,
        columnEmphasizeOnArticleCard: true,
      },
      title: podcast.title,
      excerpt: podcast.lead,
    };
  }
}
