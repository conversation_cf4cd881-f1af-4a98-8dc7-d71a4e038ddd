import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  createCanonicalUrlForPageablePage,
} from '@trendency/kesma-ui';
import { combineLatest, distinctUntilChanged, Observable, shareReplay, Subject } from 'rxjs';
import { map, switchMap, takeUntil } from 'rxjs/operators';
import {
  createVilaggazdasagTitle,
  CurrencyExchangeService,
  CurrencyOption,
  currencyOptions,
  defaultMetaInfo,
  FinancialAPICurrencyCrossRatesFriendlyResult,
  FinancialAPICurrencyDayAndWeekData,
  FinancialAPICurrencyStatisticsCurrencyData,
  FinancialAPICurrencyStatisticsData,
} from '../../shared';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { CurrencyCrossRatesComponent } from './currency-cross-rates/currency-cross-rates.component';
import { CurrencyStatisticsComponent } from './currency-statistics/currency-statistics.component';
import { CurrencyChartComponent } from './currency-chart/currency-chart.component';
import { CurrencyInfoComponent } from './currency-info/currency-info.component';
import { CurrencyCalculatorComponent } from './currency-calculator/currency-calculator.component';
import { AsyncPipe, NgIf } from '@angular/common';
import { CurrencySearchComponent } from './currency-search/currency-search.component';

@Component({
  selector: 'app-currency-exchange',
  templateUrl: './currency-exchange.component.html',
  styleUrls: ['./currency-exchange.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CurrencySearchComponent,
    NgIf,
    CurrencyCalculatorComponent,
    CurrencyInfoComponent,
    CurrencyChartComponent,
    CurrencyStatisticsComponent,
    CurrencyCrossRatesComponent,
    SidebarComponent,
    AsyncPipe,
    AdvertisementAdoceanComponent,
  ],
})
export class CurrencyExchangeComponent implements OnInit, OnDestroy {
  unsubscribe$: Subject<void> = new Subject<void>();
  adverts?: AdvertisementsByMedium;
  firstLoad = true;

  dayData$: Observable<FinancialAPICurrencyDayAndWeekData> = this.currencyExchangeService.autoRefresh$.pipe(
    switchMap(() => this.currencyExchangeService.getDayData())
  );
  crossRates$: Observable<FinancialAPICurrencyCrossRatesFriendlyResult> = this.currencyExchangeService.autoRefresh$.pipe(
    switchMap(() => this.currencyExchangeService.getCrossRates()),
    shareReplay({
      bufferSize: 1,
      refCount: true,
    }),
    takeUntil(this.unsubscribe$)
  );
  statisticsData$: Observable<FinancialAPICurrencyStatisticsCurrencyData> = combineLatest([
    this.currencyExchangeService.selectedCurrency$.pipe(distinctUntilChanged()),
    this.currencyExchangeService.autoRefresh$,
  ]).pipe(
    switchMap(([currency]) =>
      this.currencyExchangeService.getStatistics().pipe(map((result: FinancialAPICurrencyStatisticsData) => result.data.data[currency]))
    )
  );

  get selectedCurrency(): string {
    return this.currencyExchangeService.selectedCurrency;
  }

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly currencyExchangeService: CurrencyExchangeService,
    private readonly seo: SeoService,
    private readonly adStore: AdvertisementAdoceanStoreService,
    protected readonly cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.route.params.pipe(takeUntil(this.unsubscribe$)).subscribe(({ currency }) => {
      if (currency) {
        if (currencyOptions.findIndex((currencyOption: CurrencyOption): boolean => currencyOption.currency === currency.toUpperCase()) !== -1) {
          this.currencyExchangeService.selectedCurrency$.next(currency.toUpperCase());
        } else {
          this.router.navigate(['/', '404'], {
            state: {},
            skipLocationChange: true,
          });
        }
      }

      this.initAds();
    });

    this.currencyExchangeService.selectedCurrency$.pipe(takeUntil(this.unsubscribe$)).subscribe((currency: string) => {
      let canonicalUrl: string | undefined;
      if (this.firstLoad && !this.route.snapshot.params?.['currency']) {
        canonicalUrl = 'deviza-adatok';
        this.firstLoad = false;
      } else {
        canonicalUrl = `deviza-adatok/${currency.toLowerCase()}`;
      }
      const canonical = createCanonicalUrlForPageablePage(canonicalUrl);
      canonical && this.seo.updateCanonicalUrl(canonical);
      const title = createVilaggazdasagTitle(`Deviza adatok (${currency}/HUF)`);
      const metaData: IMetaData = {
        ...defaultMetaInfo,
        title: title,
        ogTitle: title,
        robots: 'index, follow',
        description:
          `${currency} deviza árfolyam információk, utolsó ár, grafikonok, statisztikák és mutatók.` +
          `Kalkulátor deviza átszámításhoz, népszerű devizák kereszt árfolyamai. `,
        ogDescription:
          `${currency} deviza árfolyam információk, utolsó ár, grafikonok, statisztikák és mutatók.` +
          `Kalkulátor deviza átszámításhoz, népszerű devizák kereszt árfolyamai. `,
      };
      this.seo.setMetaData(metaData);
    });
  }

  initAds(): void {
    this.resetAds();
    this.adStore.advertisemenets$.pipe(takeUntil(this.unsubscribe$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStore.separateAdsByMedium(ads);
      this.cdr.detectChanges();
    });
  }

  resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
