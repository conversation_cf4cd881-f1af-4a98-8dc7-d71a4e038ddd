@use 'shared' as *;

.currency-exchange {
  padding-bottom: 32px;

  .left-column {
    @include media-breakpoint-down(lg) {
      width: 100%;
    }
  }

  aside {
    @include media-breakpoint-down(lg) {
      width: 100%;
    }
  }

  ::ng-deep {
    h2.currency-exchange-title {
      color: var(--kui-deep-teal-900);
      font-size: 18px;
      line-height: normal;
      font-weight: 700;
      letter-spacing: 0.36px;
      margin-bottom: 16px;
    }
  }

  app-currency-info,
  app-currency-calculator,
  app-currency-chart,
  app-currency-statistics,
  app-currency-cross-rates {
    margin-top: 40px;
  }
}
