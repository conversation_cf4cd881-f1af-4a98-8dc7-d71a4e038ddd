<section>
  <div class="wrapper with-aside currency-exchange">
    <div class="left-column">
      <app-currency-search></app-currency-search>
      <kesma-advertisement-adocean
        *ngIf="adverts?.desktop?.roadblock_1 as ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
        [ad]="ad"
      >
      </kesma-advertisement-adocean>

      <kesma-advertisement-adocean
        *ngIf="adverts?.mobile?.mobilrectangle_1 as ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
        [ad]="ad"
      >
      </kesma-advertisement-adocean>
      <app-currency-calculator [crossRates$]="crossRates$"></app-currency-calculator>
      <app-currency-info [selectedCurrency]="selectedCurrency" [dayData$]="dayData$"></app-currency-info>

      <app-currency-chart [selectedCurrency]="selectedCurrency"></app-currency-chart>

      <kesma-advertisement-adocean
        *ngIf="adverts?.desktop?.roadblock_2 as ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
        [ad]="ad"
      >
      </kesma-advertisement-adocean>
      <kesma-advertisement-adocean
        *ngIf="adverts?.mobile?.mobilrectangle_2 as ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
        [ad]="ad"
      >
      </kesma-advertisement-adocean>
      <app-currency-statistics [statisticsData]="statisticsData$ | async"></app-currency-statistics>
      <app-currency-cross-rates [crossRates]="crossRates$ | async"></app-currency-cross-rates>

      <kesma-advertisement-adocean
        *ngIf="adverts?.desktop?.roadblock_3 as ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
          width: '100%',
        }"
        [ad]="ad"
      >
      </kesma-advertisement-adocean>
      <kesma-advertisement-adocean
        *ngIf="adverts?.mobile?.mobilrectangle_3 as ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
        [ad]="ad"
      >
      </kesma-advertisement-adocean>
    </div>
    <aside>
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>
