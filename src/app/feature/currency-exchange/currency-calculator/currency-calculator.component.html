<div *ngIf="formGroup" [formGroup]="formGroup" class="currency-calculator">
  <h2 class="currency-exchange-title"><PERSON>za átszámítás</h2>
  <div *ngIf="crossRates; else loading" class="currency-calculator-wrapper">
    <div class="currency-calculator-group">
      <kesma-form-control class="currency-calculator-input-row">
        <input (focus)="selectOnFocus($event)" class="currency-calculator-input" formControlName="fromValue" type="text" aria-label="Kiindulási érték" />
      </kesma-form-control>
      <app-currency-select
        [formGroup]="formGroup"
        [id]="'fromCurrency'"
        [includeHUF]="true"
        controlName="fromCurrency"
        [ariaLabel]="'Kiindulási deviza'"
      ></app-currency-select>
    </div>
    <div class="currency-calculator-equal-sign">
      <kesma-icon [name]="'vg-equal'" [size]="24"></kesma-icon>
    </div>
    <div class="currency-calculator-group">
      <kesma-form-control class="currency-calculator-input-row">
        <input class="currency-calculator-input" formControlName="toValue" type="text" aria-label="Kalkulált érték" />
      </kesma-form-control>
      <app-currency-select
        [formGroup]="formGroup"
        [id]="'toCurrency'"
        [includeHUF]="true"
        controlName="toCurrency"
        [ariaLabel]="'Kalkulált deviza'"
      ></app-currency-select>
    </div>
  </div>
</div>

<ng-template #loading>
  <vg-spinner [height]="50" [width]="50"></vg-spinner>
</ng-template>
