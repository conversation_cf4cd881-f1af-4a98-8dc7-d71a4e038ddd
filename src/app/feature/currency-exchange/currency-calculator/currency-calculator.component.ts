import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { CurrencyExchangeService, FinancialAPICurrencyCrossRatesFriendlyResult, VgSpinnerComponent } from '../../../shared';
import { distinctUntilChanged, Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { DecimalPipe, NgIf } from '@angular/common';
import { CurrencySelectComponent } from '../currency-select/currency-select.component';
import { IconComponent, KesmaFormControlComponent } from '@trendency/kesma-ui';

@Component({
  selector: 'app-currency-calculator',
  templateUrl: './currency-calculator.component.html',
  styleUrls: ['./currency-calculator.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [DecimalPipe],
  imports: [NgIf, FormsModule, ReactiveFormsModule, CurrencySelectComponent, VgSpinnerComponent, KesmaFormControlComponent, IconComponent],
})
export class CurrencyCalculatorComponent implements OnInit, OnDestroy {
  @Input() crossRates$: Observable<FinancialAPICurrencyCrossRatesFriendlyResult>;
  crossRates: FinancialAPICurrencyCrossRatesFriendlyResult;

  formGroup: UntypedFormGroup;

  unsubscribe$: Subject<void> = new Subject<void>();

  // Regex for numbers, user can type in numbers with , or . separators (only positive numbers)
  readonly valueRegex: RegExp = /^(0|[1-9]\d*)((\.\d+)?|(,\d+)?)$/;

  constructor(
    private readonly formBuilder: UntypedFormBuilder,
    private readonly currencyExchangeService: CurrencyExchangeService,
    private readonly decimalPipe: DecimalPipe,
    private readonly cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.initForm();
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      fromValue: [1, [Validators.required, Validators.pattern(this.valueRegex), Validators.maxLength(15)]],
      fromCurrency: [this.currencyExchangeService.selectedCurrency, Validators.required],
      toValue: [{ value: 'Betöltés...', disabled: true }],
      toCurrency: ['HUF', Validators.required],
    });

    // If form changes, then we need to calculate
    this.formGroup.valueChanges.pipe(distinctUntilChanged(), takeUntil(this.unsubscribe$)).subscribe(() => {
      this.calculate();
    });

    // If globally selected currency changes, we need to update form (and calculate)
    this.currencyExchangeService.selectedCurrency$.pipe(distinctUntilChanged(), takeUntil(this.unsubscribe$)).subscribe((selectedCurrency: string) => {
      this.formGroup?.get('fromCurrency')?.patchValue(selectedCurrency);
    });

    // If rates change, then we need to re-calculate
    this.crossRates$.pipe(takeUntil(this.unsubscribe$)).subscribe((crossRates: FinancialAPICurrencyCrossRatesFriendlyResult) => {
      this.crossRates = crossRates;
      this.calculate();
      this.cdr.detectChanges();
    });
  }

  selectOnFocus(element: FocusEvent | any): void {
    element.target.select();
  }

  calculate(): void {
    try {
      if (!this.crossRates) {
        return;
      }

      const values = this.formGroup?.value;

      const fromValue: number = +values?.fromValue?.toString().replace(',', '.');
      const conversionRate: number = this.crossRates?.[values?.toCurrency]?.[values?.fromCurrency];
      const toValue: number = fromValue * conversionRate;

      if (!this.formGroup?.get('fromValue')?.valid || isNaN(toValue)) {
        throw new Error();
      }

      this.formGroup?.get('toValue')?.patchValue(this.decimalPipe.transform(toValue, '1.0-4'), { emitEvent: false });
    } catch (e) {
      this.formGroup?.get('toValue')?.patchValue('Érvénytelen', { emitEvent: false });
    }
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
