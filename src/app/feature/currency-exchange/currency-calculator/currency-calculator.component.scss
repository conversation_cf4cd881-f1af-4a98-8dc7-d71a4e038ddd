@use 'shared' as *;

:host {
  display: block;
}

.currency-calculator {
  &-wrapper {
    display: flex;
    align-items: center;

    @include media-breakpoint-down(sm) {
      flex-direction: column;
      align-items: flex-start;
    }
  }

  &-group {
    display: flex;
    align-items: center;
    border-radius: 2px;
    border: 1px solid var(--kui-gray-950);
    width: 100%;

    @include media-breakpoint-down(sm) {
      max-width: initial;
    }

    app-currency-select {
      max-width: 50%;
      width: 100%;
    }

    ::ng-deep {
      .ng-select-container {
        border: none !important;

        .ng-arrow-wrapper {
          margin-right: 8px;
        }
      }

      .ng-dropdown-panel {
        margin-top: 0;
        margin-left: -1px;
        width: calc(100% + 2px);
      }

      kesma-form-control-error {
        position: absolute;
        bottom: -36px;
        left: 0;
      }
    }
  }

  &-equal-sign {
    margin: 0 24px;
    min-width: 24px;

    kesma-icon {
      color: var(--kui-gray-950);
    }

    @include media-breakpoint-down(sm) {
      width: 100%;
      margin: 12px 0;
      text-align: center;

      kesma-icon {
        display: inline;
      }
    }
  }

  &-input-row {
    position: relative;
    min-width: 50%;
    flex: 1;
    padding-right: 12px;

    &:after {
      position: absolute;
      right: 0;
      top: 10px;
      width: 1px;
      height: 24px;
      display: block;
      content: '';
      background-color: var(--kui-gray-200);
    }
  }

  &-input {
    background: none;
    font-size: 16px;
    font-weight: 700;
    line-height: 16px;
    width: 100%;
    height: 44px;
    padding: 0 0 0 12px;
    color: var(--kui-gray-950);

    &:disabled {
      color: var(--kui-gray-950) !important;
      opacity: 1; // required on iOS
    }
  }
}
