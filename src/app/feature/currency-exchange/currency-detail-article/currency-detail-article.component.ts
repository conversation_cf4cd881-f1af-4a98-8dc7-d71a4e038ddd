import { ChangeDetectionStrategy, Component, inject, input } from '@angular/core';
import { CurrencyChartComponent } from '../currency-chart/currency-chart.component';
import { CurrencyInfoComponent } from '../currency-info/currency-info.component';
import { CurrencyExchangeService, FinancialAPICurrencyDayAndWeekData } from '../../../shared';
import { Observable } from 'rxjs';
import { switchMap } from 'rxjs/operators';

@Component({
  selector: 'app-currency-detail-article',
  templateUrl: './currency-detail-article.component.html',
  styleUrls: ['./currency-detail-article.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CurrencyChartComponent, CurrencyInfoComponent],
})
export class CurrencyDetailArticleComponent {
  readonly #currencyExchangeService = inject(CurrencyExchangeService);

  selectedCurrency = input<string | null>(null);

  dayData$: Observable<FinancialAPICurrencyDayAndWeekData> = this.#currencyExchangeService.autoRefresh$.pipe(
    switchMap(() => this.#currencyExchangeService.getDayData())
  );
}
