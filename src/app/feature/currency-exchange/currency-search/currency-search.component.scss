@use 'shared' as *;

:host {
  display: block;
}

.currency-search {
  app-currency-select {
    max-width: 50%;

    @include media-breakpoint-down(sm) {
      max-width: 100%;
      width: 100%;
    }
  }

  &-predefined {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-top: 16px;

    @include media-breakpoint-down(sm) {
      flex-direction: column;
      align-items: flex-start;
    }

    &-title {
      color: var(--kui-gray-500);
      font-size: 14px;
      font-weight: 400;
      line-height: 18px;
    }

    &-currencies {
      display: flex;
      align-items: center;
      gap: 16px;
      flex: 1;

      @include media-breakpoint-down(sm) {
        overflow-y: auto;
        width: 100%;
        gap: 16px;
      }
    }

    &-currency {
      border-radius: 2px;
      border: 1px solid var(--kui-deep-teal-400);
      height: 40px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      font-weight: 700;
      line-height: 18px;
      color: var(--kui-gray-950);
      width: 100%;
      cursor: pointer;
      transition:
        background-color 0.3s,
        color 0.3s;

      @include media-breakpoint-down(sm) {
        height: 32px;
        min-width: 70px;
        font-size: 12px;
        line-height: 14px;
      }

      &.active {
        background-color: var(--kui-deep-teal-400);
        color: var(--kui-white);
      }

      @media (hover: hover) {
        &:hover {
          background-color: var(--kui-deep-teal-400);
          color: var(--kui-white);
        }
      }
    }
  }
}
