import { ChangeDetectionStrategy, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { distinctUntilChanged, Subject } from 'rxjs';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import { takeUntil } from 'rxjs/operators';
import { CurrencyExchangeService } from '../../../shared';
import { Location, NgClass, NgFor, NgIf } from '@angular/common';
import { CurrencySelectComponent } from '../currency-select/currency-select.component';

@Component({
  selector: 'app-currency-search',
  templateUrl: './currency-search.component.html',
  styleUrls: ['./currency-search.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, CurrencySelectComponent, FormsModule, ReactiveFormsModule, NgFor, NgClass],
})
export class CurrencySearchComponent implements OnInit, OnD<PERSON>roy {
  formGroup: UntypedFormGroup;

  predefinedCurrencies: string[] = ['EUR', 'USD', 'CHF', 'PLN', 'CZK', 'RON'];

  unsubscribe$: Subject<void> = new Subject<void>();

  constructor(
    private readonly formBuilder: UntypedFormBuilder,
    private readonly location: Location,
    private readonly currencyExchangeService: CurrencyExchangeService
  ) {}

  get selectedCurrency(): string {
    return this.currencyExchangeService.selectedCurrency;
  }

  ngOnInit(): void {
    this.initForm();
  }

  initForm(): void {
    this.formGroup = this.formBuilder.group({
      search: [this.selectedCurrency, Validators.required],
    });

    // If form changes, then we need to update selected currency
    this.formGroup
      ?.get('search')
      ?.valueChanges.pipe(distinctUntilChanged(), takeUntil(this.unsubscribe$))
      .subscribe((currency: string) => {
        this.selectCurrency(currency);
      });

    // If globally selected currency changes, we need to update form
    this.currencyExchangeService.selectedCurrency$.pipe(distinctUntilChanged(), takeUntil(this.unsubscribe$)).subscribe((selectedCurrency: string) => {
      this.formGroup?.get('search')?.patchValue(selectedCurrency, { emitEvent: false });
    });
  }

  selectCurrency(currency: string): void {
    this.currencyExchangeService.selectedCurrency$.next(currency);
    this.location.replaceState(`/deviza-adatok/${currency.toLowerCase()}`, '', {});
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
