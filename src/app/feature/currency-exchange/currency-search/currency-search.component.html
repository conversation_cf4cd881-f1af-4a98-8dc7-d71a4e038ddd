<div *ngIf="formGroup" class="currency-search">
  <h2 class="currency-exchange-title">Árf<PERSON><PERSON> kere<PERSON></h2>
  <app-currency-select [formGroup]="formGroup" [id]="'currencySelector'" controlName="search" [ariaLabel]="'Deviza'"></app-currency-select>
  <div class="currency-search-predefined">
    <div class="currency-search-predefined-title">Népszerű árfolyamok:</div>
    <div class="currency-search-predefined-currencies">
      <div
        (click)="selectCurrency(predefinedCurrency)"
        *ngFor="let predefinedCurrency of predefinedCurrencies"
        [ngClass]="{ active: selectedCurrency === predefinedCurrency }"
        class="currency-search-predefined-currency"
      >
        {{ predefinedCurrency }}/HUF
      </div>
    </div>
  </div>
</div>
