import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { FinancialAPICurrencyCrossRatesFriendlyResult, VgSpinnerComponent } from '../../../shared';
import { DecimalPipe, LowerCasePipe, NgClass, NgFor, NgIf, SlicePipe } from '@angular/common';

@Component({
  selector: 'app-currency-cross-rates',
  templateUrl: './currency-cross-rates.component.html',
  styleUrls: ['./currency-cross-rates.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, NgClass, LowerCasePipe, SlicePipe, DecimalPipe, VgSpinnerComponent],
})
export class CurrencyCrossRatesComponent {
  @Input() crossRates: FinancialAPICurrencyCrossRatesFriendlyResult | null;

  predefinedCurrencies: string[] = ['EUR', 'HUF', 'USD', 'JPY', 'GBP', 'CHF', 'AUD', 'CAD', 'CNY'];
}
