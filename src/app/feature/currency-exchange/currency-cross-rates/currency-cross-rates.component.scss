@use 'shared' as *;

:host {
  display: block;
}

.currency-cross-rates {
  &-table {
    overflow-y: auto;

    table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;

      th,
      td {
        border: 1px solid var(--kui-gray-100);
        border-radius: 2px;
        width: 10%;
        min-width: 70px;
      }

      th,
      td:first-child {
        border-color: var(--kui-deep-teal-100);
      }
    }
  }

  &-value {
    padding: 8px 4px;
    color: var(--kui-gray-950);
    font-size: 14px;
    line-height: 18px;
    font-weight: 700;
    display: flex;
    justify-content: center;
    align-items: center;

    img {
      margin-right: 8px;
      width: 20px;
      height: 20px;
    }
  }

  &-empty {
    background-color: var(--kui-gray-150);
    border-color: var(--kui-gray-150) !important;
    border-radius: 0 !important;
  }
}
