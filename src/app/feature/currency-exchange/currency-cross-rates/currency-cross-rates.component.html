<div class="currency-cross-rates">
  <h2 class="currency-exchange-title">Deviza kereszt árfolyamok</h2>
  <div *ngIf="crossRates; else loading" class="currency-cross-rates-table">
    <table>
      <thead>
        <tr>
          <th class="currency-cross-rates-empty"></th>
          <th *ngFor="let currencyColumn of predefinedCurrencies">
            <div class="currency-cross-rates-value">
              <img alt="" [src]="'assets/images/flags/flag-' + (currencyColumn | lowercase | slice: 0 : -1) + '.svg'" loading="lazy" />
              {{ currencyColumn }}
            </div>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let currencyRow of predefinedCurrencies">
          <td>
            <div class="currency-cross-rates-value">
              <img alt="" [src]="'assets/images/flags/flag-' + (currencyRow | lowercase | slice: 0 : -1) + '.svg'" loading="lazy" />
              {{ currencyRow }}
            </div>
          </td>
          <td *ngFor="let currencyColumn of predefinedCurrencies" [ngClass]="{ 'currency-cross-rates-empty': currencyRow === currencyColumn }">
            <div class="currency-cross-rates-value">
              {{ currencyRow !== currencyColumn ? (crossRates?.[currencyRow]?.[currencyColumn] || 0 | number: '1.0-4') : '' }}
            </div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

<ng-template #loading>
  <vg-spinner [height]="50" [width]="50"></vg-spinner>
</ng-template>
