<div *ngIf="currentData$ | async as currentData; else loading" class="currency-info">
  <div class="currency-info-data">
    <div class="currency-info-data-group">
      <span class="currency-info-data-name">{{ currencyTitle }} árfolyam ({{ selectedCurrency() }}/HUF)</span>
    </div>
    <div class="currency-info-data-group">
      <span
        [ngClass]="{
          up: currentData.percentage > 0,
          down: currentData.percentage < 0,
        }"
        class="color"
        >{{ currentData.rate | number: '1.0-4' }}</span
      >
      <span
        [ngClass]="{
          up: currentData.percentage > 0,
          down: currentData.percentage < 0,
        }"
        class="color currency-info-data-percent"
      >
        {{ currentData.percentage > 0 ? '+' : '' }}{{ currentData.percentage | number: '1.0-2' }}%
      </span>
      <kesma-icon
        [name]="'vg-exchange-' + (currentData.percentage > 0 ? 'up' : currentData.percentage < 0 ? 'down' : 'none')"
        [ngClass]="{
          up: currentData.percentage > 0,
          down: currentData.percentage < 0,
        }"
        [size]="16"
        class="color"
      ></kesma-icon>
    </div>
  </div>
</div>

<ng-template #loading>
  <vg-spinner [height]="50" [width]="50"></vg-spinner>
</ng-template>
