import { ChangeDetectionStrategy, Component, input, Input } from '@angular/core';
import { CurrencyOption, currencyOptions, FinancialAPICurrencyCurrentData, FinancialAPICurrencyDayAndWeekData, VgSpinnerComponent } from '../../../shared';
import { Observable } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { AsyncPipe, DecimalPipe, NgClass, NgIf } from '@angular/common';
import { IconComponent } from '@trendency/kesma-ui';
import { toObservable } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-currency-info',
  templateUrl: './currency-info.component.html',
  styleUrls: ['./currency-info.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgClass, AsyncPipe, DecimalPipe, VgSpinnerComponent, IconComponent],
})
export class CurrencyInfoComponent {
  selectedCurrency = input<string | null>(null);
  selectedCurrency$ = toObservable(this.selectedCurrency);

  @Input() dayData$: Observable<FinancialAPICurrencyDayAndWeekData>;

  currentData$: Observable<FinancialAPICurrencyCurrentData> = this.selectedCurrency$.pipe(
    map((currency: string | null) => currency ?? ''),
    switchMap((currency: string) => this.dayData$.pipe(map((result: FinancialAPICurrencyDayAndWeekData) => result?.data?.current?.[currency])))
  );

  get currencyTitle(): string {
    return currencyOptions.find((currencyOption: CurrencyOption): boolean => currencyOption.currency === this.selectedCurrency())?.title ?? 'Ismeretlen deviza';
  }
}
