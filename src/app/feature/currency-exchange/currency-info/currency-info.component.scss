@use 'shared' as *;

:host {
  display: block;
}

.currency-info {
  &-data {
    border-radius: 2px;
    border: 1px solid var(--kui-gray-800);
    background-color: var(--kui-gray-900);
    color: var(--kui-white);
    padding: 10px 16px;
    font-size: 14px;
    line-height: 18px;
    display: flex;
    align-items: center;
    gap: 16px;
    justify-content: space-between;

    @include media-breakpoint-down(sm) {
      flex-direction: column;
      align-items: flex-start;
    }

    &-group {
      display: flex;
      align-items: center;
      gap: 8px;

      @include media-breakpoint-down(sm) {
        width: 100%;
      }
    }

    &-name {
      font-weight: 700;
    }

    &-percent {
      margin-left: auto;
    }

    .color {
      color: var(--kui-deep-teal-400);
      font-weight: 700;

      &.up {
        color: var(--kui-green-500);
      }

      &.down {
        color: var(--kui-red-500);
      }
    }
  }
}
