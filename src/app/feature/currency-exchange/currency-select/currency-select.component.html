<ng-container *ngIf="formGroup && controlName" [formGroup]="formGroup">
  <kesma-form-control>
    <ng-select
      [clearable]="false"
      [formControlName]="controlName"
      [id]="id"
      [items]="currencyOptions"
      [loading]="currencyOptionsLoading"
      [searchable]="true"
      [selectOnTab]="true"
      [typeahead]="$any(typeahead$)"
      [virtualScroll]="true"
      [ariaLabel]="ariaLabel"
      bindValue="currency"
      class="vg-form-select currency-select"
      clearAllText="Törlés"
      loadingText="Kérjük várjon..."
      notFoundText="Nincs találat a keresésre"
      typeToSearchText="Keresés..."
    >
      <ng-template let-item="item" ng-option-tmp>
        <span title="{{ item.currency }} - {{ item.title }}">{{ item.currency }} - {{ item.title }}</span>
      </ng-template>
      <ng-template let-item="item" ng-label-tmp>
        <span title="{{ item.currency }} - {{ item.title }}">{{ item.currency }} - {{ item.title }}</span>
      </ng-template>
    </ng-select>
  </kesma-form-control>
</ng-container>
