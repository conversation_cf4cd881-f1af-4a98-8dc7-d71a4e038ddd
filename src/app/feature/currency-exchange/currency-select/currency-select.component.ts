import { ChangeDetectionStrategy, Component, Input, OnDestroy, OnInit } from '@angular/core';
import { CurrencyOption, currencyOptions } from '../../../shared';
import { Subject, takeUntil } from 'rxjs';
import { FormsModule, ReactiveFormsModule, UntypedFormGroup } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgIf } from '@angular/common';
import { KesmaFormControlComponent } from '@trendency/kesma-ui';

@Component({
  selector: 'app-currency-select',
  templateUrl: './currency-select.component.html',
  styleUrls: ['./currency-select.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, FormsModule, ReactiveFormsModule, NgSelectModule, KesmaFormControlComponent],
})
export class CurrencySelectComponent implements OnInit, OnDestroy {
  @Input() formGroup?: UntypedFormGroup;
  @Input() controlName?: string;
  @Input() id?: string = 'select';
  @Input() includeHUF = false;
  @Input() ariaLabel?: string;

  currencyOptionsLoading = false;
  currencyOptions: CurrencyOption[] = [];
  typeahead$: Subject<string | null> = new Subject<string | null>();
  unsubscribe$: Subject<void> = new Subject<void>();

  ngOnInit(): void {
    this.currencyOptions = this.getCurrencyOptions();
    this.handleCurrencySearch();
  }

  handleCurrencySearch(): void {
    this.typeahead$.pipe(takeUntil(this.unsubscribe$)).subscribe((term: string | null) => {
      if (!term) {
        this.currencyOptions = this.getCurrencyOptions();
        return;
      }

      this.currencyOptions = this.getCurrencyOptions().filter(
        (currencyOption: CurrencyOption) =>
          currencyOption.currency.toLowerCase().includes(term.toLowerCase()) || currencyOption.title.toLowerCase().includes(term.toLowerCase())
      );
    });
  }

  getCurrencyOptions(): CurrencyOption[] {
    return this.includeHUF
      ? [
          {
            currency: 'HUF',
            title: 'Magyar forint',
          } as CurrencyOption,
        ].concat(currencyOptions)
      : currencyOptions;
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
