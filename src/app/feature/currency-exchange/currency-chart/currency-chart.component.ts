import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, input, OnDestroy, ViewChild } from '@angular/core';
import {
  convertUTCTimestampToDate,
  currencyChartDefaultConfig,
  currencyChartSeriesConfig,
  CurrencyChartType,
  CurrencyExchangeService,
  financialAPICurrencyChartDataMapper,
  FinancialAPICurrencyDateAggregatedData,
  FinancialAPICurrencyDateData,
  VgSpinnerComponent,
} from '../../../shared';
import { BehaviorSubject, combineLatest, Subject } from 'rxjs';
import { distinctUntilChanged, switchMap, takeUntil, tap } from 'rxjs/operators';
import { createChart, CustomData, IChartApi, ISeriesApi, MouseEventParams, TickMarkType, UTCTimestamp } from 'lightweight-charts';
import { format, isWeekend } from 'date-fns';
import { AsyncPipe, DecimalPipe, NgClass } from '@angular/common';
import { UtilService } from '@trendency/kesma-core';
import { toObservable } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-currency-chart',
  templateUrl: './currency-chart.component.html',
  styleUrls: ['./currency-chart.component.scss'],
  providers: [DecimalPipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgClass, AsyncPipe, VgSpinnerComponent],
})
export class CurrencyChartComponent implements AfterViewInit, OnDestroy {
  showHeaderInfo = input<boolean>(true);
  selectedCurrency = input<string | null>(null);
  selectedCurrency$ = toObservable(this.selectedCurrency);

  chartType$: BehaviorSubject<CurrencyChartType> = new BehaviorSubject<CurrencyChartType>(
    isWeekend(new Date()) ? CurrencyChartType.WEEK : CurrencyChartType.DAY
  );

  chart: IChartApi;
  dataSeries: ISeriesApi<'Area'>;
  isChartLoaded = false;
  isChartDataLoading = false;
  isChartEmpty = false;

  CurrencyChartType = CurrencyChartType;

  unsubscribe$: Subject<void> = new Subject<void>();

  @ViewChild('chartContainer', { static: false }) chartContainer: ElementRef<HTMLDivElement>;
  @ViewChild('chartTooltip', { static: false }) chartTooltip: ElementRef<HTMLDivElement>;

  constructor(
    private readonly currencyExchangeService: CurrencyExchangeService,
    private readonly decimalPipe: DecimalPipe,
    private readonly cdr: ChangeDetectorRef,
    private readonly utilsService: UtilService
  ) {}

  ngAfterViewInit(): void {
    if (this.utilsService.isBrowser()) {
      this.handleChartData();
    }
  }

  initChart(): void {
    this.chart = createChart(this.chartContainer.nativeElement, currencyChartDefaultConfig);
    this.dataSeries = this.chart.addAreaSeries(currencyChartSeriesConfig);
  }

  handleChartData(): void {
    combineLatest([
      this.chartType$.pipe(distinctUntilChanged()),
      this.selectedCurrency$.pipe(distinctUntilChanged()),
      this.currencyExchangeService.autoRefresh$,
    ])
      .pipe(
        tap(() => {
          this.isChartDataLoading = true;
        }),
        switchMap(([chartType, currency]) => this.currencyExchangeService.getGraphData(chartType, currency ?? '')),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((chartData: FinancialAPICurrencyDateData[] | FinancialAPICurrencyDateAggregatedData[] | null) => {
        if (!this.chart) {
          this.initChart();
          this.handleChartTooltip();
          this.isChartLoaded = true;
        }

        this.dataSeries.setData(financialAPICurrencyChartDataMapper(chartData ?? [], this.chartType$.value));
        this.applyChartTimeFormats();
        this.chart.timeScale().fitContent();

        if (this.utilsService.isBrowser()) {
          setTimeout(() => {
            this.isChartDataLoading = false;
            this.cdr.detectChanges();
          }, 300);
        } else {
          this.isChartDataLoading = false;
        }

        this.isChartEmpty = (chartData?.length ?? 0) === 0;
        this.cdr.detectChanges();
      });
  }

  handleChartTooltip(): void {
    this.chart.subscribeCrosshairMove((param: MouseEventParams) => {
      const tooltip: HTMLDivElement = this.chartTooltip.nativeElement;

      if (param.point === undefined || !param.time || param.point.x < 0 || param.point.y < 0) {
        tooltip.style.display = 'none';
      } else {
        const dateStr: string = format(convertUTCTimestampToDate(param.time as UTCTimestamp), 'yyyy.MM.dd.');
        const data: CustomData | undefined = param.seriesData.get(this.dataSeries);

        // Display tooltip only if custom values are available
        if (data?.customValues) {
          const aggregatedData: FinancialAPICurrencyDateAggregatedData = data.customValues as any;

          tooltip.style.display = 'block';
          tooltip.innerHTML = `
            <div><strong>${dateStr}</strong></div>
            <div>Nyitó: <strong>${this.decimalPipe.transform(aggregatedData.open ?? 0, '1.0-4')}</strong></div>
            <div>Min: <strong>${this.decimalPipe.transform(aggregatedData.min ?? 0, '1.0-4')}</strong></div>
            <div>Max: <strong>${this.decimalPipe.transform(aggregatedData.max ?? 0, '1.0-4')}</strong></div>
            <div>Záró: <strong>${this.decimalPipe.transform(aggregatedData.close ?? 0, '1.0-4')}</strong></div>
        `;

          // Position tooltip according to mouse cursor position
          tooltip.style.left = param.point.x + 'px';
          tooltip.style.top = param.point.y + 'px';
        }
      }
    });
  }

  applyChartTimeFormats(): void {
    this.chart.applyOptions({
      localization: {
        // Format datetime labels on cross-hair hover (x axis hover label)
        timeFormatter: (timestamp: UTCTimestamp) => {
          const date: Date = convertUTCTimestampToDate(timestamp);

          switch (this.chartType$.value) {
            case CurrencyChartType.DAY:
            case CurrencyChartType.WEEK:
              return format(date, 'MM.dd. HH:mm');
            case CurrencyChartType.MONTH:
              return format(date, 'MM.dd.');
            case CurrencyChartType.YEAR:
            case CurrencyChartType.THREE_YEARS:
              return format(date, 'yyyy.MM.dd.');
            default:
              return '';
          }
        },
        // Format price with angular number formatter (decimal precision 2-4)
        priceFormatter: (priceValue: number): string => {
          return this.decimalPipe.transform(priceValue, '1.2-4') ?? '';
        },
      },
      timeScale: {
        // Format datetime labels on x axis
        tickMarkFormatter: (timestamp: UTCTimestamp, tickMarkType: TickMarkType) => {
          const date: Date = convertUTCTimestampToDate(timestamp);

          switch (this.chartType$.value) {
            case CurrencyChartType.DAY:
              return format(date, 'HH:mm');
            case CurrencyChartType.WEEK:
            case CurrencyChartType.MONTH:
              if (tickMarkType === TickMarkType.Time) {
                return '';
              }
              return format(date, 'MM.dd.');
            case CurrencyChartType.YEAR:
            case CurrencyChartType.THREE_YEARS:
              return format(date, 'yyyy.MM.');
            default:
              return '';
          }
        },
      },
    });
  }

  changeChartType(type: CurrencyChartType): void {
    this.chartType$.next(type);
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
