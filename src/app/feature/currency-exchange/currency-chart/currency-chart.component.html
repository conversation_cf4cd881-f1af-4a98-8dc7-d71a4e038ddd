<div class="currency-chart">
  @if (showHeaderInfo()) {
    <div class="currency-chart-header">
      <h2 class="currency-exchange-title">Deviza árfolyamok</h2>
      <div class="currency-chart-divider"></div>
      <h2 class="currency-exchange-title">{{ selectedCurrency() }} / HUF árfolyamgrafikon</h2>
    </div>
  }
  @if (chartType$ | async; as chartType) {
    <div class="currency-chart-types">
      <div (click)="changeChartType(CurrencyChartType.DAY)" [ngClass]="{ active: chartType === CurrencyChartType.DAY }" class="currency-chart-type">Napi</div>
      <div (click)="changeChartType(CurrencyChartType.WEEK)" [ngClass]="{ active: chartType === CurrencyChartType.WEEK }" class="currency-chart-type"><PERSON><PERSON></div>
      <div (click)="changeChartType(CurrencyChartType.MONTH)" [ngClass]="{ active: chartType === CurrencyChartType.MONTH }" class="currency-chart-type">
        Havi
      </div>
      <div (click)="changeChartType(CurrencyChartType.YEAR)" [ngClass]="{ active: chartType === CurrencyChartType.YEAR }" class="currency-chart-type">Éves</div>
      <div
        (click)="changeChartType(CurrencyChartType.THREE_YEARS)"
        [ngClass]="{ active: chartType === CurrencyChartType.THREE_YEARS }"
        class="currency-chart-type"
      >
        3 éves
      </div>
    </div>
  }
  <div class="currency-chart-wrapper">
    @if (isChartLoaded && isChartDataLoading) {
      <div class="currency-chart-data-loader">
        <vg-spinner [height]="30" [width]="30"></vg-spinner>
      </div>
    }
    @if (!isChartLoaded) {
      <div>
        <vg-spinner [height]="50" [width]="50"></vg-spinner>
      </div>
    }
    @if (isChartEmpty) {
      <div class="currency-chart-no-data">Nincs adat</div>
    }
    <div #chartContainer class="currency-chart-chart"></div>
    <div #chartTooltip class="currency-chart-tooltip"></div>
  </div>
</div>
