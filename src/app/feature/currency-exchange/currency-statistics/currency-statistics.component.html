<div class="currency-statistics">
  <h2 class="currency-exchange-title">{{ selectedCurrency }} / HUF spot stat<PERSON><PERSON><PERSON><PERSON>, mutatók</h2>
  <div *ngIf="statisticsData; else loading" class="currency-statistics-tables">
    <div class="currency-statistics-table">
      <table>
        <tbody>
          <tr>
            <td>Nyit<PERSON></td>
            <td>{{ statisticsData.open | number: '1.0-4' }}</td>
          </tr>
          <tr>
            <td>Előző záró ár</td>
            <td>{{ statisticsData.close | number: '1.0-4' }}</td>
          </tr>
          <tr>
            <td>Napi <PERSON></td>
            <td>{{ statisticsData.average | number: '1.0-4' }}</td>
          </tr>
          <tr>
            <td>Napi minimum</td>
            <td>{{ statisticsData.min | number: '1.0-4' }}</td>
          </tr>
          <tr>
            <td>Napi maximum</td>
            <td>{{ statisticsData.max | number: '1.0-4' }}</td>
          </tr>
        </tbody>
      </table>
    </div>
    <div class="currency-statistics-table">
      <table>
        <tbody>
          <tr>
            <td>Éves minimum</td>
            <td>{{ statisticsData.yearlymin | number: '1.0-4' }}</td>
          </tr>
          <tr>
            <td>Éves maximum</td>
            <td>{{ statisticsData.yearlymax | number: '1.0-4' }}</td>
          </tr>
          <tr>
            <td>Árfolyamváltozás 1 hó</td>
            <td>{{ statisticsData.onemonthpercentage > 0 ? '+' : '' }}{{ statisticsData.onemonthpercentage | number: '1.0-2' }}{{ '%' }}</td>
          </tr>
          <tr>
            <td>Árfolyamváltozás 6 hó</td>
            <td>{{ statisticsData.sixmonthpercentage > 0 ? '+' : '' }}{{ statisticsData.sixmonthpercentage | number: '1.0-2' }}{{ '%' }}</td>
          </tr>
          <tr>
            <td>Árfolyamváltozás 12 hó</td>
            <td>{{ statisticsData.yearpercentage > 0 ? '+' : '' }}{{ statisticsData.yearpercentage | number: '1.0-2' }}{{ '%' }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>

<ng-template #loading>
  <vg-spinner [height]="50" [width]="50"></vg-spinner>
</ng-template>
