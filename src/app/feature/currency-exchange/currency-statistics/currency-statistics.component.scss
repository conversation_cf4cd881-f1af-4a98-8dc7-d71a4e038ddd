@use 'shared' as *;

:host {
  display: block;
}

.currency-statistics {
  &-tables {
    display: flex;
    gap: 24px;

    @include media-breakpoint-down(sm) {
      flex-direction: column;
      gap: 0;
    }
  }

  &-table {
    width: 100%;

    table {
      width: 100%;

      td {
        border-bottom: 1px solid var(--kui-gray-700);
        background: var(--kui-gray-900);
        color: var(--kui-white);
        text-align: left;
        font-size: 14px;
        font-weight: 400;
        line-height: 18px;
        vertical-align: middle;
        padding: 8px 12px;

        span {
          color: var(--kui-gray-500);
          padding: 0 6px;
        }

        @include media-breakpoint-down(md) {
          min-width: 60px;
        }

        &:last-child {
          text-align: right;
          font-weight: 700;
        }
      }

      tr {
        &:nth-child(even) {
          td {
            background-color: var(--kui-gray-800);
          }
        }

        &:last-child {
          td {
            border-bottom: none;
          }
        }
      }
    }

    @include media-breakpoint-down(sm) {
      &:last-child {
        table {
          border-top: 1px solid var(--kui-gray-700);

          td {
            background: var(--kui-gray-800);
          }

          tr {
            &:nth-child(even) {
              td {
                background-color: var(--kui-gray-900);
              }
            }
          }
        }
      }
    }
  }
}
