import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { CurrencyExchangeService, FinancialAPICurrencyStatisticsCurrencyData, VgSpinnerComponent } from '../../../shared';
import { DecimalPipe, NgIf } from '@angular/common';

@Component({
  selector: 'app-currency-statistics',
  templateUrl: './currency-statistics.component.html',
  styleUrls: ['./currency-statistics.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, DecimalPipe, VgSpinnerComponent],
})
export class CurrencyStatisticsComponent {
  @Input() statisticsData: FinancialAPICurrencyStatisticsCurrencyData | null;

  constructor(private readonly currencyExchangeService: CurrencyExchangeService) {}

  get selectedCurrency(): string {
    return this.currencyExchangeService.selectedCurrency;
  }
}
