import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { forkJoin, Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { GalleryApiService } from './gallery-api.service';
import { mapGalleryRecommendationsToGalleryData } from '../../../shared';

@Injectable()
export class GalleryLayerResolver {
  constructor(
    private readonly galleryApiService: GalleryApiService,
    private readonly router: Router
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<any> {
    const slug: string = route.params['slug'];

    return forkJoin({
      galleryDetails: this.galleryApiService.getGalleryDetails(slug),
      recommended: this.galleryApiService.getGalleryRecommendations(slug).pipe(
        map(({ data }) => {
          if (data.length) {
            return data.map(mapGalleryRecommendationsToGalleryData);
          }
          return [];
        })
      ),
    }).pipe(
      map(({ galleryDetails, recommended }) => ({
        galleryDetails: galleryDetails,
        recommended: recommended,
      })),
      catchError((error) => {
        this.router
          .navigate(['/', '404'], {
            skipLocationChange: true,
          })
          .then();
        return throwError(error);
      })
    );
  }
}
