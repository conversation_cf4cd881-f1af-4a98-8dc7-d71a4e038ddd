@use 'shared' as *;

:host {
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 9999;
  background: var(--kui-gray-950);
  overflow-y: scroll;
}

.gallery {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-width: 1330px;
  margin: 0 auto;
  color: var(--kui-white);
  padding: 24px 84px;

  @include media-breakpoint-down(md) {
    padding: 16px;
  }

  &-layer {
    &-header {
      display: flex;
      gap: 54px;
      align-items: flex-start;

      @include media-breakpoint-down(md) {
        flex-direction: column;
        gap: 16px;
      }

      &-meta {
        display: flex;
        flex-direction: column;
        gap: 16px;
        flex: 1;

        @include media-breakpoint-down(md) {
          width: 100%;
        }
      }

      &-logo {
        background-image: url('/assets/images/logo/logo.svg');
        width: 192px;
        height: 30px;
        background-size: contain;
        background-repeat: no-repeat;
        transition: 0.3s;
        &:hover {
          background-image: url('/assets/images/logo/logo-green.svg');
        }

        @include media-breakpoint-down(md) {
          height: 40px;
          width: 100%;
        }
      }

      &-logo-container {
        display: flex;
        justify-content: space-between;
        align-items: center;

        @include media-breakpoint-down(md) {
          width: 100%;
        }
      }

      &-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        align-items: center;

        @include media-breakpoint-down(md) {
          gap: 8px;
        }

        a {
          color: var(--kui-white);
          font-size: 14px;
          font-weight: 400;
          line-height: 18px;
          transition: color 0.3s;

          &.first-tag {
            font-weight: 700;
          }

          &:hover {
            color: var(--kui-deep-teal-400);
          }
        }
      }
    }

    &-title {
      font-size: 36px;
      font-weight: 700;
      line-height: 40px;

      @include media-breakpoint-down(md) {
        font-size: 32px;
      }
    }

    &-description {
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
    }

    &-meta-wrapper {
      display: flex;
      gap: 16px;
      align-items: center;
      flex-wrap: wrap;
    }

    &-actions {
      display: flex;
      gap: 24px;

      @include media-breakpoint-down(md) {
        display: none;
      }
    }
    &-action-button {
      display: flex;
      align-items: center;
      padding: 8px;
      border: 1px solid var(--kui-gray-900);
      transition: border 0.3s;

      &.full-screen {
        @include media-breakpoint-down(md) {
          display: none;
        }
      }

      &.mobile {
        display: none;
        @include media-breakpoint-down(md) {
          display: block;
        }
      }

      &:hover {
        border: 1px solid var(--kui-deep-teal-400);
      }
    }

    &-images-container {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }

    &-images {
      display: grid;
      grid-template-columns: 0.3fr 1fr 0.3fr;

      @include media-breakpoint-down(lg) {
        grid-template-columns: 0.1fr 1fr 0.1fr;
      }

      img {
        width: 100%;
        height: 630px;
        object-fit: contain;

        @include media-breakpoint-down(lg) {
          height: 384px;
        }

        @include media-breakpoint-down(sm) {
          height: 246px;
        }
      }
    }

    &-meta-and-socials {
      display: flex;
      justify-content: space-between;
      align-items: center;

      @include media-breakpoint-down(md) {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
      }
    }

    &-socials {
      display: flex;
      gap: 24px;
      flex-wrap: wrap;

      @include media-breakpoint-down(md) {
        display: block;
        width: 100%;
      }
    }

    &-socials-mobile {
      display: none;
      @include media-breakpoint-down(md) {
        padding: 8px;
        border: 2px solid var(--kui-deep-teal-400);
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 8px;
        color: var(--kui-white);
        font-size: 12px;
        font-weight: 700;
        line-height: 14px;
        transition: background-color 0.3s;

        &:hover {
          background-color: var(--kui-deep-teal-400);
        }
      }
    }

    &-image-caption-container {
      display: flex;
      justify-content: center;
      gap: 8px;
      padding-bottom: 8px;
      border-bottom: 1px solid var(--kui-gray-50);

      @include media-breakpoint-down(md) {
        justify-content: flex-start;
      }
    }

    &-image {
      &-count,
      &-caption {
        font-size: 20px;
        font-weight: 700;
        line-height: 26px;
        letter-spacing: 0.4px;

        @include media-breakpoint-down(md) {
          font-size: 14px;
          line-height: 18px;
        }
      }

      &-count {
        color: var(--kui-deep-teal-400);
      }
    }
  }

  &-recommender {
    &-header {
      display: flex;
      justify-content: space-between;

      &-right {
        display: flex;
        gap: 10px;
        align-items: center;
        flex: 0 0 auto;

        .image-overlay-swiper {
          position: static;
        }

        &-arrows {
          @include media-breakpoint-up(lg) {
            display: none;
          }
        }

        .gallery-layer-actions {
          @include media-breakpoint-down(md) {
            display: block;
          }
        }
      }
    }

    &-galleries {
      position: relative;
      display: flex;
      flex-wrap: wrap;
      gap: 16px;

      @include media-breakpoint-down(sm) {
        flex-direction: column;
        flex-wrap: nowrap;
      }
    }

    &-swiper-desktop {
      display: block;
      @include media-breakpoint-down(md) {
        display: none;
      }
    }
  }

  &-swiper {
    position: relative;
    display: flex;
    align-items: center;

    swiper-container {
      width: 100%;
      margin-left: auto;
      margin-right: auto;
      position: relative;
      overflow: hidden;
      list-style: none;
      padding: 0;
      z-index: 1;

      ::ng-deep {
        .swiper-button-next,
        .swiper-button-prev {
          display: none;
        }
      }
    }

    &-box {
      cursor: pointer;
    }

    &-image {
      object-fit: cover;
      height: 115.5px;
      aspect-ratio: 16 / 9;

      @include media-breakpoint-down(md) {
        height: auto;
      }
    }

    &-arrow {
      height: 100%;
      width: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 2;
      top: 0;
      color: var(--kui-white);
      transition: 0.3s;

      @include media-breakpoint-down(md) {
        display: none;
      }

      &.next {
        right: 0;
      }

      &:hover {
        color: var(--kui-deep-teal-400);
      }
    }
  }
}

vg-gallery-card {
  width: calc(50% - 8px);
  max-height: 300px;
  @include media-breakpoint-down(sm) {
    width: 100%;
  }
}

.image-overlay {
  &-container {
    position: relative;
  }

  &-content {
    position: absolute;
    height: 100%;
    width: 100%;
    top: 0;
    left: 0;
    background-color: var(--kui-white);
    opacity: 0.5;
    z-index: 1;
  }

  &-swiper {
    position: absolute;
    top: calc(50% - 22px);
    z-index: 5;
    height: 44px;
    width: 44px;
    padding: 10px;
    border-radius: 2px;
    background-color: var(--kui-gray-950-65);
    color: var(--kui-white);
    transition: 0.3s;

    &.right {
      right: 0;
    }

    &.left {
      left: 0;
    }

    &.left-big-size {
      @include media-breakpoint-up(lg) {
        left: -50px;
      }
    }

    &.right-big-size {
      @include media-breakpoint-up(lg) {
        right: -50px;
      }
    }

    &:hover {
      color: var(--kui-deep-teal-400);
    }
  }
}

.separator {
  width: 1px;
  height: 12px;
  background-color: var(--kui-white);
}

.tag-separator {
  height: 10px;
  width: 1px;
  background-color: var(--kui-gray-100);
}

.only-on-desktop {
  display: block;
  min-width: 20px;
  color: var(--kui-white);

  @include media-breakpoint-down(md) {
    display: none;
  }
}

.gallery-icons {
  color: var(--kui-white);
}

.photographer {
  font-weight: 700;
}

.social-popover {
  position: relative;

  &-inner {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding: 4px;
  }

  &-elements {
    display: flex;
    gap: 8px;
    padding: 0px 8px 0px 4px;
    align-items: center;
    color: var(--kui-white);
    transition: 0.3s;

    &:hover {
      color: var(--kui-deep-teal-400);
    }

    &.not-on-desktop {
      display: none;
      @include media-breakpoint-down(md) {
        display: flex;
      }
    }
  }
}
