<ng-container *ngIf="galleryData$ | async as galleryData">
  <ng-container *ngIf="!isUserAdultChoice && galleryData?.isAdult; else galleryContent">
    <app-adult-content (isUserAdult)="onUserAdultChoose($event)"></app-adult-content>
  </ng-container>

  <ng-template #galleryContent>
    <section #fullScreen="trFullscreen" class="gallery" trFullscreen>
      <ng-container *ngIf="galleryData.images?.[currentIndex]; else galleryRecommendation">
        <div class="gallery-layer-header">
          <div class="gallery-layer-header-logo-container">
            <div *ngIf="!isMobileApp" [routerLink]="['/']" class="gallery-layer-header-logo" data-skip-on-mobile-app></div>
            <button (click)="goBack()" class="gallery-layer-action-button mobile">
              <kesma-icon [size]="24" class="gallery-icons" name="vg-close"></kesma-icon>
            </button>
          </div>

          <div class="gallery-layer-header-meta">
            <div *ngIf="!isMobileApp && galleryData.tags?.length" class="gallery-layer-header-tags" data-skip-on-mobile-app>
              <ng-container *ngFor="let tag of galleryData?.tags; let first = first; let last = last; trackBy: trackByFn">
                <a [class.first-tag]="first" [routerLink]="['/', 'cimke', tag.slug]">{{ tag.title }}</a>
                <div *ngIf="!last" class="tag-separator"></div>
              </ng-container>
            </div>
            <h1 class="gallery-layer-title">{{ galleryData.title }}</h1>
            <span class="gallery-layer-description">{{ galleryData.description }}</span>
            <div *ngIf="galleryData.images?.[currentIndex] as currentImage" class="gallery-layer-meta-and-socials">
              <div class="gallery-layer-meta-wrapper">
                <span *ngIf="currentImage.photographer as photographer" class="photographer">{{ photographer }}</span>
                <div *ngIf="galleryData.publishDate && currentImage.photographer" class="separator"></div>
                <span *ngIf="galleryData.publishDate as publishDate">{{ publishDate | dfnsFormat: 'yyyy-MM-dd, HH:mm' }}</span>
                <div *ngIf="(currentImage.source && galleryData.publishDate) || (currentImage.source && galleryData.photographer)" class="separator"></div>
                <span *ngIf="currentImage.source as source">Fotó: {{ source }}</span>
              </div>
              <div class="gallery-layer-socials">
                <button class="only-on-desktop">
                  <kesma-icon
                    (click)="onClickUrlCopy()"
                    [hasTransition]="true"
                    [size]="20"
                    class="hovered-icon"
                    customHover="vg-copy-hover-green"
                    name="vg-copy"
                  ></kesma-icon>
                </button>
                <a [href]="emailUrl" class="only-on-desktop">
                  <kesma-icon [hasTransition]="true" [size]="20" customHover="vg-mail-hover-green" name="vg-mail"></kesma-icon>
                </a>
                <a [href]="facebookUrl" class="only-on-desktop" target="_blank">
                  <kesma-icon [hasTransition]="true" [size]="20" customHover="vg-facebook-circle-2-hover-green" name="vg-facebook-circle-2"></kesma-icon>
                </a>
                <div #popoverMenu *ngIf="!isMobileApp" class="social-popover" data-skip-on-mobile-app>
                  <button (click)="togglePopover()" class="only-on-desktop">
                    <kesma-icon [hasTransition]="true" [size]="20" customHover="vg-share-2-hover-green" name="vg-share-2"></kesma-icon>
                  </button>
                  <button (click)="togglePopover()" class="gallery-layer-socials-mobile">
                    Megosztom
                    <kesma-icon [size]="24" name="vg-share-mobile"></kesma-icon>
                  </button>
                  <app-social-popover *ngIf="popoverMenu$ | async" backgroundColor="var(--kui-gray-950)">
                    <div class="social-popover-inner">
                      <button (click)="onClickUrlCopy()" class="social-popover-elements not-on-desktop">
                        <kesma-icon [size]="20" name="vg-copy"></kesma-icon>
                        <span>Link másolása</span>
                      </button>
                      <a [href]="emailUrl" class="social-popover-elements not-on-desktop">
                        <kesma-icon [size]="20" name="vg-mail"></kesma-icon>
                        <span>Email</span>
                      </a>
                      <a [href]="facebookUrl" class="social-popover-elements not-on-desktop" target="_blank">
                        <kesma-icon [size]="20" name="vg-facebook-circle-2"></kesma-icon>
                        <span> Facebook</span>
                      </a>
                      <a [href]="linkedinUrl" class="social-popover-elements" target="_blank">
                        <kesma-icon [size]="20" customHover="vg-linkedin-hover-green" name="vg-linkedin"></kesma-icon>
                        <span> LinkedIn</span>
                      </a>
                      <a [href]="twitterUrl" class="social-popover-elements" target="_blank">
                        <kesma-icon [size]="20" customHover="vg-twitter-hover-green" name="vg-twitter"></kesma-icon>
                        <span> Twitter</span>
                      </a>
                    </div>
                  </app-social-popover>
                </div>
              </div>
            </div>
          </div>
          <ng-container *ngTemplateOutlet="galleryActions"></ng-container>
        </div>
        <div *ngIf="galleryData.images as displayedImages" class="gallery-layer-images-container">
          <div class="gallery-layer-images">
            <div class="image-overlay-container">
              <img
                [alt]="displayedImages?.[currentIndex - 1]?.altText || displayedImages?.[galleryData.images.length - 1]?.altText || ''"
                [src]="displayedImages?.[currentIndex - 1]?.url?.fullSize || displayedImages?.[galleryData.images.length - 1]?.url?.fullSize"
                loading="lazy"
              />
              <ng-container *ngTemplateOutlet="overlay; context: { direction: 'left', isVisible: true }"></ng-container>
            </div>
            <img [alt]="displayedImages?.[currentIndex]?.altText || ''" [src]="displayedImages?.[currentIndex]?.url?.fullSize" loading="lazy" />
            <div class="image-overlay-container">
              <img
                [alt]="displayedImages?.[currentIndex + 1]?.altText || displayedImages?.[0]?.altText || ''"
                [src]="displayedImages?.[currentIndex + 1]?.url?.fullSize || displayedImages?.[0]?.url?.fullSize"
                loading="lazy"
              />
              <ng-container *ngTemplateOutlet="overlay; context: { direction: 'right', isVisible: true }"></ng-container>
            </div>
          </div>
          <div class="gallery-layer-image-caption-container">
            <span class="gallery-layer-image-count">{{ currentIndex + 1 }}/{{ galleryData.images.length }}</span>
            <span class="gallery-layer-image-caption">{{ displayedImages?.[currentIndex]?.caption }}</span>
          </div>
        </div>
        <div class="gallery-swiper">
          <button (click)="swipePrev()" class="gallery-swiper-arrow prev">
            <kesma-icon [size]="24" name="vg-chevron-left"></kesma-icon>
          </button>
          <swiper-container
            #swiper
            [breakpoints]="swiperBreakpoints"
            slides-per-view="3.4"
            slides-per-group="4"
            speed="1000"
            rewind="true"
            center-insufficient-slides="true"
            space-between="24"
          >
            <swiper-slide *ngFor="let gallery of galleryData.images; index as index; trackBy: trackByFn">
              <div class="gallery-swiper-box">
                <img (click)="onThumbnailClick(index)" [alt]="gallery?.altText" [src]="gallery?.url?.thumbnail" class="gallery-swiper-image" />
              </div>
            </swiper-slide>
          </swiper-container>
          <button (click)="swipeNext()" class="gallery-swiper-arrow next">
            <kesma-icon [size]="24" name="vg-chevron-right"></kesma-icon>
          </button>
        </div>
      </ng-container>
      <ng-template #galleryRecommendation>
        <ng-container *ngIf="galleryRecommender$ | async as recommendation">
          <div class="gallery-recommender-header">
            <h2 style="color: white">GALÉRIÁK</h2>

            <div class="gallery-recommender-header-right">
              <div class="gallery-recommender-header-right-arrows">
                <ng-container *ngTemplateOutlet="overlay; context: { direction: 'left' }"></ng-container>
                <ng-container *ngTemplateOutlet="overlay; context: { direction: 'right' }"></ng-container>
              </div>
              <ng-container *ngTemplateOutlet="galleryActions"></ng-container>
            </div>
          </div>
          <div class="gallery-recommender-galleries">
            <ng-container *ngFor="let gallery of recommendation; trackBy: trackByFn">
              <vg-gallery-card [isInsideAdultArticleBody]="galleryData?.isAdult" [data]="gallery"></vg-gallery-card>
            </ng-container>
            <div class="gallery-recommender-swiper-desktop">
              <ng-container *ngTemplateOutlet="overlay; context: { direction: 'left' }"></ng-container>
              <ng-container *ngTemplateOutlet="overlay; context: { direction: 'right' }"></ng-container>
            </div>
          </div>
        </ng-container>
      </ng-template>
      <ng-template #galleryActions>
        <div class="gallery-layer-actions">
          <button (click)="toggleFullScreen(fullScreen)" class="gallery-layer-action-button full-screen">
            <ng-container *ngIf="isFullScreen; else notFullScreen">
              <kesma-icon [size]="24" class="gallery-icons" name="vg-full-size"></kesma-icon>
            </ng-container>
            <ng-template #notFullScreen>
              <kesma-icon [size]="24" class="gallery-icons" name="vg-full-screen-extend"></kesma-icon>
            </ng-template>
          </button>
          <button (click)="goBack()" class="gallery-layer-action-button">
            <kesma-icon [size]="24" class="gallery-icons" name="vg-close"></kesma-icon>
          </button>
        </div>
      </ng-template>
    </section>
  </ng-template>
</ng-container>

<ng-template #overlay let-direction="direction" let-isVisible="isVisible">
  <div *ngIf="isVisible" class="image-overlay-content"></div>
  <button
    (click)="navigate(direction)"
    [class.left-big-size]="!isVisible && direction === 'left'"
    [class.left]="direction === 'left'"
    [class.right-big-size]="!isVisible && direction === 'right'"
    [class.right]="direction === 'right'"
    class="image-overlay-swiper"
  >
    <ng-container *ngIf="direction === 'left'; else right">
      <kesma-icon [size]="24" name="vg-chevron-left"></kesma-icon>
    </ng-container>

    <ng-template #right>
      <kesma-icon [size]="24" name="vg-chevron-right"></kesma-icon>
    </ng-template>
  </button>
</ng-template>
