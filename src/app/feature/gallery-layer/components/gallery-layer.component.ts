import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, ElementRef, Inject, OnDestroy, OnInit, Optional, ViewChild } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { FullscreenDirective, IMetaData, REQUEST, SeoService, StorageService } from '@trendency/kesma-core';
import {
  createCanonicalUrlForPageablePage,
  Gallery,
  GalleryData,
  GalleryDetails,
  getEmailShareUrl,
  getFacebookShareUrl,
  getTwitterShareUrl,
  IconComponent,
  SwiperBaseComponent,
} from '@trendency/kesma-ui';
import { BehaviorSubject, filter, fromEvent, map, Observable, skip, Subject, switchMap, takeUntil, tap } from 'rxjs';
import { AsyncPipe, Location, NgForOf, NgIf, NgTemplateOutlet } from '@angular/common';
import { Clipboard } from '@angular/cdk/clipboard';
import { createVilaggazdasagTitle, isMobileApp, SocialPopoverComponent, UrlService, VgGalleryCardComponent } from '../../../shared';
import { AdultContentComponent } from '../../article-page/components/adult-content/adult-content.component';
import { DateFnsModule } from 'ngx-date-fns';

const LINKEDIN_SHARE_URL = 'https://www.linkedin.com/sharing/share-offsite/?url=';

@Component({
  selector: 'app-gallery-layer',
  templateUrl: './gallery-layer.component.html',
  styleUrls: ['./gallery-layer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    VgGalleryCardComponent,
    SocialPopoverComponent,
    AdultContentComponent,
    NgIf,
    AsyncPipe,
    FullscreenDirective,
    RouterLink,
    IconComponent,
    NgForOf,
    DateFnsModule,
    NgTemplateOutlet,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class GalleryLayerComponent extends SwiperBaseComponent<any> implements OnInit, OnDestroy {
  shareUrl: string;
  /*swiperConfig: SwiperOptions = {
    slidesPerView: 3.4,
    slidesPerGroup: 4,
    speed: 1000,
    rewind: true,
    navigation: true,
    centerInsufficientSlides: true,
    spaceBetween: 24,
    breakpoints: {
      576: {
        slidesPerView: 4,
        slidesPerGroup: 4,
        speed: 1500,
      },
      976: {
        slidesPerView: 5,
        slidesPerGroup: 5,
      },
      1024: {
        slidesPerView: 6,
        slidesPerGroup: 6,
      },
    },
  };*/
  swiperBreakpoints = {
    576: {
      slidesPerView: 4,
      slidesPerGroup: 4,
      speed: 1500,
    },
    976: {
      slidesPerView: 5,
      slidesPerGroup: 5,
    },
    1024: {
      slidesPerView: 6,
      slidesPerGroup: 6,
    },
  };

  @ViewChild('popoverMenu') popoverMenu?: ElementRef;
  isFullScreen = false;
  previousUrl = '';
  facebookUrl = '';
  twitterUrl = '';
  emailUrl = '';
  linkedinUrl = '';
  currentIndex: number;
  isUserAdultChoice: boolean;
  galleryDetails: GalleryDetails;
  galleryData$: Observable<GalleryDetails> = this.activatedRoute.data.pipe(
    map(({ pageData: { galleryDetails } }) => galleryDetails),
    tap((galleryDetails: GalleryDetails) => {
      this.galleryDetails = galleryDetails;
      this.isUserAdultChoice = this.isUserAdultChoiceFromStorage;
      this.getSocialShareUrls(this.shareUrl, this.galleryDetails.title);
      this.setMetaData(galleryDetails);
    })
  );
  unsubscribe$: Subject<boolean> = new Subject<boolean>();
  galleryRecommender$: Observable<GalleryData[]> = this.activatedRoute.data.pipe(map(({ pageData: { recommended } }) => recommended));
  isMobileApp: boolean = false;
  readonly #popoverMenuSubject = new BehaviorSubject<boolean>(false);
  popoverMenu$ = this.#popoverMenuSubject.asObservable();

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly urlService: UrlService,
    private readonly location: Location,
    private readonly seo: SeoService,
    private readonly clipboard: Clipboard,
    private readonly storage: StorageService,
    @Optional() @Inject(REQUEST) private readonly request: Request
  ) {
    super();
    if (this.utils.isBrowser()) {
      this.isMobileApp = isMobileApp(navigator.userAgent || navigator.vendor || (window as any).opera);
    } else {
      let userAgent: string | undefined = '';

      if (this.request?.headers) {
        userAgent = Object.entries(this.request.headers).find((value) => value?.[0] === 'user-agent')?.[1];
      }

      // SSR: use request headers
      this.isMobileApp = isMobileApp(userAgent || '');
    }
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.activatedRoute.params.pipe(takeUntil(this.unsubscribe$)).subscribe((params) => {
      const index = params?.['index'];
      const slug = this.activatedRoute.snapshot.params['slug'];
      this.shareUrl = index && this.seo.hostUrl + `/galeria/${slug}/${index}`;
      this.currentIndex = index ? +(index - 1) : -100;
      if (this.galleryDetails) this.getSocialShareUrls(this.shareUrl, this.galleryDetails?.title);
    });

    this.urlService.previousUrl$.pipe(takeUntil(this.unsubscribe$)).subscribe((previousUrl) => {
      if (previousUrl) {
        this.previousUrl = previousUrl;
      }
    });

    this.popoverMenu$
      .pipe(
        filter((openState) => openState),
        switchMap(() => {
          return fromEvent(document, 'click').pipe(
            skip(1),

            tap((event: Event) => {
              const isEventInMenu = this.popoverMenu?.nativeElement.contains(event.target);
              if (!isEventInMenu) {
                this.togglePopoverMenu(false);
              }
            }),
            takeUntil(this.popoverMenu$.pipe(skip(1)))
          );
        }),
        takeUntil(this.unsubscribe$)
      )
      .subscribe();
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  get isUserAdultChoiceFromStorage(): boolean {
    return this.storage.getSessionStorageData('isAdultChoice', false) ?? false;
  }

  onUserAdultChoose(isAdult: boolean): void {
    this.isUserAdultChoice = isAdult;
  }

  public getSocialShareUrls(galleryLink: string, galleryTitle: string): void {
    this.facebookUrl = getFacebookShareUrl(galleryLink);
    this.twitterUrl = getTwitterShareUrl(galleryLink);
    this.emailUrl = getEmailShareUrl(galleryLink, galleryTitle);
    this.linkedinUrl = LINKEDIN_SHARE_URL + galleryLink;
  }

  togglePopover(): void {
    this.togglePopoverMenu(!this.#popoverMenuSubject.value);
  }

  togglePopoverMenu(value: boolean): void {
    this.#popoverMenuSubject.next(value);
  }

  public navigate(direction: string): void {
    if (direction === 'left') {
      if (this.currentIndex === -100) {
        this.currentIndex = this.galleryDetails?.images?.length - 1;
      } else {
        this.currentIndex--;
      }

      if (this.currentIndex < 0) {
        this.currentIndex = -100;
      }
    } else {
      if (this.currentIndex === -100) {
        this.currentIndex = 0;
      } else {
        this.currentIndex++;
      }

      if (this.currentIndex > this.galleryDetails.images.length - 1) {
        this.currentIndex = -100;
      }
    }

    this.galleryDetails.images?.[this.currentIndex]
      ? this.router
          .navigate([`../${this.currentIndex + 1}`], {
            relativeTo: this.activatedRoute,
            replaceUrl: true,
          })
          .then()
      : this.router
          .navigate(['../galeria-ajanlo'], {
            relativeTo: this.activatedRoute,
            replaceUrl: true,
          })
          .then();
  }

  public onThumbnailClick(index: number): void {
    this.currentIndex = index;
    this.router
      .navigate([`../${++index}`], {
        relativeTo: this.activatedRoute,
        replaceUrl: true,
      })
      .then();
  }

  public goBack(): void {
    if (this.previousUrl) {
      this.location.back();
    } else {
      this.router.navigateByUrl('/galeriak').then();
    }
  }

  public toggleFullScreen(fullScreenDirective: FullscreenDirective): void {
    this.isFullScreen = !this.isFullScreen;
    fullScreenDirective.toggleFullScreen();
  }

  onClickUrlCopy(): void {
    this.clipboard.copy(this.shareUrl);
  }

  override trackByFn = (index: number): number => index;

  private setMetaData(gallery: GalleryDetails): void {
    const title = createVilaggazdasagTitle(gallery.title);
    // TODO: Add the default meta info
    const metaData: IMetaData = {
      title,
      ogTitle: title,
      ogImage: (gallery as unknown as Gallery).highlightedImage.url,
    };
    this.seo.setMetaData(metaData);

    const canonical = createCanonicalUrlForPageablePage('galeria', this.activatedRoute.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);
  }
}
