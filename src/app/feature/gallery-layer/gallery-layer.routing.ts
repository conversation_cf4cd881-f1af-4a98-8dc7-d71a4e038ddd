import { Routes } from '@angular/router';
import { GalleryLayerResolver } from './api/gallery-layer.resolver';
import { GalleryLayerComponent } from './components/gallery-layer.component';

export const GALLERY_LAYER_ROUTES: Routes = [
  {
    path: ':slug',
    redirectTo: ':slug/',
    pathMatch: 'full',
  },
  {
    path: ':slug',
    providers: [GalleryLayerResolver],
    resolve: {
      pageData: GalleryLayerResolver,
    },
    children: [
      {
        path: 'galeria-ajanlo',
        component: GalleryLayerComponent,
      },
      {
        path: ':index',
        component: GalleryLayerComponent,
      },
    ],
  },
  { path: '**', redirectTo: '/404' },
];
