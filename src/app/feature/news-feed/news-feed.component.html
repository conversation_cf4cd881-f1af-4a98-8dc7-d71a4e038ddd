<section class="news-feed">
  <div class="wrapper with-aside">
    <div class="left-column">
      <div class="up-to-date">
        Hírfolyam
        <div class="separator"></div>
        foly<PERSON><PERSON><PERSON> friss<PERSON>l
      </div>

      <div class="flex">
        <h1 class="font-bold-36">{{ dossier?.newsFeedTitle }}</h1>
        <div class="description" *ngIf="dossier?.newsFeedDescription">{{ dossier?.newsFeedDescription }}</div>
        <div class="details">
          <strong>Világgazdaság</strong>
          <div class="separator"></div>
          <div class="dates">
            {{ dossier?.createdAt | publishDate: 'yyyy.MM.dd, HH:mm' }}
            <ng-container *ngIf="articles?.[0]?.lastUpdated as updatedAt">
              <div class="separator"></div>
              Frissítve: {{ updatedAt | publishDate: 'yyyy.MM.dd, HH:mm' }}
            </ng-container>
          </div>
          <app-social-share [emailSubject]="dossier?.title || ''" [linkToCopy]="shareUrl"></app-social-share>
        </div>
      </div>

      <div class="flex g-32">
        <div class="summary">
          <b>Összefoglaló:</b>
          <div class="summary-list">
            <a class="summary-link" routerLink="." [fragment]="article?.slug" *ngFor="let article of articles | slice: 0 : 5">
              <kesma-icon name="vg-bullet" [size]="24"></kesma-icon>
              {{ article?.title }}
            </a>
          </div>
        </div>

        <figure *ngIf="dossier?.coverImage?.thumbnail as thumbnail">
          <img class="thumbnail" [src]="thumbnail" [alt]="dossier?.newsFeedDescription || ''" loading="lazy" />
          <figcaption class="thumbnail-meta">
            <ng-container *ngIf="dossier?.coverImage?.caption as caption">
              {{ caption }}
            </ng-container>
            <div *ngIf="dossier?.coverImage?.photographer as photographer">Fotó: {{ photographer }}</div>
          </figcaption>
        </figure>

        <vg-newsletter-box [styleID]="NewsletterBoxType.Gong" [isListPageView]="true"></vg-newsletter-box>

        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.roadblock_1 as ad"
          [style]="{
            margin: 'var(--ad-margin)',
            padding: 'var(--ad-padding)',
            background: 'var(--ad-bg-light)',
          }"
          [ad]="ad"
        >
        </kesma-advertisement-adocean>

        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.mobilrectangle_1 as ad"
          [style]="{
            margin: 'var(--ad-margin)',
            background: 'var(--ad-bg-light)',
            padding: 'var(--ad-padding)',
          }"
          [ad]="ad"
        >
        </kesma-advertisement-adocean>

        <div class="article" [id]="article.slug" *ngFor="let article of articles">
          <a [routerLink]="buildArticleUrl(article)">
            <h2 class="font-bold-18">{{ article?.title }}</h2>
          </a>
          <div class="article-lead">{{ article?.lead || article?.excerpt }}</div>

          <div class="article-meta">
            <div class="article-author font-bold-16">{{ article?.publicAuthor }}</div>
            <div class="separator mobile"></div>
            <ng-container *ngFor="let tag of article?.tags; index as index; first as first">
              <a class="article-tag" [routerLink]="buildTagUrl(article, index)">{{ tag?.title }}</a>
              <div class="separator"></div>
            </ng-container>
            <div class="article-date">{{ article?.publishDate | publishDate: 'yyyy.MM.dd' }}</div>
          </div>

          <kesma-advertisement-adocean
            *ngIf="adverts?.desktop?.roadblock_2 as ad"
            [ad]="ad"
            [style]="{
              margin: 'var(--ad-margin)',
              background: 'var(--ad-bg-light)',
              padding: 'var(--ad-padding)',
            }"
          >
          </kesma-advertisement-adocean>

          <kesma-advertisement-adocean
            *ngIf="adverts?.mobile?.mobilrectangle_2 as ad"
            [style]="{
              margin: 'var(--ad-margin)',
              background: 'var(--ad-bg-light)',
              padding: 'var(--ad-padding)',
            }"
            [ad]="ad"
          >
          </kesma-advertisement-adocean>

          <!--
          FIXME
          Cikktörzs
          -->
          <div class="body">
            <ng-container *ngFor="let element of article?.body">
              <ng-container [ngSwitch]="element.type">
                <ng-container *ngSwitchCase="ArticleBodyType.Wysywyg">
                  <ng-container *ngFor="let wysiwygDetail of element?.details">
                    <vg-wysiwyg-box [html]="wysiwygDetail?.value || ''" trArticleFileLink></vg-wysiwyg-box>
                  </ng-container>
                </ng-container>

                <ng-container *ngSwitchCase="ArticleBodyType.NewsletterSignUp">
                  <vg-newsletter-box [styleID]="NewsletterBoxType.Basic"></vg-newsletter-box>
                </ng-container>

                <ng-container *ngSwitchCase="ArticleBodyType.NewsletterGongSignUp">
                  <vg-newsletter-box [styleID]="NewsletterBoxType.Gong"></vg-newsletter-box>
                </ng-container>

                <ng-container *ngSwitchCase="ArticleBodyType.MediaVideo">
                  <kesma-article-video [data]="element?.details?.[0]?.value"></kesma-article-video>
                </ng-container>

                <ng-container *ngSwitchCase="ArticleBodyType.TenArticle">
                  <app-article-related-content [data]="getTenArticleRecommenderArticles($any(element))" [withImage]="false"></app-article-related-content>
                </ng-container>

                <ng-container *ngSwitchCase="ArticleBodyType.TwoArticle">
                  <app-article-related-content [data]="twoArticle(element.details)" [withImage]="true"></app-article-related-content>
                </ng-container>

                <ng-container *ngSwitchCase="ArticleBodyType.DoubleArticleRecommendations">
                  <div class="double-title" *ngIf="doubleArticleTitle(element.details)">
                    {{ doubleArticleTitle(element.details).value }}
                  </div>
                  <div class="double-lead" *ngIf="doubleArticleLead(element.details)">
                    {{ doubleArticleLead(element.details).value }}
                  </div>
                  <div class="double-recommendations">
                    <vg-article-card
                      *ngFor="let article of doubleArticleRecommendations(element.details); let i = index"
                      [styleID]="ArticleCardType.RelatedArticle"
                      [data]="doubleArticleRecommendation(article.value)"
                      [hasOutline]="true"
                      [displayLinkIcon]="true"
                    >
                    </vg-article-card>
                  </div>
                </ng-container>

                <ng-container *ngSwitchCase="ArticleBodyType.Voting">
                  <vg-voting
                    (vote)="onVotingSubmit($event, voteData)"
                    *ngIf="getVoteData(element?.details?.[0]?.value) as voteData"
                    [data]="voteData.data"
                    [showResults]="!!voteData.showResults"
                    [sponsored]="voteData?.data?.sponsorship"
                    [voteId]="voteData.votedId"
                  >
                  </vg-voting>
                </ng-container>

                <ng-container *ngSwitchCase="ArticleBodyType.Gallery">
                  <vg-vg-article-slider-gallery
                    *ngIf="galleriesData[element?.details?.[0]?.value?.id]"
                    [data]="galleriesData[element?.details?.[0]?.value?.id]"
                    [highlightedImageId]="element?.details?.[1]?.value?.id"
                    (fullscreenLayerClicked)="openGalleryDedicatedRouteLayer($event)"
                    (slideChanged)="handleGallerySlideChange(galleriesData[element?.details?.[0]?.value?.id], $event)"
                  ></vg-vg-article-slider-gallery>
                </ng-container>

                <ng-container *ngSwitchCase="ArticleBodyType.Stock">
                  <app-exchange-box></app-exchange-box>
                </ng-container>

                <ng-container *ngSwitchCase="ArticleBodyType.SubsequentDossier">
                  <vg-dossier-recommender [data]="dossierRecommender(element?.details?.[0]?.value)"></vg-dossier-recommender>
                </ng-container>
              </ng-container>
            </ng-container>
          </div>

          <div class="article-share">
            <span class="article-share-text">Megosztás</span>
            <app-social-share
              [linkToCopy]="getShareUrl(article)"
              [emailSubject]="article?.title || ''"
              [isNewsFeedType]="true"
              customMobileShareIcon="vg-share-mobile"
              [smallPopup]="(isMobile$ | async) !== true"
              [isBordered]="true"
            >
            </app-social-share>
          </div>
        </div>

        <vg-spinner *ngIf="isLoading" [height]="50" [width]="50"></vg-spinner>

        <button class="more-content" *ngIf="!isLoading && hasNext" (click)="getArticles()">Mutass többet</button>

        <app-external-recommendations
          [roadblock_ottboxextra]="adverts?.desktop?.['roadblock_ottboxextra']"
          [mobilrectangle_ottboxextra]="adverts?.mobile?.['mobilrectangle_ottboxextra']"
        ></app-external-recommendations>

        <vg-newsletter-box [styleID]="NewsletterBoxType.SocialSorted"></vg-newsletter-box>

        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.roadblock_3 as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
            background: 'var(--ad-bg-light)',
            padding: 'var(--ad-padding)',
          }"
        >
        </kesma-advertisement-adocean>

        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.mobilrectangle_3 as ad"
          [style]="{
            margin: 'var(--ad-margin)',
            background: 'var(--ad-bg-light)',
            padding: 'var(--ad-padding)',
          }"
          [ad]="ad"
        >
        </kesma-advertisement-adocean>
        <app-recommendation-box [recommendations$]="recommendations$"></app-recommendation-box>

        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.roadblock_4 as ad"
          [ad]="ad"
          [style]="{
            margin: 'var(--ad-margin)',
            background: 'var(--ad-bg-light)',
            padding: 'var(--ad-padding)',
          }"
        >
        </kesma-advertisement-adocean>

        <kesma-advertisement-adocean
          *ngIf="adverts?.mobile?.mobilrectangle_4 as ad"
          [style]="{
            margin: 'var(--ad-margin)',
            background: 'var(--ad-bg-light)',
            padding: 'var(--ad-padding)',
          }"
          [ad]="ad"
        >
        </kesma-advertisement-adocean>
      </div>
    </div>
    <aside>
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>

<app-copy-box *ngIf="urlCopied$ | async as isSlided" [isSlided]="isSlided"></app-copy-box>
