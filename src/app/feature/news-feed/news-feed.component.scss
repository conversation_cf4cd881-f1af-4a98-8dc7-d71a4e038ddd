@use 'shared' as *;

:host {
  display: block;
  color: var(--kui-gray-950);
  padding-bottom: 56px;

  .left-column {
    @extend %flex-column;
    gap: 24px;

    @include media-breakpoint-down(md) {
      gap: 16px;
    }
  }

  .separator {
    width: 1px;
    background-color: var(--kui-gray-100);
  }

  .up-to-date {
    padding: 8px 24px;
    border: 1px solid var(--kui-deep-teal-400);
    color: var(--kui-deep-teal-400);
    font-size: 16px;
    font-weight: 700;
    line-height: 22px;
    @extend %flex-center;
    gap: 8px;

    .separator {
      height: 20px;
      background-color: var(--kui-deep-teal-400);
    }
  }

  .description {
    font-size: 24px;
    font-weight: 500;

    @include media-breakpoint-down(md) {
      font-size: 20px;
      letter-spacing: 0.2px;
    }
  }

  .flex {
    @extend %flex-column;
    gap: 16px;

    &.g-32 {
      gap: 32px;

      @include media-breakpoint-down(md) {
        gap: 24px;
      }
    }
  }

  .details {
    @extend %flex-center;
    flex-wrap: wrap;
    gap: 12px;
    font-size: 16px;
    line-height: 22px;

    @include media-breakpoint-down(md) {
      font-size: 14px;
      line-height: 18px;
    }

    app-social-share ::ng-deep {
      width: 188px;

      &.share {
        span {
          font-size: 16px;
          line-height: 24px;
          font-weight: 400;
        }
      }

      &.popup {
        width: 188px;
      }

      @include media-breakpoint-down(md) {
        display: none;
      }
    }

    .separator {
      height: 12px;
    }

    .dates {
      @extend %flex-center;
      flex-wrap: wrap;
      gap: 12px;
      margin-right: auto;
    }

    /*.share {
      @extend %flex-center;
      padding: 4px 8px;
      border: 1px solid var(--kui-gray-50);
      gap: 8px;
      transition: all .5s;
      cursor: pointer;

      &:hover {
        background-color: var(--kui-deep-teal-400);
      }
    }*/
  }

  .summary {
    background-color: var(--kui-deep-teal-50);
    padding: 24px 8px;
    @extend %flex-column;
    gap: 32px;
    font-weight: 700;
    font-size: 20px;
    line-height: 26px;
    letter-spacing: 0.4px;

    @include media-breakpoint-down(md) {
      font-size: 18px;
      line-height: 22px;
      padding: 16px 24px;
      margin-inline: -24px;
      width: calc(100% + 48px);
      gap: 8px;
    }

    &-list {
      @extend %flex-column;
      gap: 16px;
    }

    &-link {
      display: flex;
      gap: 8px;

      &:hover {
        text-decoration: underline transparent solid 1px;
        text-decoration-color: var(--kui-deep-teal-400);
        text-underline-offset: 5px;
      }

      kesma-icon {
        color: var(--kui-deep-teal-400);
        flex-shrink: 0;
      }

      @include media-breakpoint-down(md) {
        font-size: 16px;
      }
    }
  }

  .thumbnail {
    margin-bottom: 16.47px;
    object-fit: cover;
    width: 100%;
    max-height: 480px;

    @include media-breakpoint-down(md) {
      aspect-ratio: 16/9;
      margin-bottom: 8px;
    }

    &-meta {
      font-size: 14px;
      font-weight: 400;
      line-height: 18px;

      @include media-breakpoint-down(md) {
        font-size: 12px;
        line-height: 14px;
      }
    }
  }

  .article {
    padding: 24px;
    border: 1px solid var(--kui-deep-teal-400);
    @extend %flex-column;
    gap: 16px;

    .font-bold-18 {
      color: var(--kui-deep-teal-900);
      margin-bottom: -8px;

      &:hover {
        color: var(--kui-purple-600);
      }
    }

    &-lead {
      font-size: 18px;
      font-weight: 400;
      line-height: 24px;
      letter-spacing: 0.36px;
    }

    &-meta {
      @extend %flex-center;
      flex-wrap: wrap;
      padding-bottom: 8px;
      border-bottom: 1px solid var(--kui-deep-teal-400);
      gap: 16px;
    }

    &-author {
      white-space: nowrap;

      @include media-breakpoint-down(md) {
        font-size: 14px;
        line-height: 18px;
      }

      @include media-breakpoint-up(sm) {
        margin-right: auto;
      }
    }

    &-share {
      @extend %flex-center;
      gap: 24px;

      &-text {
        font-size: 14px;
        line-height: 18px;
        color: var(--kui-deep-teal-950);

        @include media-breakpoint-down(sm) {
          display: none;
        }
      }
    }

    &-tag {
      font-size: 14px;
      line-height: 18px;

      &:first-of-type {
        font-weight: bold;
      }

      &:hover {
        color: var(--kui-deep-teal-400);
      }
    }

    .separator {
      height: 10px;

      @include media-breakpoint-up(sm) {
        &.mobile {
          display: none;
        }
      }
    }

    ::ng-deep .body {
      .block-content {
        margin-bottom: 0;

        figcaption {
          padding-top: 0;
        }

        // Ckeditor styles.
        > * {
          margin-bottom: 16px !important;
        }
      }

      // Ckeditor custom components.
      > * {
        margin-bottom: 16px;
      }
    }
  }

  .more-content {
    font-size: 14px;
    font-weight: 700;
    line-height: 18px;
    color: var(--kui-deep-teal-400);
    padding: 8px;
    border: 1px solid var(--kui-deep-teal-400);
    transition: all 0.5s;

    &:hover {
      background-color: var(--kui-deep-teal-400);
      color: var(--kui-white);
    }
  }

  @include media-breakpoint-down(md) {
    .font-bold-36 {
      font-size: 28px;
      line-height: normal;
      letter-spacing: 0.56px;
    }
  }

  ::ng-deep {
    app-article-related-content > .related-content-wrapper,
    app-article-related-content > .related-content-wrapper-img,
    app-recommendation-box > .external-recommendations {
      margin-bottom: 0;
    }
  }
}

%flex-column {
  display: flex;
  flex-direction: column;
}

%flex-center {
  display: flex;
  align-items: center;
}
