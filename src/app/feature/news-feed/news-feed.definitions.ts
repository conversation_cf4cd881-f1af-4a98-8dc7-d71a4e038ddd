import { ApiResponseMetaList } from '@trendency/kesma-ui';

export type NewsFeedDossier = Readonly<{
  slug: string;
  title: string;
  description: string;
  newsFeedDescription: string;
  newsFeedTitle: string;
  coverImage: NewsFeedDosserImage;
  createdAt: string;
}>;

export type NewsFeedDosserImage = Readonly<{
  caption?: string;
  photographer?: string;
  source?: string;
  thumbnail?: string;
}>;

export type NewsFeedApiResponseMeta = ApiResponseMetaList &
  Readonly<{
    newsFeed: NewsFeedDossier;
  }>;
