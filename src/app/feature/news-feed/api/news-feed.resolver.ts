import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot } from '@angular/router';
import { ApiResult, Article } from '@trendency/kesma-ui';
import { Observable, share } from 'rxjs';
import { NewsFeedService } from './news-feed.service';
import { NewsFeedApiResponseMeta } from '../news-feed.definitions';

@Injectable()
export class NewsFeedResolver {
  constructor(private readonly newsFeedService: NewsFeedService) {}

  resolve(route: ActivatedRouteSnapshot): Observable<ApiResult<Article[], NewsFeedApiResponseMeta>> {
    const { dossierSlug } = route.params;

    return this.newsFeedService.getNewsFeed(dossierSlug).pipe(share());
  }
}
