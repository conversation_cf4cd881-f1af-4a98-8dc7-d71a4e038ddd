import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { ApiResult, Article } from '@trendency/kesma-ui';
import { Observable, throwError } from 'rxjs';
import { NewsFeedApiResponseMeta } from '../news-feed.definitions';
import { Router } from '@angular/router';
import { catchError } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class NewsFeedService {
  constructor(
    private readonly reqService: ReqService,
    private readonly router: Router
  ) {}

  getNewsFeed(slug: string, pageLimit = 0): Observable<ApiResult<Article[], NewsFeedApiResponseMeta>> {
    return this.reqService
      .get<ApiResult<Article[], NewsFeedApiResponseMeta>>(`/content-group/news-feed/${slug}`, {
        params: {
          page_limit: pageLimit.toString(),
          rowCount_limit: '5',
        },
      })
      .pipe(
        catchError((error) => {
          this.router.navigate(['/404'], { state: { errorResponse: JSON.stringify(error || {}) }, skipLocationChange: true }).then();

          return throwError(() => error);
        })
      );
  }
}
