import { Routes } from '@angular/router';
import { NewsFeedComponent } from './news-feed.component';
import { NewsFeedResolver } from './api/news-feed.resolver';
import { RecommendationsResolver } from '../../shared';

export const NEWS_FEED_ROUTING: Routes = [
  {
    path: '',
    component: NewsFeedComponent,
    providers: [NewsFeedResolver, RecommendationsResolver],
    resolve: {
      data: NewsFeedResolver,
      recommendations: RecommendationsResolver,
    },
  },
];
