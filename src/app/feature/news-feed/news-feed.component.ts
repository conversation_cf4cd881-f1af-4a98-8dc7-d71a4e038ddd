import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, On<PERSON><PERSON>roy, OnInit, Optional } from '@angular/core';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { PublishDatePipe, REQUEST, SeoService, UtilService } from '@trendency/kesma-core';
import {
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  AnalyticsService,
  Article,
  ArticleCard,
  ArticleFileLinkDirective,
  ArticleVideoComponent,
  buildArticleUrl,
  buildTagUrl,
  createCanonicalUrlForPageablePage,
  IconComponent,
  LimitableMeta,
  RoutingHelperService,
  VoteService,
} from '@trendency/kesma-ui';
import { combineLatest, fromEvent, Observable, of, Subject, timer } from 'rxjs';
import { map, startWith, switchMap, takeUntil, tap } from 'rxjs/operators';
import {
  ArticleCardType,
  ArticleRelatedContentComponent,
  BaseArticlePageComponent,
  CopyBoxComponent,
  createVilaggazdasagTitle,
  defaultMetaInfo,
  ExchangeBoxComponent,
  ExternalRecommendationsComponent,
  GalleryService,
  NewsletterBoxType,
  RecommendationBoxComponent,
  SocialShareComponent,
  VgArticleCardComponent,
  VgArticleSliderGalleryComponent,
  VgDossierRecommenderComponent,
  VgNewsletterBoxComponent,
  VgSpinnerComponent,
  VgVotingComponent,
  VgWysiwygBoxComponent,
} from '../../shared';
import { NewsFeedService } from './api/news-feed.service';
import { NewsFeedDossier } from './news-feed.definitions';
import { SidebarComponent } from '../layout/components/sidebar/sidebar.component';
import { AsyncPipe, NgFor, NgIf, NgSwitch, NgSwitchCase, SlicePipe } from '@angular/common';

@Component({
  selector: 'app-news-feed',
  templateUrl: './news-feed.component.html',
  styleUrls: ['./news-feed.component.scss', '../article-page/components/article-page/article-page-shared.components.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    SocialShareComponent,
    NgFor,
    RouterLink,
    NgSwitch,
    NgSwitchCase,
    ArticleRelatedContentComponent,
    ExchangeBoxComponent,
    RecommendationBoxComponent,
    SidebarComponent,
    CopyBoxComponent,
    AsyncPipe,
    SlicePipe,
    VgSpinnerComponent,
    VgNewsletterBoxComponent,
    VgDossierRecommenderComponent,
    VgArticleSliderGalleryComponent,
    VgVotingComponent,
    VgArticleCardComponent,
    VgWysiwygBoxComponent,
    PublishDatePipe,
    IconComponent,
    AdvertisementAdoceanComponent,
    ArticleVideoComponent,
    ArticleFileLinkDirective,
    ExternalRecommendationsComponent,
  ],
})
export class NewsFeedComponent extends BaseArticlePageComponent implements OnInit, OnDestroy {
  dossier?: NewsFeedDossier;
  articles?: Article[];
  adverts?: AdvertisementsByMedium;
  limitableMeta?: LimitableMeta;
  isLoading?: boolean;
  shareUrl?: string;

  pageLimit = 0;

  recommendations$: Observable<ArticleCard[]>;

  readonly NewsletterBoxType = NewsletterBoxType;
  readonly ArticleCardType = ArticleCardType;
  readonly buildArticleUrl = buildArticleUrl;
  readonly buildTagUrl = buildTagUrl;

  readonly #unsubscribe$: Subject<boolean> = new Subject();
  isMobile$ = this.utils.isBrowser()
    ? fromEvent(window, 'resize').pipe(
        map(() => window.innerWidth),
        startWith(window.innerWidth),
        map((width: number) => width <= 768),
        takeUntil(this.#unsubscribe$)
      )
    : of(false);
  readonly #urlCopiedSubject$: Subject<void> = new Subject<void>();
  urlCopied$ = this.#urlCopiedSubject$.pipe(
    switchMap(() => {
      return timer(3000).pipe(
        map(() => false),
        startWith(true)
      );
    })
  );

  constructor(
    protected override readonly cdr: ChangeDetectorRef,
    protected override readonly voteService: VoteService,
    protected override readonly galleryService: GalleryService,
    protected override readonly router: Router,
    protected override readonly utils: UtilService,
    private readonly route: ActivatedRoute,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly newsFeedService: NewsFeedService,
    private readonly routingHelperService: RoutingHelperService,
    protected override readonly seo: SeoService,
    protected override readonly analyticsService: AnalyticsService,
    @Optional() @Inject(REQUEST) protected override readonly request: Request
  ) {
    super(voteService, cdr, galleryService, router, utils, seo, analyticsService, request);
  }

  public get hasNext(): boolean {
    return (this.articles?.length || -1) < (this.limitableMeta?.rowAllCount || -1);
  }

  ngOnInit(): void {
    combineLatest([this.route.data, this.adStoreAdo.advertisemenets$])
      .pipe(takeUntil(this.#unsubscribe$))
      .subscribe(([{ data, recommendations }, advertisements]) => {
        this.recommendations$ = of(recommendations);
        this.articles = data.data;
        this.limitableMeta = data.meta?.limitable;
        this.dossier = data.meta?.newsFeed;
        this.adverts = this.adStoreAdo.separateAdsByMedium(advertisements);
        this.shareUrl = this.routingHelperService.resolveLink(['/', 'hirfolyam', this.dossier?.slug as string]);
        this.#setMetaData();
        this.articles?.forEach((article) => this.loadEmbeddedGalleries(article));
        this.cdr.detectChanges();
      });
  }

  ngOnDestroy(): void {
    this.#unsubscribe$.next(true);
    this.#unsubscribe$.complete();
  }

  getArticles(): void {
    this.isLoading = true;
    this.newsFeedService
      .getNewsFeed(this.dossier?.slug as string, ++this.pageLimit)
      .pipe(
        tap(() => (this.isLoading = false)),
        takeUntil(this.#unsubscribe$)
      )
      .subscribe(({ data }) => {
        this.articles = [...this.articles!, ...data];
        data?.forEach((article) => this.loadEmbeddedGalleries(article));
        this.cdr.markForCheck();
      });
  }

  getShareUrl(article: Article): string {
    return this.routingHelperService.resolveLink(buildArticleUrl(article));
  }

  #setMetaData(): void {
    const title = createVilaggazdasagTitle('Hírfolyam');
    const canonical = createCanonicalUrlForPageablePage('hirfolyam', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);
    this.seo.setMetaData({
      ...defaultMetaInfo,
      ogTitle: title,
      title,
    });
  }

  #mapArticleToOttbox(articles: ArticleCard[]): ArticleCard[] {
    return articles.map((article) => ({
      ...article,
      columnEmphasizeOnArticleCard: true,
      category: {
        ...article.category,
        name: 'VG.hu',
      },
    }));
  }
}
