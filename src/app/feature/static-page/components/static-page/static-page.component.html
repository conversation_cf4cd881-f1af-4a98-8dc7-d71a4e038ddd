<section class="static-page" *ngIf="!customStaticPageType || customStaticPageType === CustomStaticPageType.StaticPage">
  <div class="wrapper">
    <div class="content">
      <div class="heading-line">
        <h1 class="title">{{ title }}</h1>
      </div>
      <ng-container *ngFor="let element of body">
        <ng-container [ngSwitch]="element.type">
          <div *ngIf="element">
            <div *ngFor="let item of element?.details" [innerHTML]="item?.value | bypass: 'html'" class="text-formatter"></div>
          </div>
        </ng-container>
      </ng-container>
    </div>
  </div>
</section>

<section *ngIf="customStaticPageType === CustomStaticPageType.CustomPage">
  <div class="wrapper">
    <app-layout [structure]="layoutApiData.struct" [configuration]="layoutApiData.content"></app-layout>
  </div>
</section>
