@use 'shared' as *;

.static-page {
  padding: 64px 0;

  .content {
    max-width: 1070px;
    margin: 0 auto;
  }

  .title {
    color: var(--kui-deep-teal-900);
    margin-bottom: 16px;
  }

  .underlined-text {
    text-decoration: underline;
  }

  .text-formatter {
    p {
      font-size: 16px;
      margin-bottom: 16px;
      line-height: 24px;
    }

    a {
      color: var(--kui-deep-teal-600);
      font-weight: 600;
      text-decoration-line: underline;
    }

    hr {
      background-color: var(--kui-deep-teal-800);
      border: none;
      height: 1px;
    }

    h3 {
      font-size: 1.75rem;
    }

    h4 {
      font-size: 1.5rem;
    }

    li {
      margin: 15px 0;
    }

    table {
      margin: auto;
      td {
        padding: 12px 15px;
        border: 1px solid var(--kui-deep-teal-800);
      }
    }

    .image {
      display: block;
      max-width: 100%;

      img {
        width: 100%;
        height: auto;
      }

      figcaption {
        text-align: center;
        opacity: 0.5;
        padding-top: 5px;
        font-size: 13px;
      }
    }

    .raw-html-embed {
      // Do not use flex here, because some 3rd party stuff (iframe.ly) doesn't like it
      display: block;
      > * {
        margin: 0 auto;
      }
    }
  }
}
