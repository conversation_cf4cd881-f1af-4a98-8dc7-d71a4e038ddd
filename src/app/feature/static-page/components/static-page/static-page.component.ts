import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { EmbeddingService, SeoService } from '@trendency/kesma-core';
import {
  AnalyticsService,
  ApiResponseStaticPageMeta,
  ApiResult,
  BypassPipe,
  createCanonicalUrlForPageablePage,
  LayoutApiData,
  StaticPage,
} from '@trendency/kesma-ui';
import { createVilaggazdasagTitle, defaultMetaInfo } from '../../../../shared';
import { CustomStaticPageType, IComponentData, StaticPageResponse } from '../../api/static-page.definitions';
import { Subject, takeUntil } from 'rxjs';
import { LayoutComponent } from '../../../layout/components/layout/layout.component';
import { <PERSON><PERSON><PERSON>, NgIf, NgSwitch } from '@angular/common';

@Component({
  selector: 'app-static-page',
  templateUrl: './static-page.component.html',
  styleUrls: ['./static-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
  imports: [NgIf, NgFor, NgSwitch, LayoutComponent, BypassPipe],
})
export class StaticPageComponent implements OnInit, AfterViewInit, OnDestroy {
  public title: string;
  public body: IComponentData[];
  public staticPageResponse: StaticPageResponse;
  public customStaticPageType: CustomStaticPageType;
  public layoutApiData: LayoutApiData;

  readonly CustomStaticPageType = CustomStaticPageType;

  unsubscribe$: Subject<void> = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly embedding: EmbeddingService,
    private readonly seo: SeoService,
    private readonly cd: ChangeDetectorRef,
    private readonly analyticsService: AnalyticsService
  ) {}

  ngOnInit(): void {
    this.route.data.pipe(takeUntil(this.unsubscribe$)).subscribe((res) => {
      if (!res?.['staticPageData']) {
        return;
      }
      const { data, meta } = res['staticPageData'] as ApiResult<StaticPage | LayoutApiData, ApiResponseStaticPageMeta>;
      this.customStaticPageType = meta?.customStaticPageType;
      if (!this.customStaticPageType || this.customStaticPageType === CustomStaticPageType.StaticPage) {
        this.staticPageResponse = data as StaticPage;
        this.body = this.staticPageResponse?.body as IComponentData[];
        this.title = this.staticPageResponse?.title;
        this.analyticsService.sendPageView(undefined, 'Statikus oldal');
      }

      if (this.customStaticPageType === CustomStaticPageType.CustomPage) {
        this.title = meta.customBuiltPageTitle;
        this.layoutApiData = data as LayoutApiData;
        this.analyticsService.sendPageView(undefined, 'Egyedi oldal');
      }

      const canonical = createCanonicalUrlForPageablePage('', this.route.snapshot);
      if (canonical) {
        this.seo.updateCanonicalUrl(canonical);
      }

      this.seo.setMetaData({
        ...defaultMetaInfo,
        title: createVilaggazdasagTitle(this.title),
        ogTitle: this.title,
      });

      this.cd.markForCheck();
    });
  }

  ngAfterViewInit(): void {
    this.embedding.loadEmbedMedia();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
