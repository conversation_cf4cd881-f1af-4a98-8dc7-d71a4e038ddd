import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import {
  ApiResponseMetaList,
  ApiResult,
  ArticleCard,
  ArticleResolverData,
  ArticleRouteParams,
  ArticleSearchResult,
  BackendArticleSearchResult,
  backendBoolToBool,
  mapBackendArticleDataToArticleCard,
  RecommendationsData,
} from '@trendency/kesma-ui';
import { forkJoin, Observable, of, share, switchMap, throwError } from 'rxjs';
import { ArticleService } from './article-page.service';
import { catchError, map } from 'rxjs/operators';
import { ApiService } from '../../../shared';

@Injectable()
export class ArticleResolver {
  constructor(
    private readonly articleService: ArticleService,
    private readonly router: Router,
    private readonly apiService: ApiService
  ) {}

  resolve(route: ActivatedRouteSnapshot): Observable<
    ArticleResolverData & {
      opinions?: ArticleCard[];
      videos?: ArticleCard[];
      podcasts?: ArticleCard[];
    }
  > {
    const foundationTagSlug = route.params['slug'];
    const params: ArticleRouteParams = route.params as ArticleRouteParams;
    const previewHash = params.previewHash;
    let { year, month } = params;
    const categorySlug = params?.categorySlug || (route.data as ArticleRouteParams)?.categorySlug;
    const articleSlug = params.previewHash ? 'cikk-elonezet' : params.articleSlug;
    const previewType = params.previewType ? params.previewType : 'accepted';

    const isYear = !isNaN(parseInt(year as string));
    year = isYear && year ? year : undefined;
    month = isYear && month ? month : undefined;

    if ((!year || !month) && !previewHash && !foundationTagSlug) {
      this.router
        .navigate(['/', '404'], {
          skipLocationChange: true,
        })
        .then();
    }

    const url = foundationTagSlug ? `${foundationTagSlug}` : `${categorySlug}/${year}/${month}/${articleSlug}`;

    let request$: Observable<any>;
    const opinionArticles$ = this.apiService
      .searchByKeyword(0, 5, { 'content_types[]': 'opinion' })
      .pipe(map(({ data }) => data.map((article: BackendArticleSearchResult) => mapBackendArticleDataToArticleCard(article))))
      .pipe(share());

    const videoArticles$ = this.apiService
      .searchByKeyword(0, 3, { 'content_types[]': 'articleVideo' })
      .pipe(map(({ data }) => data.map((article: BackendArticleSearchResult) => mapBackendArticleDataToArticleCard(article))))
      .pipe(share());

    const podcastArticles$ = this.apiService
      .searchByKeyword(0, 4, { 'content_types[]': 'articlePodcast' })
      .pipe(map(({ data }) => data.map((article: BackendArticleSearchResult) => mapBackendArticleDataToArticleCard(article))))
      .pipe(share());

    if (previewHash) {
      request$ = forkJoin({
        article: this.articleService.getArticlePreview(articleSlug, previewHash || '', previewType),
        recommendations: of({} as ApiResult<RecommendationsData>),
        newestArticles: of([] as ArticleCard[]),
        articleSlug: of(articleSlug),
        categorySlug: of(categorySlug),
        opinions: of([] as ArticleCard[]),
        videos: of([] as ArticleCard[]),
        podcasts: of([] as ArticleCard[]),
      });
    } else {
      request$ = (foundationTagSlug ? this.apiService.getArticlesByFoundationTag(foundationTagSlug) : of({})).pipe(
        switchMap((searchResult: ApiResult<ArticleSearchResult[], ApiResponseMetaList> | any) => {
          let foundationCategorySlug, foundationYear, foundationMonth, foundationArticleSlug, firstArticleWithFoundationTag;
          if (foundationTagSlug) {
            firstArticleWithFoundationTag = searchResult?.data?.[0];
            if (firstArticleWithFoundationTag) {
              foundationCategorySlug = firstArticleWithFoundationTag?.columnSlug;
              foundationYear = firstArticleWithFoundationTag.publishDate.split('-')[0];
              foundationMonth = firstArticleWithFoundationTag.publishDate.split('-')[1];
              foundationArticleSlug = firstArticleWithFoundationTag?.slug;
            }
          }

          if (!(foundationTagSlug && firstArticleWithFoundationTag) && !categorySlug) {
            return of(null);
          }

          // share() operator provides only 1 request from BE
          const article$ = (
            foundationTagSlug && firstArticleWithFoundationTag
              ? this.articleService.getArticle(foundationCategorySlug ?? '', foundationYear ?? '', foundationMonth ?? '', foundationArticleSlug ?? '')
              : this.articleService.getArticle(categorySlug, year as string, month as string, articleSlug)
          ).pipe(share());

          return forkJoin({
            article: this.getArticleWithOpinions(article$),
            recommendations: of({} as ApiResult<RecommendationsData>),
            newestArticles: this.apiService
              .getArticles(3)
              .pipe(map(({ data }) => data.map((article: BackendArticleSearchResult) => mapBackendArticleDataToArticleCard(article)))),
            articleSlug: of(foundationTagSlug ? searchResult?.data?.[0]?.slug : articleSlug),
            categorySlug: of(foundationTagSlug ? searchResult?.data?.[0]?.categorySlug : categorySlug),
            year: of(foundationTagSlug ? foundationYear : year),
            month: of(foundationTagSlug ? foundationMonth : month),
            url: of(url),
            foundationTagSlug: of(foundationTagSlug),
            foundationTagTitle: of(foundationTagSlug ? searchResult?.data?.[0]?.foundationTagTitle : undefined),
            opinions: opinionArticles$,
            videos: videoArticles$,
            podcasts: podcastArticles$,
          });
        })
      );
    }

    return request$.pipe(
      catchError(() => {
        this.router
          .navigate(['/', '404'], {
            skipLocationChange: true,
          })
          .then();
        return throwError(() => null);
      })
    );
  }

  private getArticleWithOpinions(article$: Observable<any>): Observable<any> {
    return article$.pipe(
      switchMap((article) => {
        if (!article?.data?.isOpinion) {
          return of(article);
        }

        const publicAuthor = article.data?.publicAuthor || 'Vilaggazdasag';
        return this.apiService.getOpinionAuthor(publicAuthor).pipe(
          map((articles) => ({
            ...article,
            articles: {
              ...articles,
              data: articles.data.map((opinionArticle: any) => ({
                ...opinionArticle,
                isVideoType: backendBoolToBool(opinionArticle?.isVideoType),
                isPodcastType: backendBoolToBool(opinionArticle?.isPodcastType),
                hasGallery: backendBoolToBool(opinionArticle?.hasGallery),
                isOpinion: backendBoolToBool(opinionArticle?.isOpinionType),
              })),
            },
          }))
        );
      })
    );
  }
}
