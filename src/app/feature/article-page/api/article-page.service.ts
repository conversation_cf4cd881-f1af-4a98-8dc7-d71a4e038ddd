import { Injectable } from '@angular/core';
import { ReqService } from '@trendency/kesma-core';
import { ApiResult, Article, BackendArticle, BackendRecommendationsData, mapBackendArticleDataToArticleCard, RecommendationsData } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import { ApiService, backendArticlesToArticles, backendRecommendedArticleToArticleCard, externalRecommendationToArticleCard } from '../../../shared';

@Injectable({
  providedIn: 'root',
})
export class ArticleService {
  constructor(
    private readonly reqService: ReqService,
    private readonly apiService: ApiService
  ) {}

  getArticlePreview(articleSlug: string, previewHash: string, previewType: string): Observable<ApiResult<Article>> {
    const timestamp = Math.floor(new Date().getTime());
    return this.reqService
      .get<ApiResult<BackendArticle>>(`/content-page/article/${articleSlug}/preview/view?previewHash=${previewHash}&t=${timestamp.toString()}`, {
        params: {
          previewType,
        },
      })
      .pipe(
        map(({ data, meta }) => ({
          data: backendArticlesToArticles(data),
          meta,
        }))
      );
  }

  getArticle(category: string, year: string, month: string, articleSlug: string): Observable<ApiResult<Article>> {
    return this.reqService.get<ApiResult<BackendArticle>>(`/content-page/article/${category}/${year}/${(month + '').padStart(2, '0')}/${articleSlug}`).pipe(
      map(({ data, meta }: ApiResult<BackendArticle>) => ({
        data: backendArticlesToArticles(data),
        meta,
      }))
    );
  }

  getArticleRecommendations(articleSlug: string, categorySlug?: string): Observable<ApiResult<RecommendationsData>> {
    if (categorySlug) {
      return this.apiService.getCategoryArticles(categorySlug, 0, 5).pipe(
        switchMap((categoryArticles) => {
          return this.reqService
            .get<
              ApiResult<BackendRecommendationsData>
            >(`/content-page/article/${articleSlug}/recommendation?fields[]=foundationTagSlug&fields[]=foundationTagTitle`)
            .pipe(
              map(({ data, meta }: ApiResult<BackendRecommendationsData>) => ({
                data: {
                  ...data,
                  categoryArticles: categoryArticles.data.map(mapBackendArticleDataToArticleCard),
                  highPriorityArticles: data.highPriorityArticles?.map(backendRecommendedArticleToArticleCard),
                  lowPriorityArticles: data.lowPriorityArticles?.map(backendRecommendedArticleToArticleCard),
                  externalRecommendation: data.externalRecommendation?.map(externalRecommendationToArticleCard),
                  lastThreeDaysMostReadArticles: data.lastThreeDaysMostReadArticles?.map(backendRecommendedArticleToArticleCard),
                },
                meta,
              }))
            );
        })
      );
    } else {
      return this.reqService
        .get<
          ApiResult<BackendRecommendationsData>
        >(`/content-page/article/${articleSlug}/recommendation?fields[]=foundationTagSlug&fields[]=foundationTagTitle`)
        .pipe(
          map(({ data, meta }: ApiResult<BackendRecommendationsData>) => ({
            data: {
              ...data,
              highPriorityArticles: data.highPriorityArticles?.map(backendRecommendedArticleToArticleCard),
              lowPriorityArticles: data.lowPriorityArticles?.map(backendRecommendedArticleToArticleCard),
              externalRecommendation: data.externalRecommendation?.map(externalRecommendationToArticleCard),
              lastThreeDaysMostReadArticles: data.lastThreeDaysMostReadArticles?.map(backendRecommendedArticleToArticleCard),
            },
            meta,
          }))
        );
    }
  }
}
