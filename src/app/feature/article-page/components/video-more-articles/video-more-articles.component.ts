import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ArticleCard, BaseComponent, BlockTitle } from '@trendency/kesma-ui';
import { NgFor, SlicePipe } from '@angular/common';
import { ArticleCardType, VgBlockTitleComponent, VgVideoCardComponent } from '../../../../shared';

@Component({
  selector: 'app-video-more-articles',
  templateUrl: './video-more-articles.component.html',
  styleUrls: ['./video-more-articles.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgFor, SlicePipe, VgVideoCardComponent, VgBlockTitleComponent],
})
export class VideoMoreArticlesComponent extends BaseComponent<ArticleCard[]> {
  readonly ArticleCardType = ArticleCardType;

  blockTitle: BlockTitle = {
    text: 'Aj<PERSON>lott videók',
    urlName: 'To<PERSON><PERSON><PERSON><PERSON><PERSON>',
    url: '/videok',
  };
}
