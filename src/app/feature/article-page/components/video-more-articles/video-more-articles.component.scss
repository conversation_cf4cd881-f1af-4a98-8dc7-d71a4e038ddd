@use 'shared' as *;

:host {
  display: block;

  vg-block-title {
    margin-bottom: 16px;

    &::ng-deep {
      .block-url-wide {
        width: 136px !important;
      }
    }
  }

  vg-video-card {
    height: 100%;
    &::ng-deep {
      .video-card {
        &-title {
          font-size: 16px;
          font-weight: 700;
          line-height: 22px;
          color: var(--kui-gray-950);

          @include media-breakpoint-down(md) {
            font-size: 14px;
            line-height: 18px;
          }
        }

        &-link-wrapper {
          width: 100%;
        }

        &-thumbnail-box {
          @include media-breakpoint-down(md) {
            width: 136px;
          }
        }
      }
    }
  }

  .video-articles {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 32px;
    margin-bottom: 32px;

    @include media-breakpoint-down(lg) {
      grid-template-columns: repeat(2, 1fr);
      margin-bottom: 24px;
      gap: 24px;
    }

    @include media-breakpoint-down(xs) {
      grid-template-columns: 1fr;
    }
  }
}
