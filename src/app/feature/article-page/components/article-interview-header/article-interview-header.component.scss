@use 'shared' as *;

:host {
  .upper {
    background-color: var(--kui-gray-950);

    .separator {
      width: 1px;
      height: 16px;
      background-color: var(--kui-gray-700);
    }

    &-inner {
      max-width: 1096px;
      display: flex;
      gap: 24px;
      margin: 0 auto;

      @include media-breakpoint-down(sm) {
        flex-direction: column;
      }
    }
  }

  .article {
    &-data {
      &-wrapper {
        display: flex;
        flex-direction: column;
        gap: 25px;
        margin: auto 0;
        width: calc(50% - 12px);
        color: var(--kui-white);

        @include media-breakpoint-down(md) {
          padding-left: 24px;
        }

        @include media-breakpoint-down(sm) {
          padding: 16px 24px 0 24px;
          width: 100%;
        }
      }
    }

    &-thumbnail {
      width: calc(50% - 12px);
      object-fit: cover;

      @include media-breakpoint-down(sm) {
        width: 100%;
      }
    }

    &-date {
      font-size: 16px;
      line-height: 24px;
      font-weight: 400;

      &-wrapper {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        row-gap: 4px;
        column-gap: 16px;
        padding: 8px 0 16px 0;
        border-bottom: 1px solid var(--kui-gray-700);

        @include media-breakpoint-down(md) {
          padding: 8px 0;
        }
      }
    }

    &-title {
      font-weight: 700;
      font-size: 36px;
      line-height: 40px;

      @include media-breakpoint-down(md) {
        font-size: 24px;
        line-height: 32px;
      }
    }

    &-lead {
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;

      @include media-breakpoint-down(md) {
        font-size: 20px;
        line-height: 26px;
      }
    }

    &-author {
      color: var(--kui-white);
      font-size: 16px;
      line-height: 24px;
      font-weight: 400;

      &.bold {
        transition: all 0.3s;
        font-weight: 700;
        line-height: 22px;
      }

      &-link > .bold:hover {
        color: var(--kui-deep-teal-400);
      }

      &-wrapper {
        display: flex;
        gap: 25px;
        align-items: center;
      }
    }

    &-tag {
      color: var(--kui-gray-50);
      font-weight: 400;
      font-size: 14px;
      line-height: 18px;
      transition: all 0.3s;

      @include media-breakpoint-down(md) {
        font-size: 12px;
        line-height: 14px;
      }

      &:hover {
        color: var(--kui-deep-teal-400);
      }

      &.bold {
        font-weight: 700;
        color: var(--kui-white);

        &:hover {
          color: var(--kui-deep-teal-400);
        }

        @include media-breakpoint-down(md) {
          font-size: 14px;
          line-height: 18px;
        }
      }

      &-wrapper {
        display: flex;
        column-gap: 8px;
        flex-wrap: wrap;
        row-gap: 4px;

        @include media-breakpoint-down(sm) {
          flex-direction: column;
          gap: 8px;
        }
      }

      &-item {
        display: flex;
        align-items: center;

        @include media-breakpoint-down(sm) {
          &:not(:first-child) {
            display: none;
          }
        }
      }
    }

    &-photo {
      margin-left: 40px;
      color: var(--kui-white);
      font-weight: 400;
      font-size: 14px;
      line-height: 18px;

      @include media-breakpoint-down(sm) {
        margin-left: 0;
      }
    }
  }

  .interview {
    padding: 4px 8px;
    border: 1px solid var(--kui-deep-teal-400);
    font-size: 14px;
    line-height: 18px;
    font-weight: 700;
  }

  .lower {
    background-color: var(--kui-gray-900);

    .separator {
      width: 1px;
      height: 10px;
      background-color: var(--kui-gray-700);
      margin-right: 8px;
    }

    &-inner {
      max-width: 1096px;
      padding: 16px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 auto;

      @include media-breakpoint-down(md) {
        padding: 16px;
      }
    }
  }
}
