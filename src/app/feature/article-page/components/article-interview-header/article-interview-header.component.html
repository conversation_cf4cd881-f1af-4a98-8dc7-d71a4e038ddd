<div class="upper">
  <div class="upper-inner">
    <div class="article-data-wrapper">
      <div class="article-date-wrapper">
        <div class="interview">Interjú</div>
        <div class="article-date">{{ data.publishDate! | date: 'yyyy. MM. dd. HH:mm' }}</div>
        <div *ngIf="data?.lastUpdated" class="separator"></div>
        <div *ngIf="data?.lastUpdated" class="article-date">Frissítve: {{ data.lastUpdated! | date: 'yyyy. MM. dd. HH:mm' }}</div>
      </div>
      <h1 class="article-title">{{ data.title }}</h1>
      <div class="article-lead">{{ data.excerpt }}</div>
      <div class="article-author-wrapper">
        <ng-container *ngIf="!isMobileApp">
          <a
            class="article-author-link"
            *ngIf="data?.publicAuthor && data?.publicAuthorSlug; else authorName"
            [routerLink]="['/', 'szerzo', data.publicAuthorSlug]"
            data-skip-on-mobile-app
          >
            <ng-container *ngTemplateOutlet="authorName"></ng-container>
          </a>
        </ng-container>
        <ng-template #authorName>
          <div class="article-author bold">{{ data.publicAuthor }}</div>
        </ng-template>
        <div *ngIf="data?.publicAuthor && data?.articleSource" class="separator"></div>
        <div *ngIf="data?.articleSource" class="article-author">Forrás: {{ data.articleSource }}</div>
      </div>
    </div>
    <img
      *ngIf="data?.thumbnail"
      withFocusPoint
      [data]="data?.thumbnailFocusedImages"
      [alt]="data.thumbnailInfo?.altText"
      [displayedUrl]="data.thumbnail"
      [displayedAspectRatio]="{ desktop: '1:1' }"
      class="article-thumbnail"
      loading="eager"
    />
  </div>
</div>
<div class="lower">
  <div class="lower-inner">
    <div class="article-tag-wrapper">
      <ng-container *ngIf="!isMobileApp">
        <div *ngFor="let tag of data?.tags; let first = first; let last = last" class="article-tag-item" data-skip-on-mobile-app>
          <div *ngIf="!first" class="separator"></div>
          <a [class.bold]="first" [routerLink]="['/cimke', tag.slug]" class="article-tag">{{ tag.title }}</a>
        </div>
      </ng-container>
      <div *ngIf="data?.thumbnailInfo?.source" class="article-photo">Fotó: {{ data.thumbnailInfo?.source }}</div>
    </div>
    <app-social-share
      *ngIf="!isMobileApp"
      [customShareText]="(isMobile$ | async) ? 'Megosztom' : 'Megosztom a cikket'"
      [isInterviewType]="true"
      [isLight]="true"
      customMobileShareIcon="vg-share-mobile"
      data-skip-on-mobile-app
    ></app-social-share>
  </div>
</div>
<!-- <kesma-advertisement-adocean
  *ngIf="adverts?.desktop?.['leaderboard_1'] as ad"
  [ad]="ad"
  [style]="{
    margin: 'var(--ad-margin)',
    background: 'var(--ad-bg-light)',
    padding: 'var(--ad-padding)'
  }"
>
</kesma-advertisement-adocean>
<kesma-advertisement-adocean
  *ngIf="adverts?.mobile?.['mobilrectangle_1'] as ad"
  [style]="{
    margin: 'var(--ad-margin)',
    background: 'var(--ad-bg-light)',
    padding: 'var(--ad-padding)'
  }"
  [ad]="ad"
>
</kesma-advertisement-adocean> -->
