import { ChangeDetectionStrategy, Component, inject, Input, OnDestroy } from '@angular/core';
import { Article, ArticleAdvertisements, FocusPointDirective } from '@trendency/kesma-ui';
import { fromEvent, map, of, startWith, Subject, takeUntil } from 'rxjs';
import { UtilService } from '@trendency/kesma-core';
import { SocialShareComponent } from '../../../../shared';
import { RouterLink } from '@angular/router';
import { AsyncPipe, DatePipe, NgFor, NgIf, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-article-interview-header',
  templateUrl: './article-interview-header.component.html',
  styleUrls: ['./article-interview-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, NgTemplateOutlet, FocusPointDirective, NgFor, SocialShareComponent, AsyncPipe, DatePipe],
})
export class ArticleInterviewHeaderComponent implements OnDestroy {
  @Input() data: Article;
  @Input() adverts: ArticleAdvertisements;
  @Input() isMobileApp: boolean;
  private readonly utils = inject(UtilService);
  readonly #unsubscribe$: Subject<boolean> = new Subject();

  isMobile$ = this.utils.isBrowser()
    ? fromEvent(window, 'resize').pipe(
        map(() => window.innerWidth),
        startWith(window.innerWidth),
        map((width: number) => width <= 768),
        takeUntil(this.#unsubscribe$)
      )
    : of(false);

  ngOnDestroy(): void {
    this.#unsubscribe$.next(true);
    this.#unsubscribe$.complete();
  }
}
