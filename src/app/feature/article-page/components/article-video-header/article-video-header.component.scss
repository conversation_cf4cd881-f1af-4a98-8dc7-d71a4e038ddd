@use 'shared' as *;

:host {
  .upper {
    background-color: var(--kui-gray-950);

    &-inner {
      max-width: 1072px;
      display: flex;
      flex-direction: column;
      gap: 24px;
      margin: 0 auto;
      padding: 24px 0;

      @include media-breakpoint-down(md) {
        padding: 24px;
        gap: 16px;
      }
    }
  }

  .lower {
    background-color: var(--kui-deep-teal-900);

    &-inner {
      max-width: 1072px;
      padding: 16px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 auto;

      @include media-breakpoint-down(md) {
        padding: 16px 24px;
      }

      app-social-share {
        ::ng-deep {
          .share-icon-wrapper {
            gap: 32px !important;
          }
        }
      }
    }
  }

  .article {
    &-thumbnail {
      width: 100%;
      object-fit: cover;

      @include media-breakpoint-down(md) {
        width: calc(100% + 48px);
        max-width: none;
        margin: 0 -24px;
      }
    }

    &-title {
      color: var(--kui-white);
      font-weight: 700;
      font-size: 36px;
      line-height: 40px;

      @include media-breakpoint-down(md) {
        font-size: 28px;
        line-height: 37px;
        letter-spacing: 0.56px;
        margin-bottom: 8px;
      }
    }

    &-lead {
      color: var(--kui-white);
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;

      @include media-breakpoint-down(md) {
        font-size: 20px;
        line-height: 26px;
        letter-spacing: 0.2px;
      }
    }

    &-author {
      color: var(--kui-white);
      font-size: 16px;
      line-height: 24px;
      font-weight: 400;

      @include media-breakpoint-down(md) {
        font-size: 14px;
        line-height: 18px;
      }

      &.bold {
        transition: all 0.3s;
        font-weight: 700;
        line-height: 22px;

        @include media-breakpoint-down(md) {
          line-height: 18px;
        }
      }

      &-link > .bold:hover {
        color: var(--kui-deep-teal-400);
      }

      &-wrapper {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        column-gap: 25px;
        row-gap: 4px;

        @include media-breakpoint-down(md) {
          column-gap: 16px;
        }
      }
    }

    &-tag {
      color: var(--kui-gray-50);
      font-weight: 400;
      font-size: 14px;
      line-height: 18px;
      transition: all 0.3s;

      @include media-breakpoint-down(md) {
        font-size: 12px;
        line-height: 14px;
      }

      &:hover {
        color: var(--kui-deep-teal-400);
      }

      &.bold {
        font-weight: 700;
        color: var(--kui-white);

        &:hover {
          color: var(--kui-deep-teal-400);
        }
      }

      &-wrapper {
        display: flex;
        column-gap: 24px;
        flex-wrap: wrap;
        align-items: center;

        @include media-breakpoint-down(md) {
          column-gap: 8px;
          margin-bottom: 8px;
        }
      }

      &-item {
        display: flex;
        align-items: center;

        .separator {
          margin-right: 8px;
        }
      }
    }
  }

  .separator {
    width: 1px;
    height: 10px;
    background-color: var(--kui-gray-100);
  }

  @include media-breakpoint-down(md) {
    app-social-share {
      display: none;
    }
  }
}
