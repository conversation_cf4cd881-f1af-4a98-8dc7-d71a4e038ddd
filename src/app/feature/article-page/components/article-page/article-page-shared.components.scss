@use 'shared' as *;

.double {
  &-title {
    font-weight: 700;
    font-size: 16px;
    line-height: 22px;
    color: var(--kui-deep-teal-900);
    margin-bottom: 1px;

    @include media-breakpoint-down(md) {
      font-size: 18px;
      line-height: 24px;
    }
  }

  &-lead {
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    color: var(--kui-deep-teal-900);
  }

  &-recommendations {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px 32px;
    margin-bottom: 32px;
    margin-top: 16px;

    @include media-breakpoint-down(lg) {
      grid-template-columns: repeat(1, 1fr);
    }

    @include media-breakpoint-down(sm) {
      grid-template-columns: repeat(2, 1fr);
    }

    @include media-breakpoint-down(xs) {
      grid-template-columns: repeat(1, 1fr);
    }

    vg-article-card {
      &.style-RelatedArticle {
        ::ng-deep {
          .article-card-badges {
            margin-top: 0 !important;

            kesma-icon {
              margin-top: 0 !important;
            }
          }
        }
      }
    }
  }
}

:host {
  &:not(.news-feed) > {
    .double-recommendations,
    app-article-related-content,
    vg-voting,
    vg-newsletter-box,
    vg-vg-article-slider-gallery,
    app-exchange-box,
    vg-dossier-recommender {
      margin-bottom: 24px;
    }
  }
}
