@use 'shared' as *;

:host {
  display: block;
  font-family: var(--kui-font-primary);

  .scroll-target-wrapper {
    position: relative;
    z-index: 1;

    div {
      position: absolute;
      top: -180px;
    }
  }

  .minute-by-minute-blocks {
    display: flex;
    flex-direction: column;
    gap: 32px;
    margin-bottom: 32px;

    @include media-breakpoint-down(md) {
      margin-bottom: 24px;
      gap: 24px;
    }

    .minute-by-minute-block {
      border: 1px solid var(--kui-deep-teal-400);
      padding: 8px 24px;

      @include media-breakpoint-down(md) {
        padding: 8px;
      }

      vg-wysiwyg-box::ng-deep {
        p {
          margin-bottom: 16px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      &-meta {
        display: flex;
        padding: 8px 16px;
        gap: 16px;
        border-bottom: 1px solid var(--kui-deep-teal-400);
        margin-bottom: 16px;

        &-date {
          display: flex;
          align-items: center;
          gap: 16px;
          font-size: 16px;
          font-weight: 700;
          line-height: 22px;
          color: var(--kui-deep-teal-400);

          .separator {
            width: 1px;
            height: 20px;
            background-color: var(--kui-deep-teal-400);
            border-radius: 4px;
          }
        }
      }

      &-title {
        color: var(--kui-deep-teal-900);
        font-size: 18px;
        font-weight: 700;
        line-height: 24px;
        margin-bottom: 8px;

        @include media-breakpoint-down(md) {
          font-size: 16px;
          line-height: 22px;
        }
      }

      &-share {
        display: flex;
        align-items: center;
        gap: 24px;
        padding: 16px 0;

        @include media-breakpoint-down(md) {
          padding: 0;
        }

        &-text {
          font-size: 14px;
          line-height: 18px;
          color: var(--kui-deep-teal-950);
        }
      }
    }
  }

  app-author-more-articles,
  app-opinion-articles {
    margin-bottom: 32px;

    @include media-breakpoint-down(xs) {
      margin-bottom: 24px;
    }

    ::ng-deep vg-block-title {
      .block-url {
        max-width: 136px;
        width: 100%;
      }
    }
  }

  vg-podcast-box {
    background-color: var(--kui-gray-950);

    ::ng-deep {
      padding: 8px 8px 16px 8px;
      gap: 20px;

      .block-url {
        max-width: 112px;
        width: 100%;
      }
    }

    margin-bottom: 36px;
  }

  vg-voting,
  vg-newsletter-box,
  vg-vg-article-slider-gallery,
  vg-dossier-recommender {
    margin-bottom: 24px;
  }

  app-exchange-box,
  .double-recommendations,
  app-article-related-content {
    margin-bottom: 32px;
  }
}
