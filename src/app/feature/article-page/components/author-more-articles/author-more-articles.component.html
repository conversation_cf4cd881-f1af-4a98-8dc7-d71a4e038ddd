<vg-block-title [data]="$any(blockTitle$ | async)"></vg-block-title>

<div class="author-articles">
  <div class="author-article" *ngFor="let article of data | slice: 0 : 4; trackBy: trackByFn">
    <a [routerLink]="['/szerzo', $any(article).authorSlug]" class="article-author">{{ article.author }}</a>

    <vg-article-card [data]="article" [styleID]="ArticleCardType.FeaturedTopImgTagTitle" class="desktop"></vg-article-card>
    <vg-article-card [data]="article" [styleID]="ArticleCardType.FeaturedRightImgTagTitle" [hasOutline]="true" class="mobile"></vg-article-card>
  </div>
</div>
