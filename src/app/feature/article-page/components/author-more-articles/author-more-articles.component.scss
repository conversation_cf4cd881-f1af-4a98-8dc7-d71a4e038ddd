@use 'shared' as *;

:host {
  display: block;

  vg-block-title {
    margin-bottom: 16px;
  }

  vg-article-card {
    &.desktop {
      @include media-breakpoint-down(xs) {
        display: none;
      }
    }

    &.mobile {
      display: none;

      @include media-breakpoint-down(xs) {
        display: block;
      }
    }

    &::ng-deep {
      .article-card {
        @include media-breakpoint-down(xs) {
          &-link {
            padding: 8px;
          }

          &-data {
            margin: 0 !important;
          }

          &-tag {
            font-size: 12px !important;
            font-style: normal;
            font-weight: 500;
            line-height: 14px !important;
          }
        }

        &-thumbnail-meta {
          display: none !important;
        }

        &-data {
          margin: 8px 0 !important;
        }

        &-tag {
          font-size: 12px;
          font-weight: 500;
          line-height: 14px;
          margin-bottom: 0 !important;
        }

        &-title {
          font-size: 14px;
          font-style: normal;
          font-weight: 700;
          line-height: 18px;
        }

        &-badges {
          margin-top: 0;
        }

        &-podcast {
          &-type {
            &-column {
              display: none;
            }
          }
        }
      }
    }
  }

  .author-articles {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;

    @include media-breakpoint-down(lg) {
      grid-template-columns: repeat(2, 1fr);
    }

    @include media-breakpoint-down(xs) {
      grid-template-columns: 1fr;
    }

    .article-author {
      color: var(--kui-deep-teal-600);
      font-size: 14px;
      font-weight: 700;
      margin-bottom: 8px;
      display: block;
      transition: 0.3s;

      &:hover {
        color: var(--kui-gray-950);
      }
    }
  }
}
