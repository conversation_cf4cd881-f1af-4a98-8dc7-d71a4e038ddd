import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ArticleCard, BaseComponent, BlockTitle } from '@trendency/kesma-ui';
import { BehaviorSubject } from 'rxjs';
import { RouterLink } from '@angular/router';
import { As<PERSON><PERSON>ipe, NgFor, SlicePipe } from '@angular/common';
import { ArticleCardType, VgArticleCardComponent, VgBlockTitleComponent } from '../../../../shared';

@Component({
  selector: 'app-author-more-articles',
  templateUrl: './author-more-articles.component.html',
  styleUrls: ['./author-more-articles.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgFor, RouterLink, AsyncPipe, SlicePipe, VgArticleCardComponent, VgBlockTitleComponent],
})
export class AuthorMoreArticlesComponent extends BaseComponent<ArticleCard[]> {
  readonly ArticleCardType = ArticleCardType;

  blockTitle$ = new BehaviorSubject<BlockTitle>({
    text: 'A szerző további cikkei',
    urlName: 'Továbbiak',
  });

  protected override setProperties(): void {
    super.setProperties();

    if (this.data?.length) {
      this.blockTitle$.next({ ...this.blockTitle$.value, url: '/szerzo/' + (this.data as any)?.[0].authorSlug });
    }
  }
}
