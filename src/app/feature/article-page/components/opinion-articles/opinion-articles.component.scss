@use 'shared' as *;

:host {
  display: block;

  vg-block-title {
    margin-bottom: 16px;
  }

  vg-article-card {
    &::ng-deep {
      article {
        height: 100%;
      }
    }
  }

  .author-articles {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 20px;

    @include media-breakpoint-down(lg) {
      grid-template-columns: repeat(3, 1fr);
    }

    @include media-breakpoint-down(md) {
      grid-template-columns: repeat(2, 1fr);
    }

    @include media-breakpoint-down(xs) {
      grid-template-columns: 1fr;
    }
  }
}
