import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ArticleCard, BaseComponent, BlockTitle } from '@trendency/kesma-ui';
import { NgFor } from '@angular/common';
import { ArticleCardType, VgArticleCardComponent, VgBlockTitleComponent } from '../../../../shared';

@Component({
  selector: 'app-opinion-articles',
  templateUrl: './opinion-articles.component.html',
  styleUrls: ['./opinion-articles.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgFor, VgArticleCardComponent, VgBlockTitleComponent],
})
export class OpinionArticlesComponent extends BaseComponent<ArticleCard[]> {
  readonly ArticleCardType = ArticleCardType;

  blockTitle: BlockTitle = {
    text: 'Vélemény cikkek',
    urlName: 'Tov<PERSON><PERSON><PERSON><PERSON>',
    url: '/velemeny',
  };
}
