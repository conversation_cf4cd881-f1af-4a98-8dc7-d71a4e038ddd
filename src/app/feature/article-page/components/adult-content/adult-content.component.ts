import { ChangeDetectionStrategy, Component } from '@angular/core';
import { AdultComponent, IconComponent } from '@trendency/kesma-ui';

@Component({
  selector: 'app-adult-content',
  templateUrl: './adult-content.component.html',
  styleUrls: ['./adult-content.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [IconComponent],
})
export class AdultContentComponent extends AdultComponent {
  override adultLead = `Ez a tartalom olyan elemeket tartalmazhat,
  amelyek a hatályos jogszabályok kategóriái szerint kiskorúakra károsak lehetnek.
 Ha azt szeretné, hogy az ilyen tartalmakhoz erről a számítógépről kiskorú ne férhessen hozzá, használjon szűrőprogramot!
 A javasolt szűrőprogram elérhető <a class="adult-link"href="https://mte.hu/gyermekbarat-internet/" target="_blank">ide kattintva.</a>`;

  override adultText = `Ha ön elmúlt 18 éves, kattintson az "Elmúltam 18 éves" gombra és a tartalom az ön számára elérhető lesz.
  Ha ön nem múlt el 18 éves, kattintson a "Nem múltam el 18 éves" gombra; ez a tartalom az ön számára nem lesz elérhető.`;

  override acceptedText = 'Elmúltam 18 éves';
  override declinedText = 'Nem múltam el 18 éves';
}
