@use 'shared' as *;

:host {
  display: block;

  .adult {
    width: 100%;
    height: 100%;
    background-color: var(--kui-gray-950);
    z-index: 9999;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: auto;
  }

  .logo-wrapper {
    padding: 22px 20px;
    text-align: center;

    @include media-breakpoint-up(md) {
      padding: 43px 20px;
    }
  }

  .header-logo {
    height: 40px;

    @include media-breakpoint-up(md) {
      height: 60px;
    }
  }

  .content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 8px;
    padding: 0 20px 20px 20px;
    color: var(--kui-white);

    @include media-breakpoint-up(md) {
      gap: 20px;
      max-width: 720px;
      margin: 0 auto;
      padding: 0;
    }

    .adult-icon {
      color: var(--kui-red-500);

      @include media-breakpoint-down(md) {
        height: 88px;
        width: 88px;
        margin-bottom: 8px;
      }
    }
  }

  .attention {
    font-size: 24px;
    font-weight: 700;
    line-height: 32px;
    text-decoration: none;

    @include media-breakpoint-up(md) {
      font-size: 36px;
      line-height: 40px;
    }
  }

  .lead {
    font-size: 18px;
    font-weight: 700;
    line-height: 24px;

    ::ng-deep {
      .adult-link {
        color: var(--kui-deep-teal-400);
      }
    }
  }

  .text {
    font-size: 18px;
    font-weight: 400;
    line-height: 24px;
  }

  .button-wrapper {
    margin-top: 20px;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .adult-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: var(--kui-white);
    border: 1px solid var(--kui-deep-teal-400);
    border-radius: 2px;
    font-size: 16px;
    font-weight: 700;
    line-height: 22px;
    padding: 12px 16px;
    cursor: pointer;
    width: 240px;
    height: 48px;
    transition: all 0.3s;

    &:hover {
      background-color: var(--kui-deep-teal-400);
    }
  }
}
