<div *ngIf="data?.thumbnail" class="article-thumbnail-wrapper">
  <img
    *ngIf="data?.thumbnail"
    withFocusPoint
    [data]="data?.thumbnailFocusedImages"
    [displayedUrl]="data.thumbnail"
    [displayedAspectRatio]="{ desktop: '16:9' }"
    alt="Szponzor logó"
    class="article-thumbnail"
    loading="eager"
  />
  <div class="pr-wrapper">
    <div class="pr-text">Szponzorált tartalom</div>
    <div *ngIf="data?.sponsorship?.title" class="separator light"></div>
    <div *ngIf="data?.sponsorship?.title" class="pr-text pr-title">{{ data.sponsorship?.title }}</div>
  </div>
</div>
<div class="article-wrapper">
  <div *ngIf="!isMobileApp" class="article-tag-wrapper" data-skip-on-mobile-app>
    <div *ngFor="let tag of data?.tags; let first = first; let last = last" class="article-tag-item">
      <div *ngIf="!first" class="separator"></div>
      <a [class.bold]="first" [routerLink]="['/cimke', tag.slug]" class="article-tag">{{ tag.title }}</a>
    </div>
  </div>
  <h1 class="article-title">{{ data.title }}</h1>
  <div class="article-lead">{{ data.excerpt }}</div>
  <div class="article-bottom">
    <div>
      <div class="article-meta-wrapper">
        <ng-container *ngIf="!isMobileApp">
          <a
            class="article-meta-item-link"
            *ngIf="data?.publicAuthor && data?.publicAuthorSlug; else authorName"
            [routerLink]="['/', 'szerzo', data.publicAuthorSlug]"
            data-skip-on-mobile-app
          >
            <ng-container *ngTemplateOutlet="authorName"></ng-container>
          </a>
        </ng-container>
        <ng-template #authorName>
          <div class="article-meta-item bold">{{ data.publicAuthor }}</div>
        </ng-template>
        <div *ngIf="!isMobileApp && data?.publicAuthor" class="separator" data-skip-on-mobile-app></div>
        <div *ngIf="data?.articleSource" class="article-meta-item">Forrás: {{ data.articleSource }}</div>
        <div *ngIf="data?.articleSource" class="separator"></div>
        <div class="article-meta-item">{{ data.publishDate! | date: 'yyyy.MM.dd, HH:mm' }}</div>
        <div *ngIf="data?.lastUpdated" class="separator"></div>
        <div *ngIf="data?.lastUpdated" class="article-meta-item">Frissítve: {{ data.lastUpdated! | date: 'yyyy.MM.dd, HH:mm' }}</div>
      </div>
      <div *ngIf="data?.thumbnailInfo?.title || data?.thumbnailInfo?.source" class="article-thumbnail-info">
        <span *ngIf="data?.thumbnailInfo?.title" class="article-thumbnail-info-image">Kép: </span>
        <span *ngIf="data?.thumbnailInfo?.title">{{ data.thumbnailInfo?.title }}</span>
        <span *ngIf="data?.thumbnailInfo?.source"> Fotó: {{ data.thumbnailInfo?.source }}</span>
      </div>
    </div>
    <app-social-share *ngIf="!isMobileApp" [emailSubject]="data.title || ''" [smallPopup]="true" data-skip-on-mobile-app></app-social-share>
  </div>
</div>
