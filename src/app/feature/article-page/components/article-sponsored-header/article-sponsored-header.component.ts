import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Article, FocusPointDirective } from '@trendency/kesma-ui';
import { SocialShareComponent } from '../../../../shared';
import { RouterLink } from '@angular/router';
import { DatePipe, NgFor, NgIf, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-article-sponsored-header',
  templateUrl: './article-sponsored-header.component.html',
  styleUrls: ['./article-sponsored-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, FocusPointDirective, NgFor, RouterLink, NgTemplateOutlet, SocialShareComponent, DatePipe],
})
export class ArticleSponsoredHeaderComponent {
  @Input() data: Article;
  @Input() isMobileApp: boolean;
}
