@use 'shared' as *;

:host {
  .article {
    &-wrapper {
      display: flex;
      flex-direction: column;
      max-width: 1096px;
      gap: 16px;
      margin: 0 auto;
      padding: 24px 0;
      border-bottom: 1px solid var(--kui-deep-teal-400);

      @include media-breakpoint-down(lg) {
        padding: 16px 0;
        margin: 0 24px;
      }
    }

    &-tag {
      color: var(--kui-gray-500);
      font-weight: 400;
      font-size: 14px;
      line-height: 18px;
      transition: all 0.3s;

      @include media-breakpoint-down(md) {
        font-size: 12px;
        line-height: 14px;
      }

      &:hover {
        color: var(--kui-deep-teal-400);
      }

      &:not(.bold) {
        margin-left: 8px;
      }

      &.bold {
        color: var(--kui-gray-950);
        font-weight: 700;
        transition: all 0.3s;

        &:hover {
          color: var(--kui-deep-teal-400);
        }
      }

      &-wrapper {
        display: flex;
        column-gap: 16px;
        flex-wrap: wrap;
        align-items: center;
        row-gap: 4px;

        .separator {
          height: 10px;
        }

        @include media-breakpoint-down(md) {
          row-gap: 2px;
        }
      }

      &-item {
        display: flex;
        align-items: center;
      }
    }

    &-title {
      color: var(--kui-gray-950);
      font-weight: 700;
      font-size: 36px;
      line-height: 40px;

      @include media-breakpoint-down(md) {
        font-size: 28px;
        line-height: 37px;
        letter-spacing: 0.56px;
      }
    }

    &-lead {
      color: var(--kui-gray-950);
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;

      @include media-breakpoint-down(md) {
        font-size: 20px;
        line-height: 26px;
      }
    }

    &-bottom {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      align-items: flex-end;
      margin-top: 8px;

      @include media-breakpoint-down(md) {
        margin-top: 0;
      }
    }

    &-meta {
      &-wrapper {
        display: flex;
        align-items: center;
        column-gap: 16px;
        flex-wrap: wrap;
        row-gap: 4px;
        margin-bottom: 4px;

        @include media-breakpoint-down(md) {
          row-gap: 2px;
          margin-bottom: 8px;
        }
      }

      &-item {
        color: var(--kui-gray-950);
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;

        @include media-breakpoint-down(md) {
          font-size: 14px;
          line-height: 18px;
        }

        &.bold {
          font-weight: 700;
          transition: all 0.3s;
          line-height: 22px;
        }

        &-link > .bold:hover {
          color: var(--kui-deep-teal-400);
        }
      }
    }

    &-thumbnail {
      width: 100%;
      object-fit: cover;
      border-radius: 2px;

      &-wrapper {
        max-width: 1320px;
        margin: 0 auto;
        position: relative;
      }

      &-info {
        color: var(--kui-gray-950);
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px;

        &-image {
          @include media-breakpoint-up(md) {
            display: none;
          }
        }
      }
    }
  }

  .pr {
    &-wrapper {
      width: 100%;
      max-width: 1096px;
      display: flex;
      gap: 16px;
      align-items: center;
      padding: 8px 8px 6px 8px;
      border-radius: 2px;
      background-color: var(--kui-deep-teal-400-o80);
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);

      @include media-breakpoint-down(md) {
        padding: 4px 24px;

        .pr-title {
          display: none;
        }

        .separator {
          display: none;
        }
      }
    }

    &-text {
      color: var(--kui-white);
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;

      @include media-breakpoint-down(md) {
        font-size: 16px;
        line-height: 24px;
      }
    }
  }

  .separator {
    width: 1px;
    height: 12px;
    background-color: var(--kui-gray-100);

    &.light {
      height: 16px;
      background-color: var(--kui-white);
    }
  }

  app-social-share {
    @include media-breakpoint-down(md) {
      display: none;
    }
  }
}
