import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Article, BaseComponent, FocusPointDirective, IconComponent } from '@trendency/kesma-ui';
import { SocialShareComponent } from '../../../../shared';
import { RouterLink } from '@angular/router';
import { NgFor, NgIf, TitleCasePipe } from '@angular/common';
import { PublishDatePipe } from '@trendency/kesma-core';

@Component({
  selector: 'app-article-opinion-header',
  templateUrl: './article-opinion-header.component.html',
  styleUrls: ['./article-opinion-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, FocusPointDirective, NgFor, SocialShareComponent, TitleCasePipe, IconComponent, PublishDatePipe],
})
export class ArticleOpinionHeaderComponent extends BaseComponent<Article> {
  @Input() isMobileApp: boolean;
}
