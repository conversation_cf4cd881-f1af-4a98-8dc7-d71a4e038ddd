@use 'shared' as *;

:host {
  display: block;

  * {
    color: var(--kui-white);
  }

  .article-opinion-header {
    display: flex;
    gap: 24px;
    align-items: center;

    @include media-breakpoint-down(sm) {
      flex-direction: column;
    }

    &-top {
      background: var(--kui-deep-teal-950);
    }

    &-left {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 25px;
      padding: 20px 0;

      @include media-breakpoint-down(sm) {
        padding: 16px 0 0;
      }

      &-tag {
        &:hover {
          kesma-icon {
            transform: rotate(-180deg);
          }

          .first-tag {
            transition: 0.3s;
            color: var(--kui-deep-teal-400);
          }
        }

        kesma-icon {
          color: var(--kui-deep-teal-400);
          justify-content: center;
          transition: 0.3s;
        }

        .first-tag {
          font-weight: 700;
          line-height: 22px;
        }
      }

      &-title {
        font-size: 36px;
        line-height: 40px;
        text-align: center;

        @include media-breakpoint-down(sm) {
          font-size: 28px;
          line-height: normal;
          letter-spacing: 0.56px;
        }
      }

      &-lead {
        text-align: center;
        font-size: 24px;
        font-weight: 500;
        line-height: normal;

        @include media-breakpoint-down(sm) {
          font-size: 20px;
          letter-spacing: 0.2px;
        }
      }

      &-author {
        display: flex;
        align-items: center;
        gap: 16px;
        max-width: 225px;

        &:hover {
          .article-opinion-header-left-author-img {
            transform: scale(1.2);
            border-width: 3px;
          }

          .article-opinion-header-left-author-name,
          .article-opinion-header-left-author-title {
            color: var(--kui-deep-teal-400);
          }
        }

        &-img {
          width: 40px;
          height: 40px;
          object-fit: cover;
          border-radius: 200px;
          border: 2px solid var(--kui-deep-teal-400-o80);
          transition: 0.3s;
        }

        &-name {
          font-weight: 700;
          line-height: 22px;
          transition: 0.3s;
        }

        &-title {
          font-size: 12px;
          line-height: 14px;
          transition: 0.3s;
        }
      }

      &-column-publish {
        display: flex;
        gap: 8px;
        align-items: center;
      }

      &-column {
        padding: 4px 8px;
        border: 1px solid var(--kui-deep-teal-400);
        font-size: 14px;
        font-style: normal;
        font-weight: 700;
        line-height: 18px;
        transition: 0.3s;

        @include media-breakpoint-down(sm) {
          font-size: 12px;
          line-height: 14px;
        }

        &:hover {
          color: var(--kui-deep-teal-400);
        }
      }

      &-published-date {
        color: var(--kui-gray-50);
        font-size: 14px;
        line-height: 18px;
      }
    }

    &-right {
      flex: 1;

      @include media-breakpoint-down(sm) {
        margin-left: -15px;
        margin-right: -15px;
      }

      @include media-breakpoint-down(xs) {
        margin-left: -25px;
        margin-right: -25px;
      }

      img {
        object-fit: cover;
      }
    }

    &-bottom {
      background: var(--kui-deep-teal-900);

      &-inner {
        display: flex;
        align-items: center;
        gap: 24px;
        padding: 16px 0;

        @include media-breakpoint-down(sm) {
          flex-direction: column;
          gap: 8px;
          padding: 16px 24px;
          align-items: flex-start;
        }
      }

      &-tags {
        display: flex;
        gap: 32px;
        flex: 1;

        @include media-breakpoint-down(sm) {
          gap: 16px;
        }
      }

      &-tag {
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px;
        position: relative;
        transition: 0.3s;
        color: var(--kui-gray-50);

        @include media-breakpoint-down(sm) {
          font-size: 12px;
          line-height: 14px;
        }

        &:hover {
          color: var(--kui-deep-teal-400) !important;
        }

        &:not(:last-child):after {
          content: '';
          position: absolute;
          width: 1px;
          height: 10px;
          background: var(--kui-gray-100);
          right: -16px;
          top: 50%;
          transform: translateY(-50%);

          @include media-breakpoint-down(md) {
            right: -8px;
          }
        }

        &.first {
          color: var(--kui-white);
          font-weight: 700;
        }
      }

      &-meta {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        column-gap: 16px;
        row-gap: 4px;

        &-photo {
          font-size: 14px;
          line-height: 18px;

          @include media-breakpoint-down(sm) {
            font-size: 12px;
            line-height: 14px;
          }
        }

        app-social-share {
          @include media-breakpoint-down(sm) {
            display: none;
          }
        }
      }
    }
  }
}
