<section class="article-opinion-header-top">
  <div class="wrapper">
    <div class="article-opinion-header">
      <div class="article-opinion-header-left">
        <a *ngIf="!isMobileApp && data?.tag" [routerLink]="['/cimke', data?.tag?.slug]" class="article-opinion-header-left-tag" data-skip-on-mobile-app>
          <kesma-icon [size]="48" name="vg-quote"></kesma-icon>
          <span class="first-tag">{{ data?.tag?.title }}</span>
        </a>

        <h1 class="article-opinion-header-left-title">{{ data?.title }}</h1>
        <div class="article-opinion-header-left-lead">{{ data?.lead }}</div>

        <a
          *ngIf="!isMobileApp && data?.publicAuthorSlug"
          [routerLink]="['/szerzo', data?.publicAuthorSlug]"
          class="article-opinion-header-left-author"
          data-skip-on-mobile-app
        >
          <img [src]="data?.avatar || 'assets/images/vg-placeholder-avatar.svg'" alt="Szerző képe" class="article-opinion-header-left-author-img" />

          <div class="article-opinion-header-left-author-details">
            <div class="article-opinion-header-left-author-name">{{ data?.publicAuthor }}</div>
            <div class="article-opinion-header-left-author-title">{{ data?.publicAuthorTitle }}</div>
          </div>
        </a>

        <div class="article-opinion-header-left-column-publish">
          <a
            *ngIf="!isMobileApp && data?.primaryColumn?.columnEmphasizeOnArticleCard"
            [routerLink]="['/rovat', data?.primaryColumn?.slug]"
            class="article-opinion-header-left-column"
            data-skip-on-mobile-app
          >
            {{ data?.primaryColumn?.title }}
          </a>

          <div class="article-opinion-header-left-published-date">{{ data?.publishDate | publishDate: 'yyyy.MM.dd., EEEE HH:mm' }}</div>
        </div>
      </div>

      <div class="article-opinion-header-right">
        <img
          withFocusPoint
          [data]="data?.thumbnailFocusedImages"
          [displayedUrl]="data?.thumbnail || './assets/images/vg-placeholder-1-1.svg'"
          [displayedAspectRatio]="{ desktop: '1:1' }"
          alt=""
          loading="eager"
        />
      </div>
    </div>
  </div>
</section>

<section class="article-opinion-header-bottom">
  <div class="wrapper">
    <div class="article-opinion-header-bottom-inner">
      <div *ngIf="!isMobileApp" class="article-opinion-header-bottom-tags" data-skip-on-mobile-app>
        <a
          *ngFor="let tag of data?.tags; let first = first"
          [class.first]="first"
          [routerLink]="['/cimke', tag.slug]"
          class="article-opinion-header-bottom-tag"
        >
          {{ tag.title | titlecase }}
        </a>
      </div>
      <div class="article-opinion-header-bottom-meta">
        <div *ngIf="data?.thumbnailInfo?.photographer" class="article-opinion-header-bottom-meta-photo">
          <span>Fotó: </span>
          <span *ngIf="data?.thumbnailInfo?.source">{{ data?.thumbnailInfo?.source }}/</span>
          <span *ngIf="data?.thumbnailInfo?.title">{{ data?.thumbnailInfo?.title }}/</span>
          <span *ngIf="data?.thumbnailInfo?.photographer">{{ data?.thumbnailInfo?.photographer }}</span>
        </div>

        <app-social-share
          *ngIf="!isMobileApp"
          [customGap]="16"
          [emailSubject]="data?.title || ''"
          [isLightIcon]="true"
          [onlyIcons]="true"
          data-skip-on-mobile-app
        ></app-social-share>
      </div>
    </div>
  </div>
</section>
