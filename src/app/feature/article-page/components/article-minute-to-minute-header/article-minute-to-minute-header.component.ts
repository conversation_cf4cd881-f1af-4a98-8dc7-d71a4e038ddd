import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Article, ArticleAdvertisements, FocusPointDirective, IconComponent, MinuteToMinuteBlock, MinuteToMinuteState } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { SocialShareComponent } from '../../../../shared';
import { RouterLink } from '@angular/router';
import { AsyncPipe, DatePipe, NgFor, NgIf, NgTemplateOutlet, SlicePipe } from '@angular/common';

@Component({
  selector: 'app-article-minute-to-minute-header',
  templateUrl: './article-minute-to-minute-header.component.html',
  styleUrls: ['./article-minute-to-minute-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, NgTemplateOutlet, NgFor, FocusPointDirective, SocialShareComponent, AsyncPipe, SlicePipe, DatePipe, IconComponent],
})
export class ArticleMinuteToMinuteHeaderComponent {
  @Input() data: Article;
  @Input() minuteToMinutes$: Observable<MinuteToMinuteBlock[]>;
  @Input() adverts: ArticleAdvertisements;
  @Input() isMobileApp: boolean;

  readonly MinuteToMinuteState = MinuteToMinuteState;
}
