<div class="article-header-top">
  <div class="narrow-wrapper">
    <div class="article-data-wrapper">
      <div class="article-live">
        <kesma-icon name="vg-live"></kesma-icon>
        <span><PERSON><PERSON><PERSON></span>
        <div class="separator"></div>
        <span *ngIf="data.minuteToMinute === MinuteToMinuteState.RUNNING" class="running">folyamatosan frissül</span>
      </div>
      <h1 class="article-title">{{ data.title }}</h1>
      <div class="article-meta-wrapper">
        <div *ngIf="!isMobileApp" data-skip-on-mobile-app>
          <ng-container *ngIf="data?.publicAuthor && !data?.publicAuthorM2M?.length; else multiAuthor">
            <a *ngIf="data?.publicAuthorSlug; else authorName" [routerLink]="['/', 'szerzo', data.publicAuthorSlug]" class="article-meta-item-link">
              <ng-container *ngTemplateOutlet="authorName"></ng-container>
            </a>
            <ng-template #authorName>
              <div class="article-meta-item bold">{{ data.publicAuthor }}</div>
            </ng-template>
            <div *ngIf="data?.publishDate || data?.lastUpdated" class="separator"></div>
          </ng-container>
          <ng-template #multiAuthor>
            <ng-container *ngIf="data?.publicAuthorM2M?.length">
              <ng-container *ngFor="let author of data.publicAuthorM2M; let last = last">
                <a [routerLink]="['/', 'szerzo', author.slug]">
                  <div class="article-meta-item bold">{{ author.fullName }}</div>
                </a>
                <div *ngIf="!last || (last && (data?.publishDate || data?.lastUpdated))" class="separator"></div>
              </ng-container>
            </ng-container>
          </ng-template>
        </div>
        <div>
          <div *ngIf="data?.publishDate as publishDate" class="article-meta-item">{{ publishDate | date: 'yyyy.MM.dd., EEEE HH:mm' }}</div>
          <div *ngIf="data?.lastUpdated" class="separator publish-date"></div>
          <div *ngIf="data?.lastUpdated as lastUpdated" class="article-meta-item">Frissítve: {{ lastUpdated | date: 'yyyy.MM.dd., EEEE HH:mm' }}</div>
        </div>
      </div>
      <div *ngIf="data?.thumbnailInfo?.title || data?.thumbnailInfo?.source" class="article-meta-photo">
        <span *ngIf="data?.thumbnailInfo?.title">Kép: {{ data.thumbnailInfo?.title }}</span>
        <span *ngIf="data?.thumbnailInfo?.source"> Fotó: {{ data.thumbnailInfo?.source }}</span>
        <div *ngIf="data?.articleSource as source" class="article-meta-item source">Forrás: {{ source }}</div>
      </div>
    </div>
    <img
      *ngIf="data?.thumbnail"
      [alt]="data.thumbnailInfo?.altText || ''"
      [data]="data?.thumbnailFocusedImages"
      [displayedAspectRatio]="{ desktop: '16:9' }"
      [displayedUrl]="data.thumbnail"
      class="article-thumbnail"
      loading="eager"
      withFocusPoint
    />
  </div>
</div>
<div *ngIf="!isMobileApp" class="article-header-middle" data-skip-on-mobile-app>
  <div class="narrow-wrapper space-between">
    <div class="article-tag-wrapper">
      <div *ngFor="let tag of data?.tags; let first = first; let last = last" class="article-tag-item">
        <div *ngIf="!first" class="separator"></div>
        <a [class.bold]="first" [routerLink]="['/cimke', tag.slug]" class="article-tag">{{ tag.title }}</a>
      </div>
    </div>
    <app-social-share [emailSubject]="data.title || ''" [isLight]="true"></app-social-share>
  </div>
</div>
<div class="article-header-bottom">
  <div class="narrow-wrapper space-between">
    <span class="summary-text">Összefoglaló:</span>
    <div class="minute-by-minute-wrapper">
      <div *ngFor="let block of (minuteToMinutes$ | async | slice: 0 : 5) || []" class="minute-by-minute-item">
        <kesma-icon name="vg-bullet"></kesma-icon>
        <a [fragment]="'pp-' + block.id" routerLink=".">
          <div class="minute-by-minute-title">{{ block.title }}</div>
        </a>
      </div>
    </div>
  </div>
</div>
<!-- <kesma-advertisement-adocean
  *ngIf="adverts?.desktop?.['leaderboard_1'] as ad"
  [ad]="ad"
  [style]="{
    margin: 'var(--ad-margin)',
    background: 'var(--ad-bg-light)',
    padding: 'var(--ad-padding)'
  }"
>
</kesma-advertisement-adocean>
<kesma-advertisement-adocean
  *ngIf="adverts?.mobile?.['mobilrectangle_1'] as ad"
  [style]="{
    margin: 'var(--ad-margin)',
    background: 'var(--ad-bg-light)',
    padding: 'var(--ad-padding)'
  }"
  [ad]="ad"
>
</kesma-advertisement-adocean> -->
