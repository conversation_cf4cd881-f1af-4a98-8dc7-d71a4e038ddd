@use 'shared' as *;

:host {
  .article {
    &-data {
      &-wrapper {
        display: flex;
        flex-direction: column;
        width: 50%;
        gap: 20px;

        @include media-breakpoint-down(md) {
          width: 100%;
          gap: 16px;
        }
      }
    }

    &-header {
      &-top {
        background-color: var(--kui-deep-teal-950);

        .narrow-wrapper {
          @include media-breakpoint-down(md) {
            flex-direction: column;
          }
        }
      }

      &-middle {
        background-color: var(--kui-deep-teal-900);

        @include media-breakpoint-down(md) {
          app-social-share {
            display: none;
          }
        }
      }

      &-bottom {
        background-color: var(--kui-deep-teal-50);

        .narrow-wrapper {
          @include media-breakpoint-down(md) {
            flex-direction: column;
            gap: 8px;
            padding: 24px;
          }
        }
      }
    }

    &-thumbnail {
      width: 50%;
      object-fit: cover;

      @include media-breakpoint-down(md) {
        width: 100%;
      }
    }

    &-live {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 24px;
      border: 1px solid var(--kui-deep-teal-400);
      color: var(--kui-deep-teal-400);
      border-radius: 2px;

      @include media-breakpoint-down(md) {
        padding: 6px 16px;

        kesma-icon {
          width: 20px;
          height: 20px;
        }
      }

      span {
        font-size: 16px;
        font-weight: 700;
        line-height: 22px;
      }

      .separator {
        height: 20px;
        width: 1px;
        border-radius: 4px;
        background-color: var(--kui-deep-teal-400);

        @include media-breakpoint-down(md) {
          display: none;
        }
      }

      .running {
        @include media-breakpoint-down(md) {
          display: none;
        }
      }
    }

    &-meta {
      &-wrapper {
        display: flex;
        flex-direction: column;
        row-gap: 4px;
        @include media-breakpoint-down(md) {
          row-gap: 16px;
        }
        & > div {
          display: flex;
          align-items: center;
          column-gap: 16px;
          flex-wrap: wrap;

          &:last-child {
            column-gap: 8px;

            @include media-breakpoint-down(md) {
              row-gap: 1px;
            }
          }
        }
      }

      &-item {
        color: var(--kui-gray-50);
        font-weight: 400;
        font-size: 14px;
        line-height: 18px;

        &.source {
          color: var(--kui-white);
        }

        &.bold {
          font-size: 16px;
          line-height: 22px;
          font-weight: 700;
          color: var(--kui-white);
          transition: all 0.3s;

          @include media-breakpoint-down(md) {
            font-size: 14px;
            line-height: 18px;
          }
        }

        &-link > .bold:hover {
          color: var(--kui-deep-teal-400);
        }
      }

      &-photo {
        display: flex;
        flex-direction: column;
        gap: 4px;
        color: var(--kui-white);
        font-weight: 400;
        font-size: 14px;
        line-height: 18px;

        @include media-breakpoint-down(md) {
          gap: 0px;
        }
      }
    }

    &-title {
      color: var(--kui-white);
      font-weight: 700;
      font-size: 36px;
      line-height: 40px;

      @include media-breakpoint-down(md) {
        font-size: 32px;
      }
    }

    &-tag {
      color: var(--kui-gray-50);
      font-weight: 400;
      font-size: 14px;
      line-height: 18px;
      transition: all 0.3s;

      @include media-breakpoint-down(md) {
        font-size: 12px;
        line-height: 14px;
      }

      &:hover {
        color: var(--kui-deep-teal-400);
      }

      &:not(.bold) {
        margin-left: 8px;
      }

      &.bold {
        color: var(--kui-white);
        font-weight: 700;

        &:hover {
          color: var(--kui-deep-teal-400);
        }
      }

      &-wrapper {
        display: flex;
        column-gap: 9px;
        flex-wrap: wrap;
        align-items: center;
        row-gap: 4px;
      }

      &-item {
        display: flex;
        align-items: center;
      }
    }
  }

  .narrow-wrapper {
    max-width: 1096px;
    display: flex;
    margin: 0 auto;
    padding: 24px 0;
    gap: 24px;

    &.space-between {
      justify-content: space-between;
    }

    @include media-breakpoint-down(lg) {
      padding: 24px 16px !important;
    }
  }

  .separator {
    height: 12px;
    width: 1px;
    background-color: var(--kui-gray-50);

    &.publish-date {
      height: 10px;
      background-color: var(--kui-gray-100);
    }
  }

  .summary-text {
    color: var(--kui-gray-950);
    font-weight: 700;
    font-size: 20px;
    line-height: 26px;

    @include media-breakpoint-down(md) {
      font-size: 16px;
      line-height: 22px;
    }
  }

  .minute-by-minute {
    &-wrapper {
      display: flex;
      max-width: 869px;
      width: 100%;
      flex-direction: column;
      gap: 16px;
    }

    &-item {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      color: var(--kui-deep-teal-400);

      kesma-icon {
        flex: 0 0 auto;
      }
    }

    &-title {
      color: var(--kui-gray-950);
      font-weight: 700;
      font-size: 20px;
      line-height: 26px;
      text-decoration: underline transparent solid 1px;
      text-underline-offset: 3px;
      transition: all 0.3s;

      @include media-breakpoint-down(md) {
        font-size: 16px;
        line-height: 22px;
      }

      &:hover {
        text-decoration: underline var(--kui-deep-teal-400) solid 1px;
      }
    }
  }
}
