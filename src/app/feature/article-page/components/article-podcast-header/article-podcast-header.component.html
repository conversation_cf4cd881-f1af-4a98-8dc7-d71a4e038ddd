<div class="upper">
  <div class="upper-inner">
    <div [style.position]="'relative'">
      <img
        *ngIf="data?.thumbnail"
        withFocusPoint
        [data]="data?.thumbnailFocusedImages"
        [alt]="data.thumbnailInfo?.altText"
        [displayedUrl]="data.primaryColumn.thumbnail || data.thumbnail"
        [displayedAspectRatio]="{ desktop: '16:9' }"
        class="article-thumbnail"
        loading="eager"
      />
    </div>
    <div *ngIf="!isMobileApp" class="article-tag-wrapper" data-skip-on-mobile-app>
      <a [routerLink]="['/rovat', data.columnSlug]" class="article-column">
        <kesma-icon [size]="32" name="vg-podcast"></kesma-icon>
        <span>{{ data.columnTitle }}</span>
      </a>
      <div class="article-tag-wrapper-right">
        <div *ngFor="let tag of data?.tags; let first = first" class="article-tag-item">
          <div [class.first]="first" class="separator"></div>
          <a [class.first]="first" [routerLink]="['/cimke', tag.slug]" class="article-tag">{{ tag.title }}</a>
        </div>
      </div>
    </div>
    <h1 class="article-title">{{ data.title }}</h1>
    <div>
      <div class="article-lead">{{ data.excerpt }}</div>
      <div *ngIf="$any(data).podcastGuestName" class="article-guest">
        A podcast vendége: {{ $any(data).podcastGuestName }}<span *ngIf="$any(data).podcastGuestNameTitle">, {{ $any(data).podcastGuestNameTitle }}</span>
      </div>
    </div>
  </div>
</div>
<div class="lower">
  <div class="lower-inner">
    <div class="article-author-wrapper">
      <ng-container *ngIf="!isMobileApp">
        <a
          class="article-author-link"
          *ngIf="data?.publicAuthorSlug; else authorName"
          [routerLink]="['/', 'szerzo', data.publicAuthorSlug]"
          data-skip-on-mobile-app
        >
          <ng-container *ngTemplateOutlet="authorName"></ng-container>
        </a>
      </ng-container>
      <ng-template #authorName>
        <div class="article-author bold" *ngIf="data?.publicAuthor"><span class="article-author-text">Szerkesztő: </span>{{ data.publicAuthor }}</div>
      </ng-template>
      <div *ngIf="data?.publicAuthor && data?.articleSource" class="separator"></div>
      <div *ngIf="data?.articleSource" class="article-author">Forrás: {{ data.articleSource }}</div>
      <div *ngIf="data?.publishDate && data?.articleSource" class="separator"></div>
      <div class="article-author">{{ data.publishDate! | date: 'yyyy.MM.dd, HH:mm' }}</div>
      <div *ngIf="data?.thumbnailInfo?.source" class="separator"></div>
      <div *ngIf="data?.thumbnailInfo?.source" class="article-author">Fotó: {{ data.thumbnailInfo?.source }}</div>
    </div>
    <app-social-share *ngIf="!isMobileApp" [emailSubject]="data.title || ''" [isLightIcon]="true" [onlyIcons]="true" data-skip-on-mobile-app></app-social-share>
  </div>
</div>
