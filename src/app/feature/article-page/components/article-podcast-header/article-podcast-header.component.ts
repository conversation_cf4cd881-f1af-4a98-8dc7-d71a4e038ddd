import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { Article, FocusPointDirective, IconComponent } from '@trendency/kesma-ui';
import { SocialShareComponent } from '../../../../shared';
import { RouterLink } from '@angular/router';
import { DatePipe, <PERSON><PERSON>or, NgIf, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-article-podcast-header',
  templateUrl: './article-podcast-header.component.html',
  styleUrls: ['./article-podcast-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, FocusPointDirective, RouterLink, NgFor, NgTemplateOutlet, SocialShareComponent, DatePipe, IconComponent],
})
export class ArticlePodcastHeaderComponent {
  @Input() data: Article & { isVidcast?: boolean };
  @Input() isMobileApp: boolean;
}
