@use 'shared' as *;

:host {
  .upper {
    background-color: var(--kui-gray-950);

    &-inner {
      max-width: 1072px;
      display: flex;
      flex-direction: column;
      gap: 24px;
      margin: 0 auto;
      padding: 24px 0;

      @include media-breakpoint-down(md) {
        padding: 0 24px 24px 24px;
      }
    }
  }

  .lower {
    background-color: var(--kui-deep-teal-900);

    &-inner {
      max-width: 1072px;
      padding: 16px 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 0 auto;

      @include media-breakpoint-down(md) {
        padding: 16px 24px;
      }
    }
  }

  .article {
    &-thumbnail {
      width: 100%;
      object-fit: cover;

      @include media-breakpoint-down(md) {
        width: calc(100% + 48px);
        max-width: none;
        margin: 0 -24px;
      }

      &-inner {
        position: absolute;
        inset: 0;
        margin: auto;
        width: fit-content;
        height: fit-content;

        .podcast-text {
          display: flex;
          justify-content: center;
          color: var(--kui-white);
          font-weight: 400;
          font-size: 20px;
          line-height: 26px;
          letter-spacing: 5px;
          margin-top: 45px;

          @include media-breakpoint-down(lg) {
            font-size: 16px;
            line-height: 20px;
            margin-top: 24px;
            letter-spacing: 4px;
          }

          @include media-breakpoint-down(sm) {
            font-size: 12px;
            line-height: 14px;
            margin-top: 7px;
            letter-spacing: 3px;
          }
        }

        kesma-icon {
          ::ng-deep {
            svg {
              width: 465px !important;
              height: 76px !important;

              @include media-breakpoint-down(lg) {
                width: 324px !important;
                height: 52px !important;
              }

              @include media-breakpoint-down(sm) {
                width: 188px !important;
                height: 31px !important;
              }
            }
          }
        }
      }
    }

    &-title {
      color: var(--kui-white);
      font-weight: 700;
      font-size: 36px;
      line-height: 40px;

      @include media-breakpoint-down(md) {
        font-size: 28px;
        line-height: 37px;
        letter-spacing: 0.56px;
      }
    }

    &-lead {
      color: var(--kui-white);
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;

      @include media-breakpoint-down(md) {
        font-size: 20px;
        line-height: 26px;
        letter-spacing: 0.2px;
      }
    }

    &-author {
      color: var(--kui-white);
      font-size: 16px;
      line-height: 24px;
      font-weight: 400;

      @include media-breakpoint-down(md) {
        font-size: 14px;
        line-height: 18px;
      }

      &.bold {
        transition: all 0.3s;
        font-weight: 700;
      }

      &-link > .bold:hover {
        color: var(--kui-deep-teal-400);
      }

      &-wrapper {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        column-gap: 24px;
        row-gap: 4px;

        @include media-breakpoint-down(md) {
          column-gap: 16px;
        }
      }

      &-text {
        @include media-breakpoint-down(md) {
          display: none;
        }
      }
    }

    &-tag {
      color: var(--kui-gray-50);
      font-weight: 400;
      font-size: 14px;
      line-height: 18px;
      transition: all 0.3s;

      @include media-breakpoint-down(md) {
        font-size: 12px;
        line-height: 14px;
      }

      &:hover {
        color: var(--kui-deep-teal-400);
      }

      &.first {
        @include media-breakpoint-down(md) {
          font-weight: 700;
        }
      }

      &-wrapper {
        display: flex;
        column-gap: 24px;
        align-items: center;

        @include media-breakpoint-down(md) {
          column-gap: 16px;
          align-items: flex-start;
        }

        &-right {
          display: flex;
          column-gap: 24px;
          flex-wrap: wrap;
          align-items: center;
          row-gap: 4px;

          @include media-breakpoint-down(md) {
            flex-direction: column;
            align-items: flex-start;
          }
        }
      }

      &-item {
        display: flex;
        align-items: center;

        .separator {
          margin-right: 8px;

          &.first {
            @include media-breakpoint-down(md) {
              display: none;
            }
          }
        }
      }
    }

    &-guest {
      color: var(--kui-deep-teal-400);
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;

      @include media-breakpoint-down(md) {
        margin-top: 8px;
        font-size: 20px;
        line-height: 26px;
        letter-spacing: 0.2px;
      }
    }

    &-column {
      display: flex;
      padding: 8px 16px;
      border-radius: 2px;
      gap: 8px;
      border: 1px solid var(--kui-gray-900);
      color: var(--kui-deep-teal-400);
      transition: all 0.3s;
      align-items: center;

      &:hover {
        border: 1px solid var(--kui-deep-teal-400);
        span {
          color: var(--kui-deep-teal-400);
        }
      }

      span {
        transition: all 0.3s;
        color: var(--kui-white);
        font-weight: 700;
        font-size: 20px;
        line-height: 26px;
      }
    }
  }

  .separator {
    width: 1px;
    height: 10px;
    background-color: var(--kui-gray-100);
  }

  @include media-breakpoint-down(md) {
    app-social-share {
      display: none;
    }
  }
}
