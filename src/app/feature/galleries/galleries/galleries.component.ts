import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  createCanonicalUrlForPageablePage,
  GalleryData,
  IconComponent,
  LimitableMeta,
} from '@trendency/kesma-ui';
import { Subject } from 'rxjs';
import { map, takeUntil, tap } from 'rxjs/operators';
import {
  createVilaggazdasagTitle,
  defaultMetaInfo,
  NewsletterBoxType,
  VgGalleryCardComponent,
  VgNewsletterBoxComponent,
  VgPagerComponent,
} from '../../../shared';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { NgFor, NgIf, NgTemplateOutlet, SlicePipe } from '@angular/common';

@Component({
  selector: 'app-galleries',
  templateUrl: './galleries.component.html',
  styleUrls: ['./galleries.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    NgFor,
    NgTemplateOutlet,
    SidebarComponent,
    SlicePipe,
    VgNewsletterBoxComponent,
    VgPagerComponent,
    VgGalleryCardComponent,
    IconComponent,
    AdvertisementAdoceanComponent,
  ],
})
export class GalleriesComponent implements OnInit, OnDestroy {
  galleries?: GalleryData[];
  limitableMeta?: LimitableMeta;

  readonly NewsletterBoxType = NewsletterBoxType;

  readonly #destroy$: Subject<boolean> = new Subject();
  adverts?: AdvertisementsByMedium;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly seo: SeoService,
    private readonly cdr: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService
  ) {}

  ngOnInit(): void {
    this.route.data
      .pipe(
        tap(() => this.initAds()),
        map(({ data }) => data),
        takeUntil(this.#destroy$)
      )
      .subscribe(({ data, meta }) => {
        this.galleries = data;
        this.limitableMeta = meta?.limitable;
        this.#setMetaData();
        this.cdr.markForCheck();
      });
  }

  private initAds(): void {
    this.resetAds();
    this.adStore.advertisemenets$.pipe(takeUntil(this.#destroy$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStore.separateAdsByMedium(ads);

      this.cdr.detectChanges();
    });
  }

  resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  ngOnDestroy(): void {
    this.#destroy$.next(true);
    this.#destroy$.complete();
  }

  #setMetaData(): void {
    const canonical = createCanonicalUrlForPageablePage('galeriak', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);

    const title = createVilaggazdasagTitle('Galériák');
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
    };
    this.seo.setMetaData(metaData);
  }
}
