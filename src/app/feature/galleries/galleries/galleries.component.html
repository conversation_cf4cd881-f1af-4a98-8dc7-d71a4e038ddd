<section>
  <div class="wider-wrapper">
    <div class="narrow-wrapper">
      <h1 class="gallery-title">
        <kesma-icon [size]="24" name="vg-gallery"></kesma-icon>
        <PERSON><PERSON><PERSON><PERSON><PERSON>
      </h1>
      <div *ngIf="galleries?.length" class="gallery-latest-list">
        <vg-gallery-card *ngFor="let gallery of galleries | slice: 0 : 3" [data]="gallery" [hasMoreContent]="false"></vg-gallery-card>
      </div>
    </div>
  </div>

  <kesma-advertisement-adocean
    *ngIf="adverts?.desktop?.roadblock_1 as ad"
    [ad]="ad"
    [style]="{
      margin: 'var(--ad-margin)',
      padding: 'var(--ad-padding)',
      background: 'var(--ad-bg-light)',
    }"
  >
  </kesma-advertisement-adocean>

  <kesma-advertisement-adocean
    *ngIf="adverts?.mobile?.mobilrectangle_1 as ad"
    [ad]="ad"
    [style]="{
      margin: 'var(--ad-margin)',
      background: 'var(--ad-bg-light)',
      padding: 'var(--ad-padding)',
    }"
  >
  </kesma-advertisement-adocean>

  <div class="wrapper">
    <vg-newsletter-box [styleID]="NewsletterBoxType.SocialRight" class="newsletter"></vg-newsletter-box>
  </div>

  <div class="wrapper with-aside">
    <div class="left-column">
      <ng-container *ngTemplateOutlet="galleryBlockTemplate; context: { start: 3, end: 9, showNewsletter: true }"></ng-container>

      <kesma-advertisement-adocean
        *ngIf="adverts?.desktop?.roadblock_2 as ad"
        [ad]="ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
      >
      </kesma-advertisement-adocean>

      <kesma-advertisement-adocean
        *ngIf="adverts?.mobile?.mobilrectangle_2 as ad"
        [ad]="ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
      >
      </kesma-advertisement-adocean>

      <ng-container *ngTemplateOutlet="galleryBlockTemplate; context: { start: 9 }"></ng-container>

      <ng-container *ngIf="limitableMeta">
        <vg-pager
          *ngIf="limitableMeta?.pageMax && limitableMeta?.pageMax! > 0"
          [allowAutoScrollToTop]="true"
          [hasSkipButton]="true"
          [isListPager]="true"
          [maxDisplayedPages]="3"
          [rowAllCount]="limitableMeta?.rowAllCount || 0"
          [rowOnPageCount]="limitableMeta?.rowOnPageCount || 0"
          [showFirstPage]="true"
          [showLastPage]="true"
        >
        </vg-pager>
      </ng-container>

      <kesma-advertisement-adocean
        *ngIf="adverts?.desktop?.roadblock_3 as ad"
        [ad]="ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
      >
      </kesma-advertisement-adocean>

      <kesma-advertisement-adocean
        *ngIf="adverts?.mobile?.mobilrectangle_3 as ad"
        [ad]="ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
      >
      </kesma-advertisement-adocean>
    </div>

    <aside>
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>

<ng-template #galleryBlockTemplate let-end="end" let-showNewsletter="showNewsletter" let-start="start">
  <ng-container *ngIf="(galleries ?? [] | slice: start : end)?.length">
    <div class="gallery-list">
      <vg-gallery-card *ngFor="let gallery of galleries | slice: start : end" [data]="gallery" [hasMoreContent]="false"></vg-gallery-card>
    </div>
    <vg-newsletter-box *ngIf="showNewsletter" [isListPageView]="true" [styleID]="NewsletterBoxType.Basic"></vg-newsletter-box>
  </ng-container>
</ng-template>
