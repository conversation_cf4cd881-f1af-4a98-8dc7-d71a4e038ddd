import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { ApiResponseMetaList, ApiResult, GalleryData, RedirectService } from '@trendency/kesma-ui';
import { Observable, tap, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ApiService } from '../../../shared';
import { HttpErrorResponse } from '@angular/common/http';

@Injectable()
export class GalleriesResolver {
  constructor(
    private readonly apiService: ApiService,
    private readonly router: Router
  ) {}

  kesmaRedirectService = inject(RedirectService);

  resolve(snapshot: ActivatedRouteSnapshot): Observable<ApiResult<GalleryData[], ApiResponseMetaList>> {
    const queryParams = snapshot.queryParams as { page: string };
    const currentPage = queryParams?.page ? parseInt(queryParams.page, 10) - 1 : 0;

    return this.apiService.getGalleries(currentPage).pipe(
      tap((res) => {
        if (this.kesmaRedirectService.shouldBeRedirect(currentPage, res?.data)) {
          this.kesmaRedirectService.redirectOldUrl(`alapko-tartalom`, false, 302);
        }
      }),

      catchError((error: HttpErrorResponse) => {
        this.router.navigate(['/404'], { skipLocationChange: true }).then();
        return throwError(() => error);
      })
    );
  }
}
