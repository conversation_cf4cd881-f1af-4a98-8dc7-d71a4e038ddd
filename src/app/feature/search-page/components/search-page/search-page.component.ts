import { ChangeDetectionStrategy, ChangeDetectorRef, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { map, takeUntil, tap } from 'rxjs/operators';
import { Observable, Subject } from 'rxjs';
import { SearchPageData } from '../../api/search-page.definitions';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  ArticleCard,
  createCanonicalUrlForPageablePage,
  LimitableMeta,
} from '@trendency/kesma-ui';
import {
  ArticleCardType,
  createVilaggazdasagTitle,
  defaultMetaInfo,
  NewsletterBoxType,
  VgArticleCardComponent,
  VgNewsletterBoxComponent,
  VgPagerComponent,
} from '../../../../shared';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { SidebarComponent } from '../../../layout/components/sidebar/sidebar.component';
import { NgFor, NgIf, SlicePipe } from '@angular/common';
import { SearchFilterComponent } from '../search-filter/search-filter.component';

const MAX_RESULTS_PER_PAGE = 15;

@Component({
  selector: 'app-search-page',
  templateUrl: './search-page.component.html',
  styleUrls: ['./search-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    SearchFilterComponent,
    NgIf,
    VgArticleCardComponent,
    NgFor,
    SidebarComponent,
    SlicePipe,
    VgNewsletterBoxComponent,
    VgPagerComponent,
    AdvertisementAdoceanComponent,
  ],
})
export class SearchPageComponent implements OnInit, OnDestroy {
  articles: ArticleCard[] = [];
  columns: Record<string, string>[] = [];
  limitable?: LimitableMeta;
  globalFilter?: string;
  page = 0;
  resultsCount = 0;
  adverts?: AdvertisementsByMedium;

  readonly NewsletterBoxType = NewsletterBoxType;
  readonly ArticleCardType = ArticleCardType;
  readonly #unsubscribe$: Subject<boolean> = new Subject();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef,
    private readonly adStore: AdvertisementAdoceanStoreService,
    private readonly seo: SeoService
  ) {}

  ngOnInit(): void {
    this.route.queryParams
      .pipe(
        tap(() => this.initAds()),
        takeUntil(this.#unsubscribe$)
      )
      .subscribe(({ page, global_filter }) => {
        this.page = page ? --page : 0;
        this.globalFilter = global_filter;
        this.setMetaData();
      });

    (this.route.data as Observable<{ data: SearchPageData }>)
      .pipe(
        map(({ data }) => data),
        takeUntil(this.#unsubscribe$)
      )
      .subscribe(({ articles, limitable, columns }) => {
        this.articles = articles;
        this.limitable = limitable;
        this.columns = columns;
        this.calculateResultsCount();
        this.setMetaData();
        this.cdr.markForCheck();
      });
  }

  ngOnDestroy(): void {
    this.#unsubscribe$.next(true);
    this.#unsubscribe$.complete();
  }

  private initAds(): void {
    this.resetAds();
    this.adStore.advertisemenets$.pipe(takeUntil(this.#unsubscribe$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStore.separateAdsByMedium(ads);

      this.cdr.detectChanges();
    });
  }

  resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  private setMetaData(): void {
    const searchedTitle = this.globalFilter ? `Keresés: ${this.globalFilter}` : 'Keresés';
    const title = createVilaggazdasagTitle(searchedTitle);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      robots: 'noindex, nofollow',
    };
    this.seo.setMetaData(metaData);
    const canonical = createCanonicalUrlForPageablePage('kereses');
    if (canonical) {
      this.seo.updateCanonicalUrl(canonical);
    }
  }

  calculateResultsCount(): void {
    const page = this.page + 1;
    const pageMaxResults = page * MAX_RESULTS_PER_PAGE;

    if (this.limitable?.rowAllCount) {
      this.resultsCount = pageMaxResults < this.limitable.rowAllCount ? this.limitable.rowOnPageCount! * page : this.limitable.rowAllCount;
    } else {
      this.resultsCount = pageMaxResults;
    }
  }
}
