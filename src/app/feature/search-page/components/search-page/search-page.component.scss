@use 'shared' as *;

:host {
  display: block;
  margin-top: 24px;

  .search-page-inner {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .search-results {
    color: var(--gray-950);
    font-size: 24px;
    font-weight: 500;
    line-height: normal;
    display: inline;

    &-wrapper {
      h1 {
        color: var(--kui-deep-teal-900);
        display: inline-block;
        font-size: 24px;
        line-height: normal;
        letter-spacing: 0.96px;
        margin-right: 5px;
      }
    }
  }

  .articles,
  .left-column {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
}

.ad-block {
  background-color: var(--kui-gray-100);
  padding: 24px 0px;

  @include media-breakpoint-down(md) {
    display: none;
  }
}

.full-width {
  // width: 100vw;
  // margin-inline: -85px;
}
