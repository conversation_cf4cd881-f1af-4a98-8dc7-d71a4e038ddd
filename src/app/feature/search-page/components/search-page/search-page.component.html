<section>
  <div class="wrapper">
    <div class="search-page-inner">
      <app-search-filter [columnFilters]="columns"></app-search-filter>

      <div class="ad-block full-width">
        <kesma-advertisement-adocean
          *ngIf="adverts?.desktop?.roadblock_1 as ad"
          [style]="{
            margin: 'var(--ad-margin)',
            padding: 'var(--ad-padding)',
          }"
          [ad]="ad"
        >
        </kesma-advertisement-adocean>
      </div>

      <div class="search-results-wrapper">
        <h1 *ngIf="globalFilter">{{ globalFilter }}:</h1>

        <div class="search-results" *ngIf="limitable?.rowAllCount; else noResult">
          {{ limitable!.rowFrom! + 1 }}-{{ resultsCount }} cikk megje<PERSON>se a {{ limitable?.rowAllCount }}-ből
          <ng-container *ngIf="globalFilter">a keresett kifejez<PERSON>re</ng-container>
        </div>

        <ng-template #noResult>
          <div class="search-results">Ni<PERSON><PERSON> tal<PERSON></div>
        </ng-template>
      </div>

      <div class="main-article" *ngIf="articles?.length">
        <vg-article-card
          [data]="articles[0]"
          [styleID]="ArticleCardType.FeaturedRightImgDateTagTitle"
          [displayLead]="true"
          [showHighlightedView]="true"
          [showLeadInsteadOfExcerpt]="true"
        >
        </vg-article-card>
      </div>

      <kesma-advertisement-adocean
        *ngIf="adverts?.mobile?.mobilrectangle_1 as ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
        [ad]="ad"
      >
      </kesma-advertisement-adocean>

      <div class="newsletter">
        <vg-newsletter-box [styleID]="NewsletterBoxType.SocialRight"></vg-newsletter-box>
      </div>
    </div>
  </div>

  <div class="wrapper with-aside">
    <div class="left-column">
      <div class="articles">
        <vg-article-card [data]="article" [styleID]="ArticleCardType.FeaturedRightImgDateTagTitle" *ngFor="let article of articles | slice: 1 : 5">
        </vg-article-card>
      </div>

      <kesma-advertisement-adocean
        *ngIf="adverts?.desktop?.roadblock_2 as ad"
        [ad]="ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
      >
      </kesma-advertisement-adocean>
      <kesma-advertisement-adocean
        *ngIf="adverts?.mobile?.mobilrectangle_2 as ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
        [ad]="ad"
      >
      </kesma-advertisement-adocean>

      <div class="articles">
        <vg-article-card [data]="article" [styleID]="ArticleCardType.FeaturedRightImgDateTagTitle" *ngFor="let article of articles | slice: 5">
        </vg-article-card>
      </div>

      <vg-pager
        *ngIf="limitable?.pageMax! > 0"
        [rowAllCount]="limitable?.rowAllCount!"
        [rowOnPageCount]="limitable?.rowOnPageCount!"
        [isListPager]="true"
        [hasFirstLastButton]="false"
        [hasSkipButton]="true"
        [allowAutoScrollToTop]="true"
        [maxDisplayedPages]="3"
        [showLastPage]="true"
      >
      </vg-pager>

      <kesma-advertisement-adocean
        *ngIf="adverts?.desktop?.roadblock_3 as ad"
        [ad]="ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
      >
      </kesma-advertisement-adocean>

      <kesma-advertisement-adocean
        *ngIf="adverts?.mobile?.mobilrectangle_3 as ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
        [ad]="ad"
      >
      </kesma-advertisement-adocean>
    </div>

    <aside>
      <app-sidebar></app-sidebar>
    </aside>
  </div>
</section>
