:host {
  ::ng-deep {
    .ng-placeholder,
    .ng-input input {
      color: var(--kui-gray-950) !important;
      font-weight: 700 !important;
      font-size: 16px;
      transition: 0.3s;
    }

    .ng-input {
      padding-left: 16px !important;
      padding-right: 80px !important;
    }

    .ng-clear {
      transition: 0.3s;
    }

    .ng-select-container {
      cursor: pointer;

      .ng-input input {
        cursor: pointer !important;
        color: var(--kui-white) !important;
      }

      &:hover {
        .ng-placeholder,
        .ng-clear {
          color: var(--kui-white) !important;
        }
      }
    }

    .ng-select-opened {
      .ng-placeholder {
        color: var(--kui-white) !important;
      }

      .ng-option {
        &-selected,
        &-marked,
        &:hover {
          color: var(--kui-white) !important;
          background: var(--kui-deep-teal-500) !important;
        }
      }
    }
  }
}
