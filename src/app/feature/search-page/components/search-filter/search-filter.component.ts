import { ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { format } from 'date-fns';
import { articleTypeFilters, columnTypeFilters, publishFilters, SEARCH_FILTER_DATE_FORMAT } from '../../api/search-filter.utils';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject } from 'rxjs';
import { map, takeUntil } from 'rxjs/operators';
import { IHttpOptions } from '@trendency/kesma-core';
import { ApiService } from '../../../../shared';
import { TagsPageService } from '../../../tags-page/api/tags-page.service';
import { SearchFilterApiSelectComponent } from '../search-filter-api-select/search-filter-api-select.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { FormsModule } from '@angular/forms';
import { IconComponent } from '@trendency/kesma-ui';

@Component({
  selector: 'app-search-filter',
  templateUrl: 'search-filter.component.html',
  styleUrls: ['search-filter.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormsModule, NgSelectModule, SearchFilterApiSelectComponent, IconComponent],
})
export class SearchFilterComponent implements OnInit, OnDestroy {
  @Output() filterEvent = new EventEmitter<Record<string, string>>();

  authorsSourceRequest = (options: IHttpOptions): any => {
    return this.api.getPublicAuthors('', options);
  };

  authorSingleItemRequest = (slug: string): any => {
    return this.api.getPublicAuthorSocial(slug).pipe(
      // Need to map the response as the DTO is different, and it does not contain the public_author_name
      map((res) => ({
        ...res,
        data: { ...res.data, public_author_name: res.data.publicAuthorName },
      }))
    );
  };

  tagsSourceRequest = (options: IHttpOptions): any => {
    return this.tagService.getAllTags(options);
  };

  tagSingleItemRequest = (slug: string): any => {
    return this.tagService.getTag(slug);
  };

  @Input() set contentTypeFilter(contentTypeFilter: string) {
    this.searchFilter['content_types[]'] = contentTypeFilter;
    this.onSearch();
  }

  @Input() set columnFilters(columns: Record<string, string>[]) {
    if (!columns.length) return;

    this.#columnFilters = [{ ...this.columnTypeFilter[0] }, ...columns.map((column) => ({ label: column['title'], value: column['slug'] }))];
  }

  get columnFilters(): Record<string, string>[] {
    return this.#columnFilters;
  }

  private get searchFilters(): Record<string, string> {
    const filters: Record<string, string> = {};
    Object.entries(this.searchFilter).forEach((entry) => {
      const [key, value] = entry;
      if (value) {
        filters[key] = value;
      }
      if (value && key === 'from_date') {
        filters['to_date'] = format(new Date(), SEARCH_FILTER_DATE_FORMAT);
      }
    });
    return filters;
  }

  readonly articleTypeFilters = articleTypeFilters;
  readonly publishFilters = publishFilters;
  readonly columnTypeFilter = columnTypeFilters;
  #columnFilters: Record<string, string>[] = this.columnTypeFilter;

  destroy$: Subject<void> = new Subject<void>();

  searchFilter: Record<string, string> = {
    global_filter: '',
    from_date: '',
    'columnSlugs[]': '',
    'content_types[]': '',
    'tagSlugs[]': '',
    author: '',
  };

  selectedPublishDate = publishFilters[0];
  selectedArticleType = articleTypeFilters[0];
  selectedColumnSlug = columnTypeFilters[0];
  selectedAuthorSlug = '';
  selectedTagSlug = '';

  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly api: ApiService,
    private readonly cdr: ChangeDetectorRef,
    private readonly tagService: TagsPageService
  ) {}

  ngOnInit(): void {
    this.getRouteParams();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onSearch(): void {
    this.setRouteParams();
    this.filterEvent.emit(this.searchFilters);
  }

  onClearSearch(): void {
    this.searchFilter['global_filter'] = '';
  }

  getRouteParams(): void {
    this.route.data.pipe(takeUntil(this.destroy$)).subscribe(() => {
      const params = this.route.snapshot.queryParams;
      this.searchFilter = { ...this.searchFilter, ...params };

      Object.entries(params).forEach(([paramKey, paramValue]) => {
        switch (paramKey) {
          case 'from_date':
            this.selectedPublishDate = publishFilters.find((filter) => filter.value === paramValue) || publishFilters[0];
            break;
          case 'columnSlugs[]':
            this.selectedColumnSlug = this.columnFilters.find((filter) => filter['value'] === paramValue) || (columnTypeFilters[0] as any);
            break;
          case 'content_types[]':
            this.selectedArticleType = articleTypeFilters.find((filter) => filter.value === paramValue) || (articleTypeFilters[0] as any);
            break;
          case 'tagSlugs[]':
            this.selectedTagSlug = paramValue;
            break;
          case 'author':
            this.selectedAuthorSlug = paramValue;
            break;
        }
      });
      this.cdr.markForCheck();
    });
  }

  setRouteParams(): void {
    const queryParams = this.searchFilter;

    // Remove empty props
    Object.entries(queryParams).forEach(([key, value]) => {
      if (!value) delete queryParams[key];
    });

    // Remove page prop
    Object.entries(queryParams).forEach(([key]) => {
      if (key === 'page') delete queryParams[key];
    });

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
    });
  }
}
