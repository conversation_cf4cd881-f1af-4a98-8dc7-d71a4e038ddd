<div class="search-by-keyword">
  <div class="search-by-keyword-input-wrapper">
    <input class="search-by-keyword-input" type="text" placeholder="Ezt keresem..." (keydown.enter)="onSearch()" [(ngModel)]="searchFilter['global_filter']" />

    <div class="search-by-keyword-input-clear" (click)="onClearSearch()">
      <span>Törlés</span>
      <kesma-icon name="vg-circle-close" [size]="20"></kesma-icon>
    </div>
  </div>

  <button class="search-by-keyword-submit" type="submit" (click)="onSearch()">
    <span>Keresés</span>
    <kesma-icon name="vg-search"></kesma-icon>
  </button>
</div>

<div class="search-filters">
  <div class="search-filters-item">
    <ng-select
      (change)="searchFilter['from_date'] = $event.value"
      [(ngModel)]="selectedPublishDate"
      [searchable]="false"
      [clearable]="false"
      [items]="publishFilters"
      [ariaLabel]="'Dátum (-tól)'"
    >
    </ng-select>
  </div>

  <div class="search-filters-item">
    <ng-select
      (change)="searchFilter['content_types[]'] = $event.value"
      [(ngModel)]="selectedArticleType"
      [searchable]="false"
      [clearable]="false"
      [items]="articleTypeFilters"
      [ariaLabel]="'Cikk típusa'"
    >
    </ng-select>
  </div>

  <div class="search-filters-item">
    <ng-select
      (change)="searchFilter['columnSlugs[]'] = $event.value"
      [(ngModel)]="selectedColumnSlug"
      [searchable]="false"
      [clearable]="false"
      [items]="columnFilters"
      [ariaLabel]="'Rovatok'"
    >
    </ng-select>
  </div>

  <div class="search-filters-item">
    <app-search-filter-api-select
      [singleItemRequest]="authorSingleItemRequest"
      [sourceRequest]="authorsSourceRequest"
      [(ngModel)]="selectedAuthorSlug"
      placeholder="Szerző szerint"
      (ngModelChange)="searchFilter['author'] = $event"
      bindLabel="public_author_name"
      [ariaLabel]="'Szerző szerint'"
    ></app-search-filter-api-select>
  </div>

  <div class="search-filters-item">
    <app-search-filter-api-select
      [singleItemRequest]="tagSingleItemRequest"
      [sourceRequest]="tagsSourceRequest"
      [(ngModel)]="selectedTagSlug"
      placeholder="Cimke szerint"
      (ngModelChange)="searchFilter['tagSlugs[]'] = $event"
      [ariaLabel]="'Címke szerint'"
    ></app-search-filter-api-select>
  </div>
</div>
