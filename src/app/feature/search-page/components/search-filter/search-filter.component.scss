@use 'shared' as *;

:host {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;

  @include media-breakpoint-down(sm) {
    gap: 26px;
  }

  kesma-icon {
    flex: 0 0 auto;
  }
}

.search {
  &-by-keyword {
    display: flex;

    &-input {
      color: var(---kui-gray-400);
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      flex-basis: 100%;
      padding: 0;

      &-clear {
        display: flex;
        gap: 8px;
        align-items: center;
        color: var(--kui-gray-400);
        text-align: center;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
        cursor: pointer;
        width: 72px;
        flex: 0 0 auto;
      }

      &-wrapper {
        display: flex;
        border-radius: 2px;
        border: 1px solid var(--kui-gray-950);
        padding: 10px 8px;
        flex-basis: 100%;
      }
    }

    &-submit {
      display: flex;
      gap: 16px;
      align-items: center;
      justify-content: center;
      color: var(--kui-gray-950);
      font-size: 16px;
      font-weight: 700;
      line-height: 22px;
      width: 312px;
      padding: 10px 16px;
      border-radius: 2px;
      border: 1px solid var(--kui-deep-teal-900);
      transition: 0.3s;

      &:hover {
        background: var(--kui-deep-teal-500);
        color: var(--kui-white);
      }

      @include media-breakpoint-down(sm) {
        padding: 10px;
        width: auto;
      }

      span {
        @include media-breakpoint-down(sm) {
          display: none;
        }
      }
    }
  }

  &-filters {
    display: flex;
    align-items: center;
    gap: 24px;
    flex-wrap: wrap;

    @include media-breakpoint-down(sm) {
      gap: 16px;
    }

    &-item {
      flex-grow: 1;

      @include media-breakpoint-down(sm) {
        width: 100%;
      }

      &.tag-filter {
        height: 36px;
        padding: 10px 16px;
        border-radius: 2px;
        border: 1px solid var(--kui-deep-teal-900);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--kui-gray-950);
        font-size: 16px;
        font-weight: 700;
        line-height: 22px;
        transition: 0.3s;

        &:hover {
          background: var(--kui-deep-teal-500);
          color: var(--kui-white);
        }

        &.active {
          background: var(--kui-deep-teal-900);
          color: var(--kui-white);
        }
      }
    }
  }
}

::ng-deep {
  .search-filters {
    .ng-select {
      &-container {
        background-color: var(--kui-white) !important;
        cursor: pointer !important;
        padding: 10px 16px;
        border-radius: 2px;
        border: 1px solid var(--kui-deep-teal-900);
        transition: 0.3s;
        gap: 8px;

        &:hover {
          background: var(--kui-deep-teal-500) !important;

          .ng-value-label {
            color: var(--kui-white) !important;
          }

          .ng-arrow-wrapper {
            @include icon('icons/chevron-down-white.svg');
          }
        }

        .ng-arrow-wrapper {
          @include icon('icons/chevron-down.svg');
          height: 24px;
          width: 24px;
          transition: 0.3s;

          .ng-arrow {
            display: none !important;
          }
        }
      }

      &-opened {
        .ng-select-container {
          border: 1px solid var(--kui-deep-teal-900);
          background: var(--kui-deep-teal-900) !important;

          .ng-value-label,
          .ng-value-icon {
            color: var(--kui-white) !important;
          }
        }

        .ng-dropdown-panel {
          border: 1px solid var(--kui-deep-teal-900);
          border-top: none;
        }

        .ng-arrow-wrapper {
          transform: rotate(-180deg);
          @include icon('icons/chevron-down-white.svg');
        }
      }

      .ng-value {
        &-label {
          transition: 0.3s;
          color: var(--kui-gray-950);
          font-weight: 700;
        }

        &-container {
          padding: 0;
        }
      }
    }

    .ng-option {
      &-selected,
      &:hover {
        color: var(--kui-white) !important;
        background: var(--kui-deep-teal-500) !important;
      }
    }
  }
}
