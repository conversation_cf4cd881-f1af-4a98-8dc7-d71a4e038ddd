import { Routes } from '@angular/router';
import { SearchPageComponent } from './components/search-page/search-page.component';
import { SearchPageResolver } from './api/search-page.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const SEARCH_PAGE_ROUTES: Routes = [
  {
    path: '',
    component: SearchPageComponent,
    providers: [SearchPageResolver],
    resolve: { data: SearchPageResolver },
    runGuardsAndResolvers: 'always',
    canActivate: [PageValidatorGuard],
  },
];
