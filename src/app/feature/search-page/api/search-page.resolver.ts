import { inject, Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router } from '@angular/router';
import { forkJoin, Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ArticleSearchResult, RedirectService, searchResultToArticleCard } from '@trendency/kesma-ui';
import { ApiService, calculateFilters, ListPageService } from '../../../shared';
import { SearchPageData } from './search-page.definitions';
import { format } from 'date-fns';
import { SEARCH_FILTER_DATE_FORMAT } from './search-filter.utils';

@Injectable()
export class SearchPageResolver {
  constructor(
    private readonly searchPageService: ListPageService,
    private readonly router: Router,
    private readonly apiService: ApiService
  ) {}

  kesmaRedirectService = inject(RedirectService);

  resolve(route: ActivatedRouteSnapshot): Observable<SearchPageData> {
    const maxResultsPerPage = 15;

    let filters = calculateFilters(route.queryParams);

    // If URL contains from_date then set to_date as well
    if ('from_date' in filters && !('to_date' in filters)) {
      filters = { ...filters, ...{ to_date: format(new Date(), SEARCH_FILTER_DATE_FORMAT) } };
    }

    const currentPage = route.queryParams['page'] ? parseInt(route.queryParams['page'], 10) - 1 : 0;
    const columns$ = this.apiService.getParentColumns().pipe(map(({ data }) => data));
    const articlesObservable$ = this.searchPageService.searchArticle(currentPage, maxResultsPerPage, filters);

    return forkJoin({ articles: articlesObservable$, columns: columns$ }).pipe(
      map(({ articles, columns }) => {
        if (this.kesmaRedirectService.shouldBeRedirect(currentPage, articles?.data)) {
          this.kesmaRedirectService.redirectOldUrl(`kereses`, false, 302);
        }
        return {
          articles: articles?.data.map((sr: ArticleSearchResult) => searchResultToArticleCard(sr)),
          columns,
          limitable: articles?.meta?.limitable,
        } as unknown as SearchPageData;
      }),
      catchError((err) => {
        this.router.navigate(['/404'], { skipLocationChange: true }).then();
        return throwError(() => err);
      })
    );
  }
}
