import { format, subDays, subMonths, subWeeks, subYears } from 'date-fns';

export const SEARCH_FILTER_DATE_FORMAT = 'yyyy-MM-dd';

export const publishFilters = [
  {
    label: 'Dátum tartomány',
    value: '',
  },
  {
    label: 'Az utóbbi 24 órában',
    value: format(subDays(new Date(), 1), SEARCH_FILTER_DATE_FORMAT),
  },
  {
    label: 'Az utóbbi egy héten',
    value: format(subWeeks(new Date(), 1), SEARCH_FILTER_DATE_FORMAT),
  },
  {
    label: 'Utóbbi egy hónapban',
    value: format(subMonths(new Date(), 1), SEARCH_FILTER_DATE_FORMAT),
  },
  {
    label: 'Az utóbbi egy évben',
    value: format(subYears(new Date(), 1), SEARCH_FILTER_DATE_FORMAT),
  },
];

export const articleTypeFilters = [
  {
    label: 'Cikk típusa',
    value: '',
  },
  {
    label: 'Videós tartalom',
    value: 'articleVideo',
  },
  {
    label: 'Podcast tartalom',
    value: 'articlePodcast',
  },
  {
    label: 'Vélemény',
    value: 'opinion',
  },
  {
    label: 'Interjú',
    value: 'interview',
  },
];

export const columnTypeFilters = [
  {
    label: 'Rovatok',
    value: '',
  },
];
