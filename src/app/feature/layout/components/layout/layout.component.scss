@use 'shared' as *;

:host {
  display: block;

  vg-opinion-block,
  vg-podcast-box,
  vg-video-block,
  vg-gallery-block {
    ::ng-deep {
      vg-block-title,
      vg-vidcast-podcast-block-title {
        .block-url {
          max-width: 112px;
          width: 100%;
        }
      }
    }
  }

  vg-block-title,
  vg-vidcast-podcast-block-title {
    margin-bottom: 16px;

    ::ng-deep {
      .block-url {
        max-width: 112px;
        width: 100%;
      }
    }
  }

  app-exchange-box {
    ::ng-deep {
      @include media-breakpoint-down(sm) {
        background-color: var(--kui-gray-950);
        width: calc(100% + 48px);
        margin-left: -24px;
        padding: 24px;
      }
    }
  }

  vg-opinion-block {
    &::before,
    &::after {
      content: '';
      display: block;
      position: absolute;
      top: 0;
      width: calc((100vw - 100%) / 2);
      height: 100%;
      z-index: 10;
      background-color: var(--kui-deep-teal-950);
    }

    &::before {
      left: calc((100% - 100vw) / 2);
    }

    &::after {
      right: calc((100% - 100vw) / 2);
    }
  }

  vg-header-image {
    width: 100vw;
    left: 50%;
    margin-left: -50vw;
  }

  vg-conference-box.not-in-sidebar::ng-deep {
    .background-container {
      @include media-breakpoint-up(sm) {
        width: 100vw !important;
        left: 50%;
        margin-left: -50vw;
      }
    }
  }

  vg-conference-box.in-sidebar::ng-deep {
    .conference-info-title-lead {
      padding-left: 41px;
    }

    section {
      @include media-breakpoint-down(sm) {
        margin-inline: 0px;
        width: 100%;
      }
    }
  }

  kesma-layout::ng-deep {
    .layout-element {
      & > .separator {
        display: none !important;
      }

      &.row:not(.has-custom-background) {
        padding-top: 0 !important;
      }
    }
  }

  vg-branding-box {
    margin-top: 40px;

    @include media-breakpoint-down(sm) {
      margin-top: 24px;
    }
  }

  vg-podcast-box {
    &.sidebar {
      background: rgb(38, 38, 38);
    }
  }
}
