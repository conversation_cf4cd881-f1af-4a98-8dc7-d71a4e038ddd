@use 'shared' as *;
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Raleway:wght@700&display=swap');

.free-display-inventory-wrapper {
  background: #f1f1f4;
  padding: 40px 18px 28px;
  margin-bottom: 20px;
  width: 312px;

  @include media-breakpoint-down(sm) {
    width: 100%;
  }

  .they-may-also-be-interested-box-wrapper {
    margin-bottom: 20px;

    h2 {
      position: relative;
      margin-bottom: 6px;

      .text {
        font-family: 'Barlow Condensed', sans-serif;
        font-style: normal;
        font-weight: 900;
        font-size: 20px;
        line-height: 22px;
        text-transform: uppercase;
        color: #000000;
        padding-left: 5px;
        position: relative;
        z-index: 2;
      }

      &:after {
        content: '';
        width: 100%;
        max-width: 225px;
        background: #e70011;
        height: 6px;
        display: block;
        position: relative;
        top: -14px;
        left: 0;
        z-index: 1;
      }

      .sub-text {
        font-family: 'Poppins', sans-serif;
        font-style: normal;
        font-weight: 500;
        font-size: 10px;
        line-height: 12px;
        text-align: center;
        text-transform: uppercase;
        color: #c2c6cf;
        position: absolute;
        top: -20px;
        right: 0;
      }
    }

    .article-wrapper {
      display: flex;
      flex-direction: row;
      margin-bottom: 10px;

      &:hover {
        text-decoration: none;
      }

      .image-wrapper {
        margin-right: 10px;

        img {
          width: 93px;
          height: 93px;
          object-fit: cover;
          max-width: unset;
        }
      }

      .data-wrapper {
        h3 {
          font-family: 'Barlow Condensed', sans-serif;
          font-style: normal;
          font-weight: 900;
          font-size: 14px;
          line-height: 19px;
          text-transform: uppercase;
          color: #000000;
          margin-bottom: 5px;
        }

        .source {
          font-family: 'Barlow Condensed', sans-serif;
          font-style: normal;
          font-weight: 500;
          font-size: 12px;
          line-height: 17px;
          color: #e70011;
          mix-blend-mode: normal;
          text-transform: uppercase;
        }
      }
    }
  }

  .weather-wrapper {
    display: block;
    text-align: center;

    .weather {
      border: 0;
      pointer-events: none;
    }
  }

  .subscription-box {
    display: flex;
    padding: 18px 20px 20px;
    flex-wrap: wrap;
    margin-bottom: 20px;
    background-color: #ffae2b;

    & > div {
      display: flex;
      align-items: center;
      margin-bottom: 21px;

      img {
        width: 76px;
        height: 43px;
      }

      h3 {
        font-family: 'Barlow Condensed', sans-serif;
        font-style: normal;
        font-weight: 900;
        font-size: 20px;
        line-height: 26px;
        text-transform: uppercase;
        color: #000000;
        margin-left: 13px;
        margin-bottom: 0;
      }
    }

    .subscribe-button {
      width: 100%;
      background-color: #e70011;
      padding: 14px 75px;
      display: inline-block;
      font-size: 16px;
      line-height: 24px;
      font-weight: 600;
      border-radius: 30px;
      color: #ffffff;
      text-align: center;
    }
  }

  .sales-sixth-position {
    text-align: center;

    img {
      max-width: unset;
    }
  }

  .real-estate-wrapper {
    margin-bottom: 20px;
    background: #ffffff;
    position: relative;

    .real-estate-header {
      padding: 10px 10px;
      width: 100%;
      display: flex;
      flex-direction: row;
      border-bottom: 1px solid #f1f1f4;

      .logo {
        width: 145px;
      }

      .more-button {
        display: flex;
        align-items: center;
        align-self: center;
        justify-content: center;
        width: 100%;
        border-radius: 4px;
        border: 0.5px solid #c41617;
        padding: 4px 5px 4px 5px;
        margin-left: 20px;

        &:hover {
          text-decoration: none;
          background-color: #c41617;
          transition-duration: 100ms;

          span {
            color: #fff;
          }
        }

        span {
          text-transform: uppercase;
          font-family: 'Open Sans', sans-serif;
          font-style: normal;
          font-weight: 600;
          font-size: 9px;
          line-height: 12px;
          text-align: center;
          color: #c41617;
        }
      }
    }

    .arrow-box {
      height: 30px;
      width: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #c41617;
      position: absolute;
      z-index: 1;

      &.left {
        top: 95px;
        left: 0;
        border-radius: 0 6px 6px 0;
      }

      &.right {
        top: 95px;
        right: 0;
        border-radius: 6px 0 0 6px;
      }

      .arrow {
        width: 15px;

        &.left {
          transform: rotate(180deg);
        }
      }

      &:hover {
        cursor: pointer;
      }
    }

    .real-estate-box {
      padding: 10px 10px 0;
      width: 100%;
      display: flex;
      justify-content: space-between;
    }

    .real-estate-element {
      border-radius: 10px 10px 10px 10px;
      margin-bottom: 10px;
      display: flex;
      flex-direction: column;
      width: 135px;

      &:hover {
        text-decoration: none;
      }

      .image-wrapper {
        img {
          width: 135px;
          height: 106px;
          object-fit: cover;
          display: inline-block;
          max-width: unset;
          border-radius: 10px 10px 0 0;
        }
      }

      .data-wrapper {
        padding: 10px;
        border: 1px solid #f1f1f4;
        border-radius: 0 0 10px 10px;
        height: 120px;

        h3 {
          font-family: 'Open Sans', sans-serif;
          font-style: normal;
          font-weight: 700;
          font-size: 16px;
          line-height: 16px;
          color: #000000;
          margin-bottom: 10px;
        }

        .data-name {
          padding-right: 3px;
          font-family: 'Open Sans', sans-serif;
          display: inline-block;
          font-style: normal;
          font-weight: 700;
          font-size: 10px;
          line-height: 12px;
          color: #000;
        }

        .data-value {
          font-family: 'Open Sans', sans-serif;
          font-style: normal;
          font-weight: 400;
          font-size: 10px;
          line-height: 12px;
          color: #000;

          sup {
            font-size: 7px;
          }
        }

        .description {
          font-family: 'Open Sans', sans-serif;
          font-style: normal;
          font-weight: 300;
          font-size: 10px;
          line-height: 14px;
          color: #000000;
        }
      }
    }
  }

  .videa-wrapper {
    display: block;
    margin-bottom: 20px;
    background: #ffffff;
    padding-bottom: 20px;

    &:hover {
      text-decoration: none;
    }

    .videa-header {
      text-align: center;
      background: #2e2e30;
      height: 40px;
    }

    .videa-picture {
      position: relative;
      margin-bottom: 10px;

      img {
        width: 100%;
        object-fit: cover;
      }

      .play-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }

    .hd {
      position: absolute;
      bottom: 10px;
      left: 10px;
      z-index: 5;
      background-color: red;
      color: white !important;
      font-family: 'Raleway', sans-serif;
      font-weight: bold;
      font-size: 13px;
      padding: 0 0.2em;
      line-height: 13px;
      border: 1px solid red;
      border-radius: 3px;
    }

    .time {
      display: block;
      padding: 0.2em 0.6em 0.3em;
      font-weight: bold;
      line-height: 1;
      text-align: center;
      white-space: nowrap;
      vertical-align: baseline;
      border-radius: 0.25em;
      background-color: #000;
      color: #ffffff;
      font-family: 'Raleway', sans-serif;
      font-size: 10px;
      position: absolute;
      bottom: 10px;
      right: 10px;
      z-index: 5;
    }

    .videa-data-wrapper {
      padding: 0 20px;

      .uploader {
        display: inline-block;
        margin-bottom: 10px;
        text-align: left;
        vertical-align: top;
        padding: 4px 6px;
        white-space: nowrap;
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        font-family: 'Raleway', sans-serif;
        font-weight: 700;
        font-size: 10px;
        background-color: #14478f;
        color: #ffffff;
        border-radius: 0.25em;
        line-height: 1;
        text-transform: uppercase;
      }

      .title {
        font-family: 'Raleway', sans-serif;
        font-style: normal;
        font-weight: 700;
        font-size: 16px;
        line-height: 16px;
        color: #2e2e30;
        margin-bottom: 14px;
      }

      .sub-button {
        font-family: 'Raleway', sans-serif;
        font-style: normal;
        font-weight: 700;
        font-size: 12px;
        line-height: 16px;
        color: #ffffff;
        letter-spacing: -0.01em;
        text-transform: uppercase;
        text-align: center;
        background: #dc102f;
        border-radius: 3px;
        padding: 7px 0;
      }
    }
  }
}
