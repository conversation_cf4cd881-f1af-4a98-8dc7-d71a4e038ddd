import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { EnvironmentApiUrl, ReqService, UtilService } from '@trendency/kesma-core';
import { ApiResult, mapIriszIngatlanDataToRealEstateData, RealEstateBazaarBlockComponent } from '@trendency/kesma-ui';
import {
  IriszBackendIngatlanData,
  IriszBackendResponse,
  RealEstateBazaarData,
} from '@trendency/kesma-ui/lib/components/real-estate-bazaar-block/real-estate-bazaar.definitions';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { campaigns } from './free-display-campaigns.consts';
import { articles, FreeDisplayInventory, videaData } from './free-display-inventory.definitions';
import { environment } from '../../../../../environments/environment';
import { CleanHttpService } from '../../../../shared';
import { NgFor, NgIf } from '@angular/common';

@Component({
  selector: 'app-free-display-inventory',
  templateUrl: './free-display-inventory.component.html',
  styleUrls: ['./free-display-inventory.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, RealEstateBazaarBlockComponent],
})
export class FreeDisplayInventoryComponent implements OnInit, AfterViewInit {
  adBlockerIsActive = false;
  loading = false;
  unsubscribe = new Subject<boolean>();
  public articles: articles[];
  // public salesSixthPosition: salesSixthPosition;
  public realEstateData: RealEstateBazaarData[] = [];
  public videaVideoUrl: string;
  public videaChannelName: string;
  public videaData: videaData;
  public realEstateCampaignParams: string;
  public weatherCampaignParams: string;
  public newsletterLinkWithCampaign: string;

  constructor(
    private readonly reqService: ReqService,
    private readonly utilsService: UtilService,
    private readonly cd: ChangeDetectorRef,
    private readonly httpService: CleanHttpService
  ) {}

  get iriszApiUrl(): string {
    if (typeof environment.iriszApiUrl === 'string') {
      return environment.iriszApiUrl as string;
    }

    const { clientApiUrl, serverApiUrl } = environment.iriszApiUrl as EnvironmentApiUrl;
    return this.utilsService.isBrowser() ? clientApiUrl : serverApiUrl;
  }

  ngOnInit(): void {
    this.getRealEstateData();
  }

  getData(): void {
    this.reqService
      .get<ApiResult<FreeDisplayInventory>>('/free-display-feed')
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(({ data }) => {
        this.articles = this.mapArticleLinks(data['news-line']);

        // this.salesSixthPosition = data.['sales_6th_position'];
        if (data.videa.videos !== undefined) {
          this.videaData = data.videa.videos[0];
          this.videaChannelName = data.videa.videos[0].channel.name;
          const regex = /url=([^&]*)/gi;
          this.videaVideoUrl = decodeURIComponent(regex.exec(data.videa?.videos?.[0]?.oembed_url)?.[1] + this.createCampaignParams(campaigns.videa));
        }

        this.loading = true;
        this.cd.detectChanges();
      });
  }

  ngAfterViewInit(): void {
    if (!this.utilsService.isBrowser()) {
      return;
    }

    this.getAdBlockerIsActive();
    if (this.adBlockerIsActive) {
      this.getData();

      this.newsletterLinkWithCampaign = `https://vg.hu/hirlevel-feliratkozas`;
      this.realEstateCampaignParams = this.createCampaignParams(campaigns.realEstate);
      this.weatherCampaignParams = this.createCampaignParams(campaigns.weather);
    }
  }

  mapArticleLinks(articles: articles[]): articles[] {
    return articles.map((article, i) => {
      return { ...article, link: this.replaceArticleLink(article.link, i) };
    });
  }

  replaceArticleLink(link: string, index: number): string {
    const clearedLink = link.split('?')[0];
    return clearedLink + this.createCampaignParams(campaigns.articles[index]);
  }

  getAdBlockerIsActive(): void {
    if (typeof window.ado !== 'object') {
      this.adBlockerIsActive = true;
    }
  }

  createCampaignParams(param: any): string {
    return `?utm_source=${param.source}&utm_medium=${param.medium}&utm_campaign=${param.campaign}`;
  }

  getRealEstateData(): void {
    this.httpService.get(`${this.iriszApiUrl}/inventory/display/vilaggazdasag`).subscribe((data: IriszBackendResponse) => {
      data?.ingatlanbazar?.forEach((realEstate: IriszBackendIngatlanData) => {
        this.realEstateData.push(mapIriszIngatlanDataToRealEstateData(realEstate));
      });
      this.cd.detectChanges();
    });
  }
}
