<div class="free-display-inventory-wrapper" *ngIf="adBlockerIsActive && loading">
  <div class="they-may-also-be-interested-box-wrapper">
    <h2><span class="text"><PERSON><PERSON><PERSON> is <PERSON>rdekelhetnek</span><span class="sub-text">Hirde<PERSON>s</span></h2>
    <a *ngFor="let element of articles" href="{{ element.link }}" class="article-wrapper" target="_blank">
      <div class="image-wrapper">
        <img loading="lazy" src="{{ element.images[0].image_url }}" alt="{{ element.title }}" />
      </div>
      <div class="data-wrapper">
        <h3>{{ element.title }}</h3>
        <div class="source">{{ element.source_name }}</div>
      </div>
    </a>
  </div>

  <a class="weather-wrapper" [href]="'http://koponyeg.hu' + weatherCampaignParams" target="_blank">
    <iframe class="weather" width="300" height="100" style="margin-bottom: 16px" src="https://koponyeg.hu/add-on-embed?w=300&r=1&c=light"> </iframe
  ></a>

  <a *ngIf="videaData" href="{{ videaVideoUrl }}" class="videa-wrapper" target="_blank">
    <div class="videa-header">
      <img loading="lazy" src="/assets/images/videa-logo.svg" alt="Videa logo" />
    </div>
    <div class="videa-picture">
      <img loading="lazy" src="{{ videaData.thumbnail }}" alt="{{ videaData.title }}" />
      <div class="play-icon">
        <img loading="lazy" src="/assets/images/videa-play-icon.svg" alt="Play icon" />
      </div>
      <span class="hd">HD</span>
      <div class="time">{{ videaData.duration }}</div>
    </div>
    <div class="videa-data-wrapper">
      <div class="uploader">{{ videaChannelName }}</div>
      <div class="title">{{ videaData.title }}</div>
      <div class="sub-button">Feliratkozom a Világgazdaság csatornájára</div>
    </div>
  </a>

  <div class="subscription-box">
    <div>
      <img loading="lazy" src="/assets/images/mail-icon.svg" alt="Mail icon" />
      <h3>Iratkozzon fel a Világgazdaság hírlevelére!</h3>
    </div>
    <a [href]="newsletterLinkWithCampaign" class="subscribe-button"><span>Feliratkozom</span></a>
  </div>

  <ng-container *ngIf="realEstateData.length > 0">
    <kesma-real-estate-bazaar-block [showHeader]="true" [itemsToShow]="2" [data]="realEstateData"></kesma-real-estate-bazaar-block>
  </ng-container>
</div>
