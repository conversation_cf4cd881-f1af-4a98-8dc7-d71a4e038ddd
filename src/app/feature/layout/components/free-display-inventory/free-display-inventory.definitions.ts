/* eslint-disable functional/prefer-readonly-type */
export type videaData = Readonly<{
  channel: videaDataChannel;
  oembed_url: string;
  thumbnail: string;
  title: string;
  duration: string;
}>;

export type videaDataChannel = Readonly<{
  id: string;
  name: string;
}>;

export type articles = Readonly<{
  link: string;
  images: articleImage[];
  title: string;
  source_name: string;
}>;

export type articleImage = Readonly<{
  id: string;
  image_url: string;
}>;

export type realEstate = Readonly<{
  description: string;
  seoUrl: string;
  renderPictures: string;
  priceHUF: number;
  url?: string;
  rooms: number;
  area: number;
  settlement: string;
}>;

export interface FreeDisplayInventory {
  'news-line': articles[];
  ingatlanbazar: {
    hits: realEstate[];
  };
  videa: {
    videos: videaData[];
  };
}
