import { Injectable } from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { BehaviorSubject, map, Observable, take } from 'rxjs';
import { ReqService, UtilService } from '@trendency/kesma-core';
import { AgroData, AgroResponse } from '../../../shared';

@Injectable({
  providedIn: 'root',
})
export class AgrokepService {
  constructor(
    private readonly utils: UtilService,
    private readonly reqService: ReqService
  ) {}

  agrokepSubject = new BehaviorSubject<AgroData[]>([]);
  dataRequested = false;

  getAgrokepArticles(): Observable<AgroData[]> | undefined {
    if (!this.utils.isBrowser()) {
      return;
    }

    if (!this.dataRequested) {
      this.dataRequested = true;
      const params = new HttpParams().append('cache', new Date().getTime().toString());
      this.reqService
        .get<AgroResponse>(`/mediaworks/agrokep`, {
          params,
        })
        .pipe(
          take(1),
          map(({ data }) => data),
          map((articles) => this.agrokepSubject.next(articles))
        )
        .subscribe();
    }

    return this.agrokepSubject;
  }
}
