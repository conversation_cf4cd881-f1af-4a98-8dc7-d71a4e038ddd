import { inject, Injectable } from '@angular/core';
import { catchError, map, Observable, of } from 'rxjs';
import { CleanHttpService, mapTrafficDeflectorArticlesToBrandingBoxArticle, VgBrandingBoxArticle } from '../../../shared';
import { environment } from 'src/environments/environment';
import { PersonalizedRecommendationApiResponse } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class TrafficDeflectorService {
  private readonly httpService = inject(CleanHttpService);

  private get deflectorApiUrl(): string {
    return environment.type === 'beta' ? 'https://terelo.app.content.private/api' : (environment.personalizedRecommendationApiUrl as string);
  }

  getTrafficDeflectorData(traffickingPlatforms: string, articleLimit: number): Observable<VgBrandingBoxArticle[] | undefined> {
    return this.httpService
      .get<PersonalizedRecommendationApiResponse>(`${this.deflectorApiUrl}/recommendation`, {
        params: {
          traffickingPlatforms,
          utmSource: 'vg.hu',
          withoutPos: '1',
        },
      })
      .pipe(
        map((data) => data?.[traffickingPlatforms]?.map(mapTrafficDeflectorArticlesToBrandingBoxArticle)?.slice(0, articleLimit)),
        catchError(() => {
          return of(undefined);
        })
      );
  }
}
