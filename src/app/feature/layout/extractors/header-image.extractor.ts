import { Injectable } from '@angular/core';
import { DataExtractorFunction, HeaderImageContent, LayoutDataExtractorService, LayoutElementContent } from '@trendency/kesma-ui';

@Injectable()
export class HeaderImageExtractor implements LayoutDataExtractorService<HeaderImageContent | undefined> {
  extractData: DataExtractorFunction<HeaderImageContent | undefined> = (element: LayoutElementContent) => {
    const config = element.config as any;
    if (!config) {
      return;
    }

    const data: HeaderImageContent = {
      backgroundImage: config?.selectedImage?.selectedVariant?.publicUrl,
      logo: config?.logo?.selectedVariant?.publicUrl,
      title: config?.title,
      url: config?.url,
      height: 90,
    };

    return {
      data,
      meta: {
        extractedBy: HeaderImageExtractor.name,
      },
    };
  };
}
