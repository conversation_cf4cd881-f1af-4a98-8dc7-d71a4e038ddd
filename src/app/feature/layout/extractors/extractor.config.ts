import { ArticleArrayExtractor, ArticleCardExtractor, DataExtractor, LayoutElementContentType, WysiwygExtractor } from '@trendency/kesma-ui';
import { ConferenceExtractor } from './conference.extractor';
import { HeaderImageExtractor } from './header-image.extractor';

export const VG_EXTRACTORS_CONFIG: DataExtractor<unknown>[] = [
  {
    extractor: ArticleArrayExtractor,
    supportedContentTypes: [LayoutElementContentType.MOST_VIEWED],
  },
  {
    extractor: ArticleCardExtractor,
    supportedContentTypes: [LayoutElementContentType.PODCAST_ARTICLE_LIST, LayoutElementContentType.MinuteToMinute],
  },
  {
    extractor: ConferenceExtractor,
    supportedContentTypes: [LayoutElementContentType.CONFERENCE],
  },
  {
    extractor: HeaderImageExtractor,
    supportedContentTypes: [LayoutElementContentType.Image],
    priority: 1000,
  },
  {
    extractor: WysiwygExtractor,
    supportedContentTypes: [LayoutElementContentType.Wysiwyg],
  },
];
