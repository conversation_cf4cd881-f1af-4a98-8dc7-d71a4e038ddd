import { Injectable } from '@angular/core';
import {
  Conference,
  DataExtractorFunction,
  LayoutDataExtractorService,
  LayoutElementContent,
  LayoutElementContentConfigurationConference,
} from '@trendency/kesma-ui';

@Injectable()
export class ConferenceExtractor implements LayoutDataExtractorService<Conference | undefined> {
  extractData: DataExtractorFunction<Conference | undefined> = (element: LayoutElementContent) => {
    const config = element.config as LayoutElementContentConfigurationConference;
    if (!config) {
      return;
    }

    const data: Conference = {
      title: config.title,
      lead: config.lead,
      backgroundImg1: config.indexImage1?.selectedVariant?.publicUrl || './assets/images/vg-placeholder-16-9.svg',
      date: config.date,
      venue: config.location,
      backgroundImg2: config.indexImage2?.selectedVariant?.publicUrl || './assets/images/vg-placeholder-16-9.svg',
      url: config.url,
    };

    return {
      data,
      meta: {
        extractedBy: ConferenceExtractor.name,
      },
    };
  };
}
