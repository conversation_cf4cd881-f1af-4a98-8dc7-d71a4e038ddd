import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { ArticlePageComponent } from '../article-page/components/article-page/article-page.component';
import { StaticPageComponent } from '../static-page/components/static-page/static-page.component';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-slug-route-handler',
  templateUrl: './slug-route-handler.component.html',
  styleUrls: ['./slug-route-handler.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, StaticPageComponent, ArticlePageComponent],
})
export class SlugRouteHandlerComponent implements OnInit, OnDestroy {
  staticPage: boolean;

  unsubscribe$: Subject<void> = new Subject<void>();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.route.data.pipe(takeUntil(this.unsubscribe$)).subscribe((res) => {
      this.staticPage = !(res?.['articlePageData']?.article || res?.['articlePageData']?.data);
      this.cdr.detectChanges();
    });
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
