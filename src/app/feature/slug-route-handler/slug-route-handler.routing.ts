import { Routes } from '@angular/router';
import { SlugRouteHandlerComponent } from './slug-route-handler.component';
import { StaticPageResolver } from '../static-page/api/static-page.resolver';
import { ArticleResolver } from '../article-page/api/article-page.resolver';

export const STATIC_PAGE_ROUTES: Routes = [
  {
    path: '',
    component: SlugRouteHandlerComponent,
    providers: [StaticPageResolver, ArticleResolver],
    resolve: {
      staticPageData: StaticPageResolver,
      articlePageData: ArticleResolver,
    },
  },
];
