import { Routes } from '@angular/router';
import { CategoryPageComponent } from './components/category-page.component';
import { CategoryResolver } from './api/category.resolver';
import { PageValidatorGuard } from '@trendency/kesma-ui';

export const CATEGORY_ROUTES: Routes = [
  {
    path: '',
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    pathMatch: 'full',
    component: CategoryPageComponent,
    canActivate: [PageValidatorGuard],
    providers: [CategoryResolver],
    resolve: {
      pageData: CategoryResolver,
    },
    data: { categoryRouteType: 'category-layout' },
  },
  {
    path: ':year',
    pathMatch: 'full',
    canActivate: [PageValidatorGuard],
    component: CategoryPageComponent,
    providers: [CategoryResolver],
    resolve: {
      pageData: CategoryResolver,
    },
    data: { categoryRouteType: 'category-year' },
  },
  {
    path: ':month',
    runGuardsAndResolvers: 'paramsOrQueryParamsChange',
    pathMatch: 'full',
    component: CategoryPageComponent,
    canActivate: [PageValidatorGuard],
    providers: [CategoryResolver],
    resolve: {
      pageData: CategoryResolver,
    },
    data: { categoryRouteType: 'category-month' },
  },
];
