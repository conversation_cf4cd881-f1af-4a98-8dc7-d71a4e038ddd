import { ApiResponseMeta<PERSON>ist, <PERSON><PERSON><PERSON><PERSON><PERSON>, ArticleCard, AutoFill, LayoutStruct, SelectedArticle, Sponsorship } from '@trendency/kesma-ui';

export const articlesPageSize = 12;

export type CategoryRouteType = 'category-layout' | 'category-year' | 'category-month';

export type CategoryResolverResponse = {
  layoutApiResponse: Layout | null;
  columnParentTitle?: string;
  excludedIds: string[];
  columnTitle: string;
  category: ApiResult<ArticleCard[], ApiResponseMetaList>;
  slug: string;
  year: string;
  month: string;
  sponsorship: Sponsorship;
};

export type Layout = {
  struct: LayoutStruct[];
  content: LayoutContent[];
};

export type LayoutContent = {
  autoFill: AutoFill;
  hasImage: boolean;
  layoutElementId: string;
  selectedArticles: SelectedArticle[];
  selectedOpinions: selectedOpinions[];
};

type selectedOpinions = {
  id: string;
  data: Data;
};

type Data = {
  id: string;
  slug: string;
  title: string;
  excerpt?: string;
  columnId: string;
  isActive?: string;
  sponsorId?: string[];
  columnSlug: string;
  priorityId?: string;
  columnTitle: string;
  publishDate: string;
  sponsorTitle?: string[];
  thumbnailUrl?: string;
  priorityTitle?: string;
  columnParentId?: any;
  reading_length?: string;
  columnParentSlug?: any;
  publicAuthorName?: string;
  columnParentTitle?: any;
  publicAuthorAvatarThumbnailUrl?: string;
  lead?: string;
};
