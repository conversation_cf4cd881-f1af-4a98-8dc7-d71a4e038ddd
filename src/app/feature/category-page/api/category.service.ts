import { inject, Injectable } from '@angular/core';
import { Params, Router } from '@angular/router';
import { ApiResponseMetaList, ApiResult, ArticleCard, LayoutWithExcludeIds, RedirectService } from '@trendency/kesma-ui';
import { Observable, throwError } from 'rxjs';
import { catchError, map, switchMap } from 'rxjs/operators';
import { articlesPageSize, CategoryResolverResponse } from './category.definitions';
import { ApiService, mapCategoryResponse } from '../../../shared';
import { LayoutService } from '../../layout/services/layout.service';

@Injectable({ providedIn: 'root' })
export class CategoryService {
  kesmaRedirectService = inject(RedirectService);

  public constructor(
    private readonly apiService: ApiService,
    private readonly layoutService: LayoutService,
    private readonly router: Router
  ) {}

  public getRequestForCategoryLayout(params: Params, queryParams: Params): Observable<CategoryResolverResponse> {
    const { categorySlug } = params;
    const { page } = queryParams;
    const pageIndex = page ? page - 1 : 0;

    return this.layoutService.getLayoutWithExcludeIds(categorySlug).pipe(
      switchMap((layoutResponse: LayoutWithExcludeIds) => {
        return this.apiService.getCategoryArticles(categorySlug, pageIndex, articlesPageSize, undefined, undefined, layoutResponse.excludedIds).pipe(
          map((res: any) => {
            if (this.kesmaRedirectService.shouldBeRedirect(pageIndex, res?.data)) {
              this.kesmaRedirectService.redirectOldUrl(`rovat/${categorySlug}`, false, 302);
            }
            return res;
          }),
          catchError((error) => {
            this.router
              .navigate(['/', '404'], {
                state: { errorResponse: JSON.stringify(error) },
                skipLocationChange: true,
              })
              .then();
            return throwError(() => error);
          }),
          map((categoryResponse: ApiResult<ArticleCard[], ApiResponseMetaList>) => mapCategoryResponse(categoryResponse, categorySlug, '', '', layoutResponse))
        );
      })
    );
  }

  public getRequestForCategoryByDate(params: Params, queryParams: Params): Observable<CategoryResolverResponse> {
    const { categorySlug, year, month } = params;
    const { page } = queryParams;
    const pageIndex = page ? page - 1 : 0;
    const request$ = this.layoutService.getLayoutWithExcludeIds(categorySlug).pipe(
      switchMap((layoutResponse) =>
        this.apiService.getCategoryArticles(categorySlug, pageIndex, articlesPageSize, year, month, []).pipe(
          map((categoryResponse: ApiResult<any[], ApiResponseMetaList>) => {
            if (this.kesmaRedirectService.shouldBeRedirect(pageIndex, categoryResponse?.data)) {
              this.kesmaRedirectService.redirectOldUrl(`rovat/${categorySlug}`, false, 302);
            }
            return mapCategoryResponse(categoryResponse, categorySlug, year, month, layoutResponse as LayoutWithExcludeIds, true);
          }),
          catchError((error) => {
            this.router
              .navigate(['/', '404'], {
                state: { errorResponse: JSON.stringify(error) },
                skipLocationChange: true,
              })
              .then();
            return throwError(() => error);
          })
        )
      )
    );
    if (isNaN(year) || isNaN(month)) {
      this.router
        .navigate(['/', '404'], {
          skipLocationChange: true,
        })
        .then();
    }
    return request$;
  }
}
