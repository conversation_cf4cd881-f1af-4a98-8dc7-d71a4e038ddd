import { ActivatedRouteSnapshot } from '@angular/router';
import { CategoryResolverResponse, CategoryRouteType } from './category.definitions';
import { CategoryService } from './category.service';
import { Observable } from 'rxjs';
import { Injectable } from '@angular/core';

@Injectable()
export class CategoryResolver {
  public constructor(private readonly categoryService: CategoryService) {}

  public resolve(route: ActivatedRouteSnapshot): Observable<CategoryResolverResponse> {
    const categoryRouteType: CategoryRouteType = route.data['categoryRouteType'];
    const params = route.params;
    const queryParams = route.queryParams;

    switch (categoryRouteType) {
      case 'category-layout':
        return this.categoryService.getRequestForCategoryLayout(params, queryParams);
      case 'category-year':
        return this.categoryService.getRequestForCategoryByDate(params, queryParams);
      case 'category-month':
        return this.categoryService.getRequestForCategoryByDate(params, queryParams);
    }
  }
}
