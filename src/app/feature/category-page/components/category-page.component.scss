@use 'shared' as *;

:host {
  display: block;
  margin-top: 18px;
}

.inner-wrapper {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.sponsorship {
  display: flex;
  width: 100%;
  padding: 20px 0;
  max-height: 98px;
  justify-content: space-around;
  margin-bottom: 16px;

  @include media-breakpoint-down(md) {
    max-height: 64px;
    padding: 18px;
    width: 100vw;
    left: 50%;
    margin-left: -50vw;
    position: relative;
  }

  &-text {
    align-self: center;
    font-size: 18px;
    font-weight: 500;
    line-height: 24px;

    @include media-breakpoint-down(md) {
      font-size: 12px;
      line-height: 14px;
    }
  }

  &-logo {
    position: relative;
    width: 50%;
    height: 51px;

    @include media-breakpoint-down(md) {
      height: 31px;
    }

    img {
      position: absolute;
      top: 0;
      right: 0;
      height: 100%;
      object-fit: contain;
    }
  }
}

//TODO: edit this when ads are ready
.ad-block {
  background-color: var(--kui-gray-100);
  padding: 24px 0;

  &.in-left-column {
    padding: 0;
    padding-bottom: 16px;
  }
}

.full-width {
  width: calc(100% + 140px);
  margin-inline: -70px;
}

.with-aside {
  margin-top: 25px;
}
