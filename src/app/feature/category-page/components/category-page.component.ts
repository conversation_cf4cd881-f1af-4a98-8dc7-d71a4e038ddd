import { ChangeDetectionStrategy, ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Subject } from 'rxjs';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementsByMedium,
  AnalyticsService,
  ArticleCard,
  createCanonicalUrlForPageablePage,
  LayoutApiData,
  LimitableMeta,
  mapBackendArticleDataToArticleCard,
  Sponsorship,
} from '@trendency/kesma-ui';
import { ActivatedRoute } from '@angular/router';
import { IMetaData, SeoService } from '@trendency/kesma-core';
import { map, takeUntil, tap } from 'rxjs/operators';
import {
  ApiService,
  ArticleCardType,
  createVilaggazdasagTitle,
  defaultMetaInfo,
  NewsletterBoxType,
  VgArticleCardComponent,
  VgBlockTitleComponent,
  VgNewsletterBoxComponent,
  VgPagerComponent,
} from '../../../shared';
import { SidebarComponent } from '../../layout/components/sidebar/sidebar.component';
import { LayoutComponent } from '../../layout/components/layout/layout.component';
import { NgFor, NgIf, SlicePipe } from '@angular/common';

@Component({
  selector: 'app-category-page',
  templateUrl: './category-page.component.html',
  styleUrls: ['./category-page.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    LayoutComponent,
    VgArticleCardComponent,
    NgFor,
    SidebarComponent,
    SlicePipe,
    VgNewsletterBoxComponent,
    VgPagerComponent,
    VgBlockTitleComponent,
    AdvertisementAdoceanComponent,
  ],
})
export class CategoryPageComponent implements OnInit, OnDestroy {
  private readonly destroy$: Subject<boolean> = new Subject();

  readonly ArticleCardType = ArticleCardType;
  readonly NewsletterBoxType = NewsletterBoxType;

  columnTitle?: string;
  layoutData?: LayoutApiData;
  limitable?: LimitableMeta;
  adverts?: AdvertisementsByMedium;
  sponsorship?: Sponsorship;
  rowAllCount: number;
  rowOnPageCount: number;
  rowFrom: number;
  slug: string;
  excludedIds: string[] = [];
  simpleArticles: ArticleCard[];

  constructor(
    private readonly route: ActivatedRoute,
    private readonly analyticsService: AnalyticsService,
    private readonly seo: SeoService,
    private readonly cdr: ChangeDetectorRef,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly apiService: ApiService
  ) {}

  ngOnInit(): void {
    this.route.data
      .pipe(
        map(({ pageData }) => pageData),
        takeUntil(this.destroy$)
      )
      .subscribe((pageData) => {
        this.excludedIds = pageData?.excludedIds;
        this.columnTitle = pageData?.columnTitle;

        this.layoutData = pageData?.layoutApiResponse;
        this.limitable = pageData?.category?.meta?.limitable;
        this.rowOnPageCount = pageData?.category?.meta?.limitable.rowOnPageCount;
        this.rowAllCount = pageData?.category?.meta?.limitable.rowAllCount;
        this.rowFrom = pageData?.category?.meta?.limitable.rowFrom;
        this.slug = pageData?.slug;
        this.sponsorship = pageData?.sponsorship;
        this.analyticsService.sendPageView({ pageCategory: pageData?.slug });
        this.setMetaData(pageData?.columnTitle);

        this.cdr.markForCheck();
      });
    this.activatedRoute.queryParams
      .pipe(
        tap(() => this.initAds()),
        takeUntil(this.destroy$)
      )
      .subscribe(({ page }) => {
        this.fetchSimpleArticlesPage(parseInt(page, 10) - 1 || 0);
        if (this.columnTitle) {
          this.setMetaData(this.columnTitle);
        }
      });
  }

  private setMetaData(title: string): void {
    const canonical = createCanonicalUrlForPageablePage('rovat', this.route.snapshot);
    canonical && this.seo.updateCanonicalUrl(canonical);
    title = createVilaggazdasagTitle(title);
    const metaData: IMetaData = {
      ...defaultMetaInfo,
      title: title,
      ogTitle: title,
      description: defaultMetaInfo.description,
      ogDescription: defaultMetaInfo.ogDescription,
    };
    this.seo.setMetaData(metaData);
  }

  private fetchSimpleArticlesPage(nextPage: number): void {
    this.apiService.getCategoryArticles(this.slug, nextPage, this.rowOnPageCount, undefined, undefined, this.excludedIds).subscribe((res) => {
      this.simpleArticles = res.data.map(mapBackendArticleDataToArticleCard);
      this.cdr.detectChanges();
    });
  }

  initAds(): void {
    this.resetAds();
    this.adStoreAdo.advertisemenets$.pipe(takeUntil(this.destroy$)).subscribe((ads: Advertisement[]) => {
      this.adverts = this.adStoreAdo.separateAdsByMedium(ads);
      this.cdr.detectChanges();
    });
  }

  resetAds(): void {
    this.adverts = undefined;
    this.cdr.detectChanges();
  }

  trackByFn = (index: number): number => index;

  ngOnDestroy(): void {
    this.adStoreAdo.setArticleParentCategory('');
    this.destroy$.next(true);
    this.destroy$.complete();
  }
}
