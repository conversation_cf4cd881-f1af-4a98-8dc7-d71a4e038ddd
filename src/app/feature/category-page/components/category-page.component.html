<section>
  <div class="wrapper">
    <a *ngIf="sponsorship" [href]="sponsorship?.url" [style.background-color]="sponsorship?.highlightedColor" class="sponsorship" target="_blank">
      <div [style.color]="sponsorship?.fontColor" class="sponsorship-text">A rovat tartalmának támogatója:</div>
      <div class="sponsorship-logo">
        <img *ngIf="sponsorship?.thumbnailUrl" [src]="sponsorship?.thumbnailUrl" alt="Szponzor logó" />
      </div>
    </a>
    <vg-block-title *ngIf="columnTitle" [data]="{ text: columnTitle }" [headingLevel]="1"></vg-block-title>
    <app-layout *ngIf="layoutData" [configuration]="layoutData?.content" [structure]="layoutData?.struct"></app-layout>
    <div class="ad-block full-width">
      <kesma-advertisement-adocean
        *ngIf="adverts?.desktop?.roadblock_1 as ad"
        [ad]="ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
      >
      </kesma-advertisement-adocean>

      <kesma-advertisement-adocean
        *ngIf="adverts?.mobile?.mobilrectangle_1 as ad"
        [ad]="ad"
        [style]="{
          margin: 'var(--ad-margin)',
          background: 'var(--ad-bg-light)',
          padding: 'var(--ad-padding)',
        }"
      >
      </kesma-advertisement-adocean>
    </div>
  </div>
  <div class="wrapper with-aside">
    <div class="left-column">
      <div class="inner-wrapper">
        <vg-newsletter-box [styleID]="NewsletterBoxType.Gong"></vg-newsletter-box>
        <ng-container *ngFor="let article of simpleArticles | slice: 0; let i = index; trackBy: trackByFn">
          <vg-article-card [data]="article" [styleID]="ArticleCardType.FeaturedRightImgDateTagTitle"></vg-article-card>
          <div *ngIf="i === 4" class="ad-block in-left-column">
            <kesma-advertisement-adocean
              *ngIf="adverts?.desktop?.roadblock_2 as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg-light)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>

            <kesma-advertisement-adocean
              *ngIf="adverts?.mobile?.mobilrectangle_2 as ad"
              [ad]="ad"
              [style]="{
                margin: 'var(--ad-margin)',
                background: 'var(--ad-bg-light)',
                padding: 'var(--ad-padding)',
              }"
            >
            </kesma-advertisement-adocean>
          </div>
        </ng-container>
        <vg-pager
          *ngIf="rowAllCount > rowOnPageCount"
          [allowAutoScrollToTop]="true"
          [hasSkipButton]="true"
          [isListPager]="true"
          [maxDisplayedPages]="5"
          [rowAllCount]="rowAllCount || 0"
          [rowOnPageCount]="rowOnPageCount || 0"
        >
        </vg-pager>
        <div class="ad-block in-left-column">
          <kesma-advertisement-adocean
            *ngIf="adverts?.desktop?.roadblock_3 as ad"
            [ad]="ad"
            [style]="{
              margin: 'var(--ad-margin)',
              background: 'var(--ad-bg-light)',
              padding: 'var(--ad-padding)',
            }"
          >
          </kesma-advertisement-adocean>

          <kesma-advertisement-adocean
            *ngIf="adverts?.mobile?.mobilrectangle_3 as ad"
            [ad]="ad"
            [style]="{
              margin: 'var(--ad-margin)',
              background: 'var(--ad-bg-light)',
              padding: 'var(--ad-padding)',
            }"
          >
          </kesma-advertisement-adocean>
        </div>
      </div>
    </div>
    <aside>
      <app-sidebar [excludedIds]="excludedIds"></app-sidebar>
    </aside>
  </div>
</section>
