import { environment } from '../environments/environment';
import { DateFnsConfigurationService } from 'ngx-date-fns';
import { hu } from 'date-fns/locale';
import { ApplicationConfig, importProvidersFrom, provideZoneChangeDetection } from '@angular/core';
import { AppEnvironment, provideEncodedTransferState } from '@trendency/kesma-core';
import { provideHttpClient, withInterceptors, withInterceptorsFromDi } from '@angular/common/http';
import { adDebugFactory, DEV_AD_DEBUG } from '@trendency/kesma-ui';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideRouter, withEnabledBlockingInitialNavigation, withInMemoryScrolling } from '@angular/router';
import { GoogleTagManagerModule } from 'angular-google-tag-manager';
import { routes } from './app.routing';
import { register as registerSwiperElement } from 'swiper/element/bundle';
import { elasticIndexWarningInterceptor, portalHeaderHttpInterceptor } from './shared';

const GTAG_PROVIDER = [{ provide: 'googleTagManagerId', useValue: environment.googleTagManager }];
const huConfig = new DateFnsConfigurationService();
huConfig.setLocale(hu);

registerSwiperElement();

export const appConfig: ApplicationConfig = {
  providers: [
    provideEncodedTransferState(),
    provideAnimations(),
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes, withEnabledBlockingInitialNavigation(), withInMemoryScrolling({ scrollPositionRestoration: 'enabled', anchorScrolling: 'enabled' })),
    provideHttpClient(withInterceptorsFromDi(), withInterceptors([portalHeaderHttpInterceptor, elasticIndexWarningInterceptor])),
    importProvidersFrom(GoogleTagManagerModule),
    {
      provide: AppEnvironment,
      useValue: environment,
    },
    {
      provide: DateFnsConfigurationService,
      useValue: huConfig,
    },
    {
      provide: DEV_AD_DEBUG,
      useFactory: adDebugFactory,
      deps: [AppEnvironment],
    },
    ...GTAG_PROVIDER,
  ],
};
