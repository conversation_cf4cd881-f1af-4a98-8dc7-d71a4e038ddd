import { Injectable } from '@angular/core';
import { InitResolverData, InitResponse, RelatedType, SimplifiedMenuTree, TrendingTag } from '@trendency/kesma-ui';
import { forkJoin, Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ApiService } from '../services';

@Injectable()
export class InitResolver {
  constructor(private readonly apiService: ApiService) {}

  resolve(): Observable<InitResolverData> {
    return forkJoin([
      this.apiService.init().pipe(
        map(({ data }) => data),
        catchError(() => {
          return of({} as InitResponse);
        })
      ) as Observable<InitResponse>,
      this.apiService
        .getMenu({
          [RelatedType.VIDEO_COLLECTION]: 'videok',
          [RelatedType.PODCAST_COLLECTION]: 'podcast',
        })
        .pipe(
          catchError(() => {
            return of({} as SimplifiedMenuTree);
          })
        ) as Observable<SimplifiedMenuTree>,
      this.apiService.getTagsOnHeaderBar().pipe(
        map((res) => {
          return res.data.map((data: TrendingTag) => {
            return {
              ...data,
              thumbnail: data.thumbnailUrl,
            };
          });
        }),
        catchError(() => {
          return of([] as TrendingTag[]);
        })
      ) as Observable<TrendingTag[]>,
    ]).pipe(map<[InitResponse, SimplifiedMenuTree, TrendingTag[]], InitResolverData>(([init, menu, tags]) => ({ init, menu, tags })));
  }
}
