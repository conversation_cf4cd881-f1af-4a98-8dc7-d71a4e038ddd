import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ArticleCard, BackendArticleSearchResult, mapBackendArticleDataToArticleCard } from '@trendency/kesma-ui';
import { catchError, map } from 'rxjs/operators';
import { ApiService } from '../services';

@Injectable()
export class RecommendationsResolver {
  constructor(private readonly apiService: ApiService) {}

  resolve(): Observable<ArticleCard[]> {
    return this.apiService.getArticles().pipe(
      map(({ data }) => data.map((article: BackendArticleSearchResult) => mapBackendArticleDataToArticleCard(article))),
      catchError(() => of([]))
    ) as Observable<ArticleCard[]>;
  }
}
