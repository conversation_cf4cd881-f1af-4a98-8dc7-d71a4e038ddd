@use 'shared' as *;

:host {
  display: block;

  .pager {
    position: relative;
    gap: 8px;
    flex-wrap: wrap;

    @include media-breakpoint-down(md) {
      gap: 4px;
    }

    .buttons {
      display: flex;
    }

    .list-pager {
      gap: 8px;

      @include media-breakpoint-down(md) {
        gap: 4px;
      }
    }

    .button-num {
      @extend %flex-container;
      color: var(--color, var(--kui-deep-teal-400)) !important;
      border: 1px solid var(--border, var(--kui-deep-teal-100));
      font-size: 18px;
      font-weight: 700;
      line-height: normal;
      border-radius: 2px;
      padding: 0 6px;

      &.active {
        background: var(--background, var(--kui-deep-teal-400));
        color: var(--kui-white) !important;
      }

      &:hover {
        background-color: var(--kui-deep-teal-400);
        color: var(--kui-white) !important;
      }
    }

    .button-num,
    .button-arrow {
      transition: all 0.3s;
    }

    .button-arrow {
      @extend %flex-container;
      border-radius: 5px;

      .icon-prev {
        transform: rotate(180deg);
      }

      &.next,
      &.prev {
        background-color: transparent;
      }
      &:hover {
        background-color: var(--kui-deep-teal-400);
        .icon {
          filter: invert(100%) sepia(0%) saturate(0%) hue-rotate(93deg) brightness(103%) contrast(103%);
        }
      }
      border: 1px solid var(--border, var(--kui-deep-teal-400));
      border-radius: 2px;
      border-color: var(--border, var(--kui-deep-teal-400));
      width: 32px;
      height: 32px;

      .icon {
        background-image: var(--chevron-icon, url('/assets/images/icons/vg-chevron-right.svg'));
        filter: invert(75%) sepia(60%) saturate(528%) hue-rotate(110deg) brightness(87%) contrast(89%);
        width: 24px;
        height: 24px;
      }

      &.disabled {
        display: none;
      }
    }
  }
}

%flex-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 32px;
  height: 32px;
}
