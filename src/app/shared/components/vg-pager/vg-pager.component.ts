import { ChangeDetectionStrategy, Component } from '@angular/core';
import { PagerComponent } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { NgClass, NgIf } from '@angular/common';

@Component({
  selector: 'vg-pager',
  templateUrl: './../../../../../node_modules/@trendency/kesma-ui/src/lib/components/pager/pager.component.html',
  styleUrls: ['./../../../../../node_modules/@trendency/kesma-ui/src/lib/components/pager/pager.component.scss', './vg-pager.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgClass, RouterLink],
})
export class VgPagerComponent extends PagerComponent {
  override removeDisabledElement: boolean = true;
}
