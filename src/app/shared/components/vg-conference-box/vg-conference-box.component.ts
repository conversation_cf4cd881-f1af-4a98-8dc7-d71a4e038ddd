import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { BaseComponent, Conference, IconComponent } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'vg-conference-box',
  templateUrl: './vg-conference-box.component.html',
  styleUrls: ['./vg-conference-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, IconComponent],
})
export class VgConferenceBoxComponent extends BaseComponent<Conference> {
  @Input() @HostBinding('class.is-sidebar') isSidebar = false;
  @Input() desktopWidth = 12;

  get mobileView(): boolean {
    return !!(this.isSidebar || this.desktopWidth < 5);
  }
}
