@use 'shared' as *;

:host {
  display: flex;
  width: 100%;

  kesma-icon {
    flex: 0 0 auto;
  }

  .conference-box-container {
    position: relative;
    width: 100%;
  }

  .background-container {
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;

    @include media-breakpoint-down(md) {
      display: none;
    }
  }

  section {
    position: relative;
    display: flex;

    @include media-breakpoint-down(md) {
      flex-direction: column;
      width: calc(100% + 48px);
      margin-inline: -24px;
    }
  }

  .background-img {
    object-fit: cover;
    width: 50%;
    max-height: 100%;
    opacity: 90%;

    @include media-breakpoint-down(md) {
      width: 100%;
    }

    &.on-mobile {
      position: absolute;
      left: 0;
      bottom: 0;
      height: 100%;

      @include media-breakpoint-up(md) {
        display: none;
      }
    }
  }

  .conference {
    &-left-container {
      flex: 1;
      padding: 50px 0px;

      @include media-breakpoint-down(md) {
        width: 100%;
        position: relative;
        padding: 24px;
      }
    }

    &-logo {
      background-image: url('/assets/images/vg-konferenciaink.svg');
      width: 300px;
      height: 40px;
    }

    &-info {
      display: flex;
      flex-direction: column;
      gap: 8px;
      position: relative;

      &-title-lead {
        display: flex;
        flex-direction: column;
        gap: 16px;
        padding: 0 16px;

        @include media-breakpoint-down(md) {
          padding: 0;
          padding-left: 41px;
        }
      }

      &-title {
        color: var(--kui-white);
        text-transform: uppercase;

        @include media-breakpoint-down(md) {
          font-size: 28px;
          line-height: normal;
          letter-spacing: 0.56px;
        }
      }

      &-lead {
        color: var(--kui-deep-teal-50);
        font-size: 20px;
        font-weight: 700;
        line-height: 26px;
        letter-spacing: 0.4px;

        @include media-breakpoint-down(md) {
          font-size: 16px;
          line-height: 22px;
          letter-spacing: normal;
        }
      }
    }

    &-right-container {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      @include media-breakpoint-down(md) {
        padding: 24px;
        position: relative;
      }
    }

    &-info-block {
      display: flex;
      flex-direction: column;
      gap: 22px;

      @include media-breakpoint-down(md) {
        position: relative;
        width: 100%;
      }
    }

    &-date-venue {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: center;
      gap: 8px;
      color: var(--kui-white);

      &-data {
        display: flex;
        gap: 16px;
        width: 100%;
      }
    }

    &-btn-registration {
      border: 1px solid var(--kui-deep-teal-400);
      border-radius: 2px;
      color: var(--kui-deep-teal-400);
      padding: 8px;

      @include media-breakpoint-down(md) {
        width: 100%;
      }

      &:hover {
        background: var(--kui-deep-teal-400);
        color: var(--kui-gray-900);
      }

      &-text {
        padding: 8px;

        @include media-breakpoint-down(md) {
          font-size: 20px;
          line-height: 26px;
        }
      }
    }
  }

  &.is-sidebar {
    .background-container {
      display: none;
    }

    section {
      flex-direction: column;
    }

    .background-img {
      width: 100%;

      @include media-breakpoint-up(md) {
        display: block;
      }
    }

    .conference {
      &-left-container {
        width: 100%;
        position: relative;
        padding: 24px;
      }

      &-logo {
        width: 100%;
        background-size: contain;
        background-repeat: no-repeat;
      }

      &-info {
        display: flex;
        flex-direction: column;
        gap: 8px;
        position: relative;

        &-title-lead {
          padding: 0;
        }

        &-title {
          font-size: 28px;
          line-height: normal;
          letter-spacing: 0.56px;
        }

        &-lead {
          color: var(--kui-deep-teal-50);
          font-size: 16px;
          font-weight: 700;
          line-height: 22px;
          letter-spacing: normal;
        }
      }

      &-right-container {
        padding: 24px;
        position: relative;
      }

      &-info-block {
        position: relative;
        width: 100%;
      }

      &-btn-registration {
        width: 100%;

        &:hover {
          background: var(--kui-deep-teal-400);
          color: var(--kui-gray-900);
        }

        &-text {
          font-size: 20px;
          line-height: 26px;
        }
      }
    }

    .font-bold-20 {
      font-size: 16px;
      line-height: 22px;
    }
  }

  .icon {
    width: 24px;
    height: 24px;
  }
}
