@use 'shared' as *;

.stock-exchange-stripe {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 60px;
  gap: 20px;
  background: var(--kui-gray-950);

  @include media-breakpoint-down(md) {
    padding: 8px 24px;
    gap: 16px;
  }

  .desktop {
    @include media-breakpoint-down(md) {
      display: none;
    }
  }

  .mobile {
    display: none;

    @include media-breakpoint-down(md) {
      display: block;
    }
  }

  .label {
    padding: 3px 12px;
    transition: 0.3s;
    border: 1px solid var(--kui-deep-teal-400);
    border-radius: 2px;
    color: var(--kui-white);
    display: flex;
    gap: 8px;
    align-items: center;
    font-size: 8px;
    font-weight: 400;
    line-height: 10px;
    flex: 0 0 auto;

    img {
      width: 30px;
      height: 24px;

      @include media-breakpoint-down(md) {
        width: 24px;
      }
    }

    @media (hover: hover) {
      &:hover {
        background: var(--kui-deep-teal-400);
      }
    }

    @include media-breakpoint-down(md) {
      padding: 3px;
    }
  }

  // iOS css animation flickering fix
  * {
    backface-visibility: hidden;
    transform-style: preserve-3d;
  }

  .exchange-stripe {
    display: inline-flex;
    align-items: center;
    position: relative;
    transition: 0.3s linear opacity;

    @keyframes slideshow {
      to {
        transform: translateX(-50%);
      }
    }

    &.animate {
      animation: slideshow 50s linear infinite;
    }

    @media (hover: hover) {
      &:hover {
        animation-play-state: paused;
      }
    }

    vg-exchange-item {
      margin-right: 20px;
    }

    &.paused {
      animation-play-state: paused;
    }

    &-wrapper {
      overflow: hidden;
      position: relative;

      &:before {
        content: 'Frissítés...';
        display: flex;
        position: absolute;
        width: 100%;
        height: 100%;
        text-align: center;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: 0.3s linear opacity;
        color: var(--kui-white);
      }

      &.update {
        &:before {
          opacity: 1;
        }

        > div {
          animation-play-state: paused;
          opacity: 0;
        }
      }
    }
  }

  &-mobile-extra {
    display: none;
    border-bottom: 1px solid var(--kui-gray-950);
    padding: 4px 0;
    font-size: 10px;
    line-height: 12px;
    justify-content: center;
    align-items: center;
    gap: 8px;

    img {
      width: 30px;
      height: 24px;
    }

    @include media-breakpoint-down(md) {
      display: flex;
    }
  }
}
