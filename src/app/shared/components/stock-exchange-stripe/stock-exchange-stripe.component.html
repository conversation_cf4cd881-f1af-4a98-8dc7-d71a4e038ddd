<ng-container *ngIf="latestData$ | async as latestData">
  <div class="stock-exchange-stripe">
    <a class="label" routerLink="/reszvenyek">
      <img alt="BÉT logó" class="desktop" src="assets/images/icons/bet.svg" />
      <kesma-icon class="mobile" name="vg-trending-lines"></kesma-icon>

      <span class="desktop">
        Árfolyamok: 15 perccel<br />
        késleltetett adatok
      </span>
    </a>

    <div #stripeWrapper class="exchange-stripe-wrapper">
      <div #stripe (touchend)="removeMobilePausedStateWithDelay()" (touchstart)="stripe.classList.add('paused')" class="exchange-stripe">
        <!-- Stripe elements (display twice to create a nice loop with css animation) -->
        <ng-container *ngFor="let item of [].constructor(2)">
          <vg-exchange-item *ngFor="let data of latestData" [data]="data"></vg-exchange-item>
        </ng-container>
      </div>
    </div>
  </div>
  <div class="stock-exchange-stripe-mobile-extra">
    <img alt="BÉT logó" src="assets/images/icons/bet-color.svg" />
    <span>Árfolyamok: 15 perccel késleltetett adatok</span>
  </div>
</ng-container>
