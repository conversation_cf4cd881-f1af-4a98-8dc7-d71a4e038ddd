@use 'shared' as *;

@mixin highlight-style($type: highlight) {
  padding: 4px 24px;
  color: var(--kui-deep-teal-900);
  background: var(--kui-white);
  border-left: 8px solid var(--kui-deep-teal-400);
  margin: 24px 0;

  p {
    font-size: 20px;
    font-weight: 700;
    line-height: 26px;
    letter-spacing: 0.4px;
    margin: 0;

    @include media-breakpoint-down(sm) {
      font-size: 16px;
      line-height: 22px;
    }
  }

  @if $type == highlight2 {
    & {
      padding: 16px 24px;
      color: var(--kui-white);
      background: var(--Deep-Teal-950, #042f2c);
      border-radius: 2px;
      border: 1px solid var(--kui-deep-teal-400);
      border-left-width: 8px;

      p {
        font-size: 18px;
        font-weight: 500;
        line-height: 24px;

        @include media-breakpoint-down(sm) {
          font-size: 16px;
        }
      }
    }
  }
}

.block-content::ng-deep {
  a {
    color: var(--kui-deep-teal-600);
    text-decoration: none;
    line-height: normal;
    transition: all 0.3s;
    text-decoration: underline solid 1px transparent;
    text-underline-offset: 3px;

    &:hover {
      color: var(--kui-amethyst-500);
      text-decoration-color: var(--kui-amethyst-500);
    }
  }

  figure.image {
    margin: 24px auto;
    width: 100%;

    img {
      border-radius: 2px;
      cursor: pointer;
      width: 100%;
    }

    figcaption {
      color: var(--kui-gray-950);
      font-size: 12px;
      font-weight: 400;
      line-height: 14px;
      padding: 8px 0 0;

      &:before {
        content: none;
      }
    }
  }

  .custom-text-style {
    margin: 24px 0;

    &.underlined-text {
      text-decoration: underline;
    }

    &.highlight {
      @include highlight-style();
    }

    &.highlight-style2 {
      @include highlight-style(highlight2);
    }

    &.highlight-style3 {
      padding: 16px 24px;
      border: 1px solid var(--kui-deep-teal-400);
      border-top-width: 8px;
      border-radius: 2px;

      .highlightBlock-style3-content {
        margin-bottom: 0;

        & > * {
          margin-bottom: 24px;
          margin-top: 0;
        }

        p {
          font-size: 18px;
          line-height: 24px;
          font-weight: 500;
          color: var(--kui-gray-950);
          text-align: center;

          @include media-breakpoint-down(md) {
            font-size: 16px;
          }
        }

        & > h2:first-child,
        & > h3:first-child,
        & > h4:first-child {
          margin-bottom: 8px;
          letter-spacing: 0.4px;
          text-align: center;
        }

        & > *:last-child {
          margin-bottom: 0;
        }
      }
    }

    &.highlight-comment {
      padding: 24px;
      background-color: var(--kui-deep-teal-50);
      position: relative;
      font-style: italic;
      min-height: 96px;

      &::before {
        content: '';
        position: absolute;
        left: 24px;
        top: 24px;
        width: 48px;
        height: 48px;
        background: url('/assets/images/icons/info.svg') no-repeat;
        background-size: contain;
      }

      @include media-breakpoint-down(sm) {
        padding: 16px;
        min-height: 30px;
        &::before {
          left: 16px;
          top: 16px;
          width: 24px;
          height: 24px;
          background-size: 24px;
        }
      }

      p:last-of-type {
        margin-bottom: 0;
      }

      .highlightBlock-comment-content {
        margin-left: 0;
        margin-top: 28px;

        p {
          font-size: 18px;
          font-weight: 500;
          line-height: 24px;
          color: var(--kui-deep-teal-800);
          font-style: normal;

          @include media-breakpoint-down(sm) {
            font-size: 16px;
          }
        }

        @include media-breakpoint-up(sm) {
          margin-left: 72px;
          margin-top: 0;
        }
      }
    }

    &.quote {
      color: var(--kui-white);
      background: var(--kui-deep-teal-900);
      padding: 64px 16px 16px;
      position: relative;
      border: none;
      margin: 24px 0;
      line-height: 40px;

      &::before {
        @extend %quote-icon;
        background-image: url('/assets/images/icons/vg-quote.svg');
        // It is needed because in svg files the fill=currentColor
        filter: invert(71%) sepia(17%) saturate(2431%) hue-rotate(116deg) brightness(109%) contrast(66%);
        top: 16px;
        left: 16px;
      }

      .quoteBlock-content {
        margin-top: 4px;

        p {
          font-size: 32px;
          font-weight: 500;
          line-height: 40px;
          font-style: normal;

          @include media-breakpoint-down(sm) {
            font-size: 24px;
            line-height: normal;
          }
        }
      }

      .quoteBlock-content p:before,
      .quoteBlock-content p:after {
        content: none;
      }
    }

    &.border-text {
      padding: 16px 24px;
      color: var(--kui-white);
      background: var(--Deep-Teal-950, #042f2c);
      border-radius: 2px;
      border: 1px solid var(--kui-deep-teal-400);
      border-left-width: 8px;

      p {
        font-weight: 500;
        line-height: 24px;
      }
    }
  }

  h2,
  h3,
  h4 {
    margin-bottom: 16px;
    color: var(--kui-gray-950);
    font-family: var(--kui-font-primary);
    font-style: normal;
    font-weight: 700;
    line-height: 26px;
    letter-spacing: 0.4px;

    @include media-breakpoint-down(sm) {
      line-height: normal;
      letter-spacing: 0.36px;
    }
  }

  h2 {
    font-size: 20px;
    line-height: 26px;

    @include media-breakpoint-down(sm) {
      font-size: 18px;
    }
  }

  h3 {
    font-size: 18px;
    line-height: 26px;
  }

  h4 {
    font-size: 16px;
    text-decoration: none;
    line-height: 22px;
    color: var(--kui-deep-teal-900);
  }

  p {
    font-size: 18px;
    font-weight: 400;
    line-height: 24px;
    letter-spacing: 0.36px;

    @include media-breakpoint-down(sm) {
      font-size: 16px;
    }
  }

  ol,
  ul {
    li {
      color: var(--kui-gray-950);
      font-size: 18px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      letter-spacing: 0.36px;
      margin: 16px 0;
      padding-left: 40px;
      display: flow-root;

      &::before {
        position: absolute;
        left: 0;
        width: 24px;
        height: 24px;
      }

      @include media-breakpoint-down(sm) {
        font-size: 16px;
      }
    }
  }

  & > ol {
    & > li {
      &:before {
        display: inline-block !important;
      }
    }
  }

  ul {
    li {
      list-style-type: none;

      &::before {
        content: '';
        background: url(/assets/images/icons/vg-bullet.svg);
        // It is needed because in svg files the fill=currentColor
        filter: invert(65%) sepia(85%) saturate(390%) hue-rotate(117deg) brightness(96%) contrast(84%);
      }
    }
  }

  ol {
    counter-reset: index;
    padding-left: 0;
    margin: 0;

    li {
      display: flow-root;
      position: relative;

      &:before {
        counter-increment: index;
        content: counters(index, '.');
        display: inline-flex !important;
        align-items: center;
        justify-content: center;
        color: var(--kui-deep-teal-400);
        font-size: 18px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        letter-spacing: 0.36px;
        border-radius: 2px;
        border: 0.5px solid var(--kui-deep-teal-400);
      }
    }
  }
}

%quote-icon {
  content: '';
  position: absolute;
  background-size: contain;
  background-repeat: no-repeat;
  width: 48px;
  height: 48px;
}
