import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ArticleFileLinkDirective, BypassPipe, ImageLightboxComponent, WysiwygBoxComponent } from '@trendency/kesma-ui';
import { Ng<PERSON><PERSON>, NgIf } from '@angular/common';
import { RunScriptsDirective } from '@trendency/kesma-core';

@Component({
  selector: 'vg-wysiwyg-box',
  templateUrl: '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/wysiwyg-box/wysiwyg-box.component.html',
  styleUrls: ['../../../../../node_modules/@trendency/kesma-ui/src/lib/components/wysiwyg-box/wysiwyg-box.component.scss', './vg-wysiwyg-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, ArticleFileLinkDirective, RunScriptsDirective, ImageLightboxComponent, BypassPipe],
})
export class VgWysiwygBoxComponent extends WysiwygBoxComponent {}
