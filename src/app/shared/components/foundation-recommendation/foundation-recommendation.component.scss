@use 'shared' as *;

.foundation-recommendation {
  margin-top: 24px;
  margin-bottom: 24px;

  h4 {
    margin-bottom: 24px;

    &.side {
      @include media-breakpoint-down(md) {
        margin-top: 24px;
      }
    }
  }

  &-sub {
    padding-top: 16px;
  }

  &-side {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
}

.more-recommendation-link {
  padding: 24px 0 0;
  text-align: center;

  a {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    border-bottom: 1px solid var(--kui-gray-950);
    padding: 0 0 8px;
    font-size: 16px;
    font-weight: 700;
    line-height: 22px;
    transition: 0.3s;

    @media (hover: hover) {
      &:hover {
        color: var(--kui-amethyst-500);
        border-bottom: 1px solid var(--kui-amethyst-500);
      }
    }
  }
}
