<ng-container *ngIf="recommendations && recommendations.length > 0">
  <div class="row foundation-recommendation">
    <div class="col-12 {{ sideRecommendations.length > 0 ? 'col-md-8' : 'col-md-12' }}">
      <h4>To<PERSON><PERSON><PERSON><PERSON> cik<PERSON>k a témában</h4>
      <div class="row">
        <div class="col-12 foundation-recommendation-main">
          <vg-article-card [data]="mainRecommendation" [styleID]="ArticleCardType.FeaturedImgAbsoluteTagTitle"></vg-article-card>
        </div>
        <div *ngFor="let recommendation of subRecommendations" class="col-12 col-sm-6 col-md-12 col-lg-6 foundation-recommendation-sub">
          <vg-article-card [data]="recommendation" [hideLead]="true" [styleID]="ArticleCardType.FeaturedRightImgTagTitleLead"></vg-article-card>
        </div>
      </div>
    </div>
    <ng-container *ngIf="sideRecommendations.length > 0">
      <div class="col-12 col-md-4">
        <h4 class="side">To<PERSON><PERSON><PERSON><PERSON> hírek a témában</h4>
        <div class="foundation-recommendation-side">
          <vg-article-card
            *ngFor="let recommendation of sideRecommendations"
            [data]="recommendation"
            [styleID]="ArticleCardType.FeaturedTagTitle"
          ></vg-article-card>
        </div>
      </div>
    </ng-container>
  </div>

  <div class="more-recommendation-link">
    <a [routerLink]="['/', 'cimke', foundationTagSlug]">
      <span>Még több cikk</span>
      <kesma-icon [size]="24" name="vg-chevron-diagonal-right"></kesma-icon>
    </a>
  </div>
</ng-container>
