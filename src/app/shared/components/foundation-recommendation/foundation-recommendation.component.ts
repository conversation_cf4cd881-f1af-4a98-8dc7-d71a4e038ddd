import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { ApiService } from '../../services';
import uniqBy from 'lodash-es/uniqBy';
import { ArticleCard, IconComponent, searchResultToArticleCard, Tag } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { NgFor, NgIf } from '@angular/common';
import { ArticleCardType } from '../../definitions';
import { VgArticleCardComponent } from '../vg-article-card/vg-article-card.component';

@Component({
  selector: 'app-foundation-recommendation',
  templateUrl: './foundation-recommendation.component.html',
  styleUrls: ['./foundation-recommendation.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, RouterLink, VgArticleCardComponent, IconComponent],
})
export class FoundationRecommendationComponent implements OnInit {
  @Input() foundationTagSlug: string;
  @Input() tags: Tag[];
  @Input() articleSlug: string;

  recommendations: ArticleCard[] = [];
  ArticleCardType = ArticleCardType;

  constructor(
    private readonly apiService: ApiService,
    private readonly cdr: ChangeDetectorRef
  ) {}

  get mainRecommendation(): ArticleCard | undefined {
    return this.recommendations.length > 0 ? this.recommendations[0] : undefined;
  }

  get subRecommendations(): ArticleCard[] {
    return this.recommendations.length > 1 ? this.recommendations.slice(1, 5) : [];
  }

  get sideRecommendations(): ArticleCard[] {
    return this.recommendations.length > 5 ? this.recommendations.slice(5, 11) : [];
  }

  ngOnInit(): void {
    this.getArticlesByFoundationTagSlug();
  }

  getArticlesByFoundationTagSlug(): void {
    this.apiService.searchArticleByTags([this.foundationTagSlug], undefined, 0, 12).subscribe((res) => {
      this.recommendations = res.data.filter((a) => a.slug !== this.articleSlug).map((searchResult) => searchResultToArticleCard(searchResult));

      if (this.recommendations.length < 11) {
        this.getMoreArticlesByTags();
      } else {
        this.cdr.detectChanges();
      }
    });
  }

  getMoreArticlesByTags(): void {
    this.apiService
      .searchArticleByTags(
        this.tags.map((tag) => tag.slug),
        undefined,
        0,
        12
      )
      .subscribe((res2) => {
        this.recommendations = uniqBy(
          this.recommendations.concat(res2.data.filter((a) => a.slug !== this.articleSlug).map((searchResult) => searchResultToArticleCard(searchResult))),
          'id'
        );
        this.cdr.detectChanges();
      });
  }
}
