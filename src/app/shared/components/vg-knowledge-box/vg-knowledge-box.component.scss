@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  .wrapper {
    &:hover {
      .header {
        background: var(--kui-deep-teal-400);

        .knowledge-image {
          &-green {
            opacity: 0;
          }

          &-white {
            opacity: 1;
          }
        }
      }

      .body {
        &-description {
          text-decoration: underline solid var(--kui-deep-teal-400) 1px;
        }

        .vg-icon-chevron-diagonal-right {
          &-green {
            opacity: 0;
          }

          &-purple {
            opacity: 1;
          }
        }
      }
    }
  }

  .header {
    display: flex;
    padding: 8px;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 7px;
    border: 1px solid var(--kui-deep-teal-400);
    transition: 0.3s;

    .knowledge-image {
      &-green,
      &-white {
        opacity: 1;
        width: 218px;
        height: 27px;
      }

      &-white {
        opacity: 0;
        position: relative;
        top: -33px;
        margin-bottom: -33px;
      }
    }
  }

  .body {
    border: 1px solid var(--kui-brand-mnb);
    border-top-width: 0;
    padding: 8px;

    &-description {
      padding: 0 8px;
      color: var(--kui-gray-950);
      text-decoration-color: var(--kui-deep-teal-400);
      text-underline-offset: 4px;
      font-weight: 700;
      line-height: 22px;
      transition: 1s;
    }

    kesma-icon {
      justify-content: flex-end;
      color: var(--kui-deep-teal-400);
    }

    &:hover kesma-icon {
      color: var(--kui-amethyst-500);
    }
  }
}
