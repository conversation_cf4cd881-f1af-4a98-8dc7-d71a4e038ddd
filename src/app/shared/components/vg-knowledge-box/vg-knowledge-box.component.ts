import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { RouterLink } from '@angular/router';
import { IconComponent } from '@trendency/kesma-ui';

@Component({
  selector: 'vg-knowledge-box',
  templateUrl: './vg-knowledge-box.component.html',
  styleUrls: ['./vg-knowledge-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, IconComponent],
})
export class VgKnowledgeBoxComponent {
  @Input() text = '';
  @Input() routeUrl = ['/szoszedet'];
}
