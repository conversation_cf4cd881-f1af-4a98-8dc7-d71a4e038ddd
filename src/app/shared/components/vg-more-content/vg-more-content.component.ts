import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent, IconComponent } from '@trendency/kesma-ui';
import { VgMoreContentType } from '../../definitions';

@Component({
  selector: 'vg-more-content',
  templateUrl: './vg-more-content.component.html',
  styleUrls: ['./vg-more-content.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [IconComponent],
})
export class VgMoreContentComponent extends BaseComponent<VgMoreContentType> {}
