import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';

@Component({
  selector: 'app-social-popover',
  templateUrl: './social-popover.component.html',
  styleUrls: ['./social-popover.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SocialPopoverComponent {
  @HostBinding('attr.style') hostStyle = '--popover-bg: var(--kui-white)';

  @Input() set backgroundColor(color: string) {
    this.hostStyle = `--popover-bg: ${color}`;
  }
}
