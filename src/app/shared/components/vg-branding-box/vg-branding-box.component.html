<ng-container *ngIf="data?.length">
  <img [src]="logoUrl" class="logo" alt="" />

  <!-- FIGYELŐ -->
  <ng-container *ngIf="brand === 'figyelo'">
    <div class="flex-wrapper">
      <ng-container [ngTemplateOutlet]="articleInner" [ngTemplateOutletContext]="{ article: data?.[0], showImage: true, isFirst: true }"></ng-container>

      <div class="grid-wrapper">
        <ng-container
          [ngTemplateOutlet]="articleInner"
          [ngTemplateOutletContext]="{ article: article, showImage: i <= 1 }"
          *ngFor="let article of data?.slice(1, articleCount); index as i"
        ></ng-container>
      </div>
    </div>
  </ng-container>

  <!-- MAGYAR NEMZET-->
  <ng-container *ngIf="brand === 'magyarnemzet'">
    <div class="grid-wrapper">
      <ng-container
        [ngTemplateOutlet]="articleInner"
        [ngTemplateOutletContext]="{ article: article, showImage: true }"
        *ngFor="let article of data?.slice(0, articleCount); index as i"
      ></ng-container>
    </div>
  </ng-container>

  <vg-more-content [data]="linkContent"></vg-more-content>
</ng-container>

<ng-template #articleInner let-article="article" let-showImage="showImage" let-isFirst="isFirst">
  <a class="article" [class.first]="isFirst" [href]="article?.link" target="_blank">
    <div class="article-img-wrapper" *ngIf="showImage">
      <img [src]="article?.imageUrl" *ngIf="article?.imageUrl" class="article-img" alt="" />
      <kesma-icon name="vg-chevron-diagonal-right" *ngIf="!isFirst"></kesma-icon>
    </div>

    <div class="article-title" *ngIf="article?.title">{{ article?.title }}</div>
    <div class="article-lead" *ngIf="isFirst && article?.description">{{ article?.description }}</div>
  </a>
</ng-template>
