import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { ArticleCardType, VgBrandingBoxArticle, VgBrandingBoxBrand, VgMoreContentType } from '../../definitions';
import { Mutable } from '@trendency/kesma-core';
import { BaseComponent, IconComponent } from '@trendency/kesma-ui';
import { VgMoreContentComponent } from '../vg-more-content/vg-more-content.component';
import { NgFor, NgIf, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'vg-branding-box',
  templateUrl: 'vg-branding-box.component.html',
  styleUrls: ['vg-branding-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgTemplateOutlet, NgFor, VgMoreContentComponent, IconComponent],
})
export class VgBrandingBoxComponent extends BaseComponent<VgBrandingBoxArticle[]> {
  @Input() @HostBinding('class') brand: VgBrandingBoxBrand = 'figyelo';
  @Input() articleCount: number = 7;

  readonly ArticleCardType = ArticleCardType;

  get linkContent(): VgMoreContentType {
    const content = {} as Mutable<VgMoreContentType>;

    switch (this.brand) {
      case 'figyelo':
        content.url = 'https://figyelo.hu/';
        content.urlName = 'Tovább a Figyelo.hu-ra';
        break;
      case 'magyarnemzet':
        content.url = 'https://magyarnemzet.hu/';
        content.urlName = 'Tovább a Magyar Nemzet.hu-ra';
        break;
    }
    return content;
  }

  get logoUrl(): string {
    return `/assets/images/logo-${this.brand}.svg`;
  }
}
