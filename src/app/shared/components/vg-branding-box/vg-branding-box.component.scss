@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  .flex-wrapper {
    display: flex;
    gap: 24px;
  }

  .grid-wrapper {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;

    @include media-breakpoint-down(md) {
      grid-template-columns: 1fr;
    }
  }

  .article {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding-bottom: 8px;

    &-title,
    &-lead {
      transition: 0.3s;
      text-decoration: underline transparent solid 1px;
      text-underline-offset: 5px;
    }

    &-img {
      object-fit: cover;
      aspect-ratio: 16 / 9;
      transition: 0.3s;

      &-wrapper {
        overflow: hidden;
        flex: 0 0 auto;
        position: relative;

        kesma-icon {
          display: none;
          position: absolute;
          z-index: 1;
          bottom: 8px;
          right: 8px;
          color: var(--kui-deep-teal-400);

          @include media-breakpoint-down(md) {
            display: block;
          }
        }
      }
    }

    &:not(.first) {
      .article-img {
        @include media-breakpoint-down(md) {
          aspect-ratio: 1/1;
          object-fit: cover;
        }
      }
    }

    &:hover {
      .article-title,
      .article-lead {
        text-decoration-color: var(--kui-deep-teal-400);
      }

      .article-img {
        transform: scale(1.1);
      }
    }

    @include media-breakpoint-down(md) {
      padding-bottom: 0;
      flex-direction: row;
      justify-content: space-between;
      gap: 16px;

      &:not(.first) .article-img {
        width: 112px;

        &-wrapper {
          order: 1;
        }
      }
    }
  }

  .logo {
    @include media-breakpoint-down(md) {
      display: block;
      margin: 0 auto;
    }
  }

  &.figyelo {
    .logo {
      height: 40px;
      @extend %mobilLogoSize;
    }

    .flex-wrapper {
      margin: 16px 0;

      & > * {
        flex-basis: 50%;
      }

      @include media-breakpoint-down(md) {
        flex-direction: column;
      }

      @include media-breakpoint-down(md) {
        margin: 24px 0;
      }
    }

    .article {
      &-title {
        font-size: 18px;
        font-weight: 700;
        line-height: normal;
        letter-spacing: 0.36px;
        color: var(--kui-gray-950);

        @include media-breakpoint-down(md) {
          font-size: 16px;
          line-height: 22px;
        }
      }

      &.first {
        flex-direction: column;

        .article-title {
          font-size: 20px;
          line-height: 26px;
          letter-spacing: 0.4px;

          @include media-breakpoint-down(md) {
            font-size: 18px;
            line-height: normal;
            letter-spacing: 0.36px;
          }
        }

        .article-lead {
          font-size: 18px;
          font-weight: 400;
          line-height: 24px;
          letter-spacing: 0.36px;

          @include media-breakpoint-down(md) {
            display: none;
          }
        }
      }
    }
  }

  &.magyarnemzet {
    .logo {
      height: 44px;
      @extend %mobilLogoSize;
    }
  }

  &.magyarnemzet {
    .grid-wrapper {
      margin: 16px 0;
      grid-template-columns: repeat(4, 1fr);

      @include media-breakpoint-down(md) {
        grid-template-columns: repeat(2, 1fr);
      }

      @include media-breakpoint-down(sm) {
        grid-template-columns: 1fr;
      }
    }

    .article-title {
      font-size: 18px;
      font-weight: 700;
      line-height: normal;
      letter-spacing: 0.36px;

      @include media-breakpoint-down(md) {
        font-size: 16px;
        font-style: normal;
        line-height: 22px;
      }
    }
  }
}

%mobilLogoSize {
  @include media-breakpoint-down(md) {
    height: 32px;
  }
}
