import { AfterViewInit, ChangeDetectionStrategy, Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { DomSanitizer, SafeHtml } from '@angular/platform-browser';
import { Article, Tag } from '@trendency/kesma-ui';
import { SocialShareComponent } from '../social-share/social-share.component';
import { RouterLink } from '@angular/router';
import { DatePipe, NgFor, NgIf, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'app-article-header',
  templateUrl: './article-header.component.html',
  styleUrls: ['./article-header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgI<PERSON>, NgFor, RouterLink, NgTemplateOutlet, SocialShareComponent, DatePipe],
})
export class ArticleHeaderComponent implements OnChanges, AfterViewInit {
  @Input() data: Article;
  @Input() foundationTagSlug?: string;
  @Input() foundationTagTitle?: string;
  @Input() customShareText?: string;
  @Input() isMobileApp: boolean;

  tags: Tag[] = [];

  embedPrAdvert?: SafeHtml;

  constructor(private readonly sanitizer: DomSanitizer) {}

  ngAfterViewInit(): void {
    this.updateTags();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['data']) {
      this.updateTags();
      this.embedPrAdvert = this.sanitizer.bypassSecurityTrustHtml(this.data?.embedPrAdvert ?? '');
    }
  }

  private updateTags(): void {
    this.tags = this.foundationTagSlug
      ? [
          {
            title: this.foundationTagTitle,
            slug: this.foundationTagSlug,
          },
          ...(this.data?.tags ?? []),
        ]
      : (this.data?.tags.filter((t) => t.slug !== this.foundationTagSlug) ?? []);
  }
}
