@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;

  .article {
    &-tag {
      color: var(--kui-gray-500);
      font-weight: 400;
      font-size: 14px;
      line-height: 18px;
      transition: all 0.3s;

      @include media-breakpoint-down(md) {
        font-size: 12px;
        line-height: 14px;
      }

      &.bold {
        font-weight: 700;
        color: var(--kui-gray-950);
      }

      &:hover {
        color: var(--kui-deep-teal-400);
      }

      &-wrapper {
        display: flex;
        column-gap: 16px;
        flex-wrap: wrap;
        align-items: center;
        row-gap: 4px;

        @include media-breakpoint-down(md) {
          column-gap: 8px;
        }
      }

      &-item {
        display: flex;
        align-items: center;
      }
    }

    &-column {
      padding: 4px 8px;
      width: fit-content;
      border: 1px solid var(--kui-deep-teal-400);
      color: var(--kui-gray-950);
      font-weight: 700;
      font-size: 14px;
      line-height: 18px;
      cursor: pointer;

      @include media-breakpoint-down(md) {
        font-size: 12px;
        line-height: 14px;
      }

      &:hover {
        a {
          transition: all 0.3s;
          color: var(--kui-deep-teal-400);
        }
      }
    }

    &-title {
      color: var(--kui-gray-950);
      font-weight: 700;
      font-size: 36px;
      line-height: 40px;

      @include media-breakpoint-down(md) {
        font-size: 28px;
        line-height: 37px;
      }
    }

    &-lead {
      color: var(--kui-gray-950);
      font-weight: 500;
      font-size: 24px;
      line-height: 32px;

      @include media-breakpoint-down(md) {
        font-size: 18px;
        line-height: 24px;
      }
    }

    &-embed-pr-advert {
      width: 100%;
      display: flex;
      justify-content: center;
      margin: 10px auto;
    }

    &-bottom {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      row-gap: 16px;
      align-items: center;
    }

    &-meta {
      &-wrapper {
        display: flex;
        align-items: center;
        column-gap: 12px;
        row-gap: 4px;
        flex-wrap: wrap;

        @include media-breakpoint-down(md) {
          column-gap: 16px;
        }

        .separator {
          margin: 0;
          height: 12px;
        }
      }

      &-item {
        color: var(--kui-gray-950);
        font-weight: 400;
        font-size: 16px;
        line-height: 24px;

        @include media-breakpoint-down(md) {
          font-size: 14px;
          line-height: 18px;
        }

        &.bold {
          font-weight: 700;
          line-height: 22px;
        }
      }
    }
  }

  .separator {
    width: 1px;
    height: 10px;
    background-color: var(--kui-gray-100);
    margin-right: 8px;
  }

  app-social-share {
    @include media-breakpoint-down(md) {
      display: none;
    }
  }
}
