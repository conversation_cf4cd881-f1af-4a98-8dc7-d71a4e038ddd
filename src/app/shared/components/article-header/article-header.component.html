<div *ngIf="!isMobileApp" class="article-tag-wrapper" data-skip-on-mobile-app>
  <div *ngFor="let tag of tags; let first = first; let last = last" class="article-tag-item">
    <div *ngIf="!first" class="separator"></div>
    <a [class.bold]="first" [routerLink]="['/cimke', tag.slug]" class="article-tag">{{ tag.title }}</a>
  </div>
</div>
<div
  *ngIf="!isMobileApp && !foundationTagSlug && data.primaryColumn?.columnEmphasizeOnArticleCard && data?.columnSlug"
  class="article-column"
  data-skip-on-mobile-app
>
  <a [routerLink]="['/rovat', data.columnSlug]">{{ data.columnTitle }}</a>
</div>
<h1 class="article-title">{{ data.title }}</h1>
<div class="article-lead">{{ data.excerpt }}</div>
<div *ngIf="embedPrAdvert" [innerHTML]="embedPrAdvert" class="article-embed-pr-advert"></div>

<div class="article-bottom">
  <div class="article-meta-wrapper">
    <ng-container
      *ngIf="!isMobileApp && data.publicAuthor"
      [ngTemplateOutlet]="data.publicAuthorSlug && data.publicAuthorSlug.length > 0 ? publicAuthorWithLink : publicAuthorDetails"
    ></ng-container>
    <div *ngIf="!isMobileApp && data?.publicAuthor" class="separator"></div>
    <div *ngIf="data?.articleSource" class="article-meta-item">forrás: {{ data.articleSource }}</div>
    <div *ngIf="data?.articleSource" class="separator"></div>
    <div class="article-meta-item">{{ data.publishDate! | date: 'yyyy.MM.dd, HH:mm' }}</div>
    <div *ngIf="data?.lastUpdated" class="separator"></div>
    <div *ngIf="data?.lastUpdated" class="article-meta-item">Frissítve: {{ data.lastUpdated! | date: 'yyyy.MM.dd, HH:mm' }}</div>
  </div>
  <app-social-share [customShareText]="customShareText" [emailSubject]="data.title || ''"></app-social-share>
</div>

<ng-template #publicAuthorWithLink>
  <a [routerLink]="['/szerzo', data.publicAuthorSlug]" data-skip-on-mobile-app>
    <ng-container [ngTemplateOutlet]="publicAuthorDetails"></ng-container>
  </a>
</ng-template>

<ng-template #publicAuthorDetails>
  <div class="article-meta-item bold" data-skip-on-mobile-app>{{ data.publicAuthor }}</div>
</ng-template>
