@use 'shared' as *;

.exchange-box {
  &-row {
    background-color: var(--kui-gray-900);

    &.even {
      background-color: var(--kui-gray-950);
    }

    vg-exchange-item {
      width: 100%;

      ::ng-deep {
        .exchange-item {
          border-radius: initial;
          justify-content: flex-start;
          padding: 8px 16px;
          height: 44px;

          &-name {
            width: 40%;
          }

          &-value {
            width: 25%;
          }

          &-percentage {
            width: 20%;
          }

          kesma-icon {
            margin-left: auto;
          }
        }
      }
    }
  }
}
