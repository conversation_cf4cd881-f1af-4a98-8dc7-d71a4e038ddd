import { ChangeDetectionStrategy, Component, Input, OnChanges, SimpleChanges } from '@angular/core';
import { Observable } from 'rxjs';
import { ExchangeBoxDataType } from '@trendency/kesma-ui';
import { FinancialLatestDataService } from '../../services';
import { AsyncPip<PERSON>, Ng<PERSON><PERSON>, <PERSON><PERSON>or, NgIf, SlicePipe } from '@angular/common';
import { VgExchangeItemData } from '../../definitions';
import { VgSpinnerComponent } from '../vg-spinner/vg-spinner.component';
import { VgExchangeItemComponent } from '../vg-exchange-item/vg-exchange-item.component';

@Component({
  selector: 'app-exchange-table-box',
  templateUrl: './exchange-table-box.component.html',
  styleUrls: ['./exchange-table-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgI<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Async<PERSON>ip<PERSON>, <PERSON>licePipe, VgSpinner<PERSON>omponent, VgExchangeItemComponent],
})
export class ExchangeTableBoxComponent implements OnChanges {
  @Input() dataType: ExchangeBoxDataType = ExchangeBoxDataType.STOCK;

  tableStockData$: Observable<VgExchangeItemData[]> = this.financialLatestDataService.latestStockData$;

  constructor(private readonly financialLatestDataService: FinancialLatestDataService) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['dataType']) {
      this.tableStockData$ =
        this.dataType === ExchangeBoxDataType.STOCK ? this.financialLatestDataService.latestStockData$ : this.financialLatestDataService.latestCurrencyData$;
    }
  }
}
