@use 'shared' as *;

:host {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 0 0 16px 0;
  border-radius: 2px;
  border: 1px solid var(--kui-deep-teal-100);

  /*  @include media-breakpoint-down(lg) {
    flex-direction: row-reverse;
    gap: 12px;
    padding: 8px 0 8px 8px;
  }*/

  .video-card {
    &-thumbnail {
      border-radius: 2px;
      object-fit: cover;
      width: 100%;
      transition: transform 0.5s;

      &-box {
        width: calc(100% + 2px);
        margin-left: -1px;
        margin-top: -1px;
        position: relative;
        overflow: hidden;

        @include media-breakpoint-down(sm) {
          width: 100% !important;
          height: fit-content;
          min-width: 136px;
          margin: 0;
        }
      }

      &-badge {
        position: absolute;
        left: 0;
        bottom: 0;
        padding: 8px;
        display: flex;
        align-items: center;
        gap: 4px;
        border-radius: 2px;
        background-color: var(--kui-gray-950-85);
        color: var(--kui-white);
        transition: all 0.3s;

        kesma-icon {
          width: 20px;
          height: 20px;
        }
      }

      &-length {
        font-size: 12px;
        line-height: 14px;
        font-weight: 500;
      }
    }

    &-link {
      line-height: 22px;

      @include media-breakpoint-down(md) {
        font-size: 14px;
        line-height: 18px;
      }

      &-wrapper {
        padding: 0 8px;
        display: flex;
        flex-direction: column;
        gap: 8px;
        transition: all 0.3s;
        text-decoration: underline transparent solid 1px;
        text-underline-offset: 5px;

        @include media-breakpoint-down(md) {
          padding: 0;
        }
      }
    }

    &-title {
      font-size: 16px;
    }

    &-lead {
      display: none;
    }

    &-tag {
      line-height: 14px;
      font-size: 12px;
      font-weight: 700;
      transition: all 0.3s;

      &:hover {
        color: var(--kui-deep-teal-600);
      }
    }
  }

  &:hover {
    border: 1px solid var(--kui-deep-teal-400);

    .video-card {
      &-title {
        text-decoration: underline var(--kui-deep-teal-400) solid 1px;
      }

      &-lead {
        text-decoration: underline var(--kui-deep-teal-400) solid 1px;
      }

      &-thumbnail {
        transform: scale(1.1);

        &-badge {
          background-color: var(--kui-gray-950-65);
          color: var(--kui-deep-teal-400);

          .icon {
            filter: var(--kui-deep-teal-400);
          }
        }
      }
    }
  }

  &.is-sidebar {
    flex-direction: row-reverse;
    gap: 12px;
    padding: 8px 0 8px 8px;
    @include media-breakpoint-down(md) {
      flex-direction: column !important;
    }

    .video-card {
      &-thumbnail {
        &-box {
          width: 100%;
          height: fit-content;
          min-width: 136px;
          margin: 0;
        }
      }

      &-lead {
        display: none !important;
      }

      &-link {
        font-size: 14px;
        line-height: 18px;

        &-wrapper {
          padding: 0;
        }
      }
    }
  }

  &.is-light {
    border: 1px solid var(--kui-gray-700);
    gap: 8px;

    @include media-breakpoint-down(md) {
      flex-direction: column;
      padding: 0 0 16px 0;
    }

    .video-card {
      &-tag {
        line-height: 18px;
        font-size: 14px;
        color: var(--kui-white);

        &:hover {
          color: var(--kui-deep-teal-600);
        }
      }

      &-title {
        line-height: 26px;
        font-size: 20px;
        color: var(--kui-white);

        @include media-breakpoint-down(md) {
          line-height: 24px;
          font-size: 18px;
        }
      }

      &-lead {
        display: block;
        margin-top: 8px;
        color: var(--kui-white);
        line-height: 24px;
        font-size: 18px;
        font-weight: 400;
      }

      &-link {
        &-wrapper {
          padding: 0 8px;
        }
      }

      &-thumbnail {
        &-badge {
          padding: 4px 16px 4px 8px;
          gap: 10px;

          @include media-breakpoint-down(md) {
            padding: 8px;
            gap: 6px;
          }

          kesma-icon {
            width: 32px;
            height: 32px;

            @include media-breakpoint-down(md) {
              width: 20px;
              height: 20px;
            }

            ::ng-deep {
              svg {
                width: 32px !important;
                height: 32px !important;
              }
            }
          }

          &-length {
            font-size: 14px;
            line-height: 18px;
          }
        }
      }
    }
  }

  &.is-light.is-sidebar {
    gap: 16px;
    padding: 0;
    flex-direction: row-reverse;

    .video-card {
      &-link {
        &-wrapper {
          padding: 4px 0px 8px 8px;
        }
      }

      &-title {
        line-height: 24px;
        font-size: 18px;

        @include media-breakpoint-down(md) {
          line-height: 22px;
          font-size: 16px;
        }
      }

      &-thumbnail {
        height: 100%;

        &-box {
          height: auto;

          @include media-breakpoint-down(md) {
            padding-bottom: 32px;
            margin-bottom: 4px;
          }
        }

        &-badge {
          padding: 8px;
          gap: 4px;

          @include media-breakpoint-down(sm) {
            width: 100%;
            border-radius: 0;
            justify-content: center;
            background-color: var(--kui-gray-950);
          }

          kesma-icon {
            width: 20px;
            height: 20px;
          }

          &-length {
            font-size: 12px;
            line-height: 14px;
          }
        }
      }
    }
  }

  &.layout-element {
    .video-card {
      &-thumbnail-box,
      &-link-wrapper {
        flex: 1;
      }
    }

    &:not(.is-sidebar) {
      .video-card {
        &-thumbnail {
          &-box {
            width: auto;
            margin-left: 0;
            margin-top: 0;
            border-bottom: 1px solid var(--kui-gray-700);
            flex: 0 0 auto;
          }

          &-badge {
            gap: 8px;

            @include media-breakpoint-up(md) {
              left: 3.5px;
            }
          }

          &-length {
            @include media-breakpoint-up(md) {
              font-size: 14px;
            }
          }
        }

        &-lead {
          @include media-breakpoint-up(md) {
            font-weight: 500;
          }
        }
      }
    }

    &.is-sidebar {
      .video-card {
        &-thumbnail {
          &-box {
            @include media-breakpoint-down(sm) {
              flex: initial;
              min-width: auto;
              max-width: 100%;
            }
          }
        }
      }
    }
  }
}
