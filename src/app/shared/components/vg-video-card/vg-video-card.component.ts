import { ChangeDetectionStrategy, Component, HostBinding, Input, OnInit } from '@angular/core';
import { ArticleCard, BaseComponent, buildArticleUrl, buildTagUrl, buildVideoUrl, FocusPointDirective, IconComponent, VideoCard } from '@trendency/kesma-ui';
import { NgIf } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'vg-video-card',
  templateUrl: './vg-video-card.component.html',
  styleUrls: ['./vg-video-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, FocusPointDirective, NgIf, IconComponent],
})
export class VgVideoCardComponent extends BaseComponent<ArticleCard | VideoCard> implements OnInit {
  @Input() @HostBinding('class.is-sidebar') isSidebar = false;
  @Input() @HostBinding('class.is-light') isLight = false;
  @Input() @HostBinding('class.layout-element') isLayoutElement = false;
  @Input() isVideoType: boolean = false;

  tagLink: string[] = [];
  videoLink: string[] = [];

  override ngOnInit(): void {
    super.ngOnInit();
    this.videoLink = this.data ? (this.isVideoType ? buildVideoUrl(this.data as VideoCard) : buildArticleUrl(this.data as ArticleCard)) : [];
    this.tagLink = this.data ? buildTagUrl(this.data) : [];
  }

  get articleCard(): ArticleCard {
    return this.data as ArticleCard;
  }
}
