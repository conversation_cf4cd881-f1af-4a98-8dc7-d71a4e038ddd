<figure class="video-card-thumbnail-box">
  <a [routerLink]="videoLink" [attr.aria-label]="articleCard?.title">
    <img
      class="video-card-thumbnail"
      withFocusPoint
      [data]="articleCard?.thumbnailFocusedImages"
      [displayedUrl]="articleCard?.thumbnail?.url || './assets/images/vg-placeholder-16-9.svg'"
      [displayedAspectRatio]="{ desktop: '16:9' }"
      [alt]="articleCard?.thumbnail?.alt || ''"
      loading="lazy"
    />
  </a>

  <div class="video-card-thumbnail-badge">
    <kesma-icon name="vg-video"></kesma-icon>
    <span class="video-card-thumbnail-length" *ngIf="articleCard?.readingTime || articleCard?.length as length">{{ length }} perc</span>
  </div>
</figure>

<div class="video-card-link-wrapper">
  <a class="video-card-tag" [routerLink]="tagLink">
    {{ articleCard?.tags?.[0]?.title }}
  </a>

  <a class="video-card-link" [routerLink]="videoLink">
    <h2 class="video-card-title">{{ articleCard?.title }}</h2>
    <div class="video-card-lead">{{ articleCard?.lead }}</div>
  </a>
</div>
