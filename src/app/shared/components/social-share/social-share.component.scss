@use 'shared' as *;

:host {
  &.light-icon {
    kesma-icon {
      flex: 0 0 auto;
      cursor: pointer;

      &:not(.facebook) {
        color: var(--kui-white);

        &:hover {
          color: var(--kui-deep-teal-400);
        }
      }

      &.facebook {
        color: var(--kui-deep-teal-900);
      }
    }
  }

  .share {
    display: flex;
    gap: 8px;
    align-items: center;
    border: 1px solid var(--kui-gray-50);
    padding: 4px 8px;
    width: fit-content;
    cursor: pointer;
    color: var(--kui-gray-950);
    font-weight: 700;
    font-size: 12px;
    line-height: 14px;
    transition: all 0.3s;
    position: relative;

    & > kesma-icon {
      &.mobile {
        display: none;

        @include media-breakpoint-down(md) {
          display: block;
        }
      }

      &:not(.mobile) {
        @include media-breakpoint-down(md) {
          display: none;
        }
      }
    }

    &:hover {
      border: 1px solid var(--kui-deep-teal-400);
      background-color: var(--kui-deep-teal-400);
    }

    &-icon {
      &-wrapper {
        display: flex;
        align-items: center;
        color: var(--kui-gray-950);
      }

      &-item {
        &:hover {
          cursor: pointer;
          color: var(--kui-deep-teal-400);
        }
      }
    }
  }

  .popup {
    position: absolute;
    z-index: 10;
    top: -1px;
    left: -1px;
    width: 160px;
    background: var(--kui-white);
    border: 1px solid var(--kui-gray-50);
    cursor: default;

    &-body {
      display: flex;
      flex-direction: column;
      gap: 8px;

      &-share-item {
        display: flex;
        width: fit-content;
        align-items: center;
        padding: 2px 8px;
        gap: 8px;
        cursor: pointer;
        transition: all 0.3s;
        color: var(--kui-gray-950);

        &:hover {
          color: var(--kui-deep-teal-400);

          .popup-body-share-item-label {
            color: var(--kui-gray-950);
            text-decoration: underline var(--kui-deep-teal-400) solid 1px;
          }
        }

        &-label {
          font-size: 12px;
          line-height: 14px;
          font-weight: 500;
          text-decoration: underline transparent solid 1px;
          text-underline-offset: 3px;
        }
      }
    }
  }

  &.small-popup {
    .popup {
      left: -128px;
    }
  }

  &.light {
    .share {
      border: 1px solid var(--kui-deep-teal-700);
      color: var(--kui-white);
    }
  }

  .outline {
    outline: 1px solid var(--kui-gray-100);
    cursor: pointer;
    padding: 4px;

    &:hover {
      color: var(--kui-deep-teal-400);
    }
  }

  &.bordered {
    .share-icon-wrapper kesma-icon {
      outline: 1px solid var(--kui-gray-100);
      cursor: pointer;
      padding: 4px;

      &:hover {
        color: var(--kui-deep-teal-400);
      }
    }
  }

  &.news-feed {
    display: block;
    width: 100%;

    .share {
      width: 100%;
      border-color: var(--kui-deep-teal-400);
      border-radius: 2px;
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  &.interview-type {
    .share {
      border-color: var(--kui-gray-700);

      @include media-breakpoint-down(md) {
        border-color: var(--kui-deep-teal-400);
        border-radius: 2px;
        padding: 4px 4px 4px 8px;
      }
    }
  }
}
