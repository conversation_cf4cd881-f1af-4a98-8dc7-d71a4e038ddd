<ng-container *ngIf="smallPopup || onlyIcons; else shareButton">
  <div class="share-icon-wrapper" [style.gap.px]="customGap">
    <div class="share-icon-item" [class.outline]="isOutline" [copyToClipboard]="getShareUrl()" (click)="showCopyConfirmation()">
      <kesma-icon name="vg-link" [size]="24"></kesma-icon>
    </div>

    <a [href]="getShareUrl('email')" target="_blank" class="share-icon-item" [class.outline]="isOutline" aria-label="E-mail">
      <kesma-icon name="vg-email" [size]="24"></kesma-icon>
    </a>

    <a *ngIf="onlyIcons" [href]="getShareUrl('twitter')" target="_blank" class="share-icon-item" [class.outline]="isOutline" aria-label="X">
      <kesma-icon name="vg-twitter" [size]="24"></kesma-icon>
    </a>

    <a *ngIf="!isAnchorLink" [href]="getShareUrl('facebook')" target="_blank" class="share-icon-item" [class.outline]="isOutline" aria-label="Facebook">
      <kesma-icon *ngIf="!isLightIcon" name="vg-facebook-circle" class="facebook" [size]="20"></kesma-icon>
      <kesma-icon *ngIf="isLightIcon" name="vg-facebook-circle-2" [size]="24"></kesma-icon>
    </a>

    <div
      *ngIf="smallPopup"
      (click)="isPopupVisible = !isPopupVisible"
      (clickOutside)="isPopupVisible = false"
      [style.position]="'relative'"
      [class.outline]="isOutline"
    >
      <kesma-icon class="share-icon-item" name="vg-share-2" [size]="24"></kesma-icon>
      <ng-container [ngTemplateOutlet]="popupContent"></ng-container>
    </div>
  </div>
</ng-container>

<ng-template #shareButton>
  <div class="share" (click)="isPopupVisible = !isPopupVisible" (clickOutside)="isPopupVisible = false">
    <span>{{ customShareText ? customShareText : isNewsFeedType ? 'Megosztom' : 'Megosztom a cikket' }}</span>
    <kesma-icon [name]="'vg-share-2'" [size]="20"></kesma-icon>
    <kesma-icon [name]="customMobileShareIcon" [size]="24" class="mobile" *ngIf="customMobileShareIcon"></kesma-icon>
    <ng-container [ngTemplateOutlet]="popupContent"></ng-container>
  </div>
</ng-template>

<app-copy-box *ngIf="urlCopied$ | async as isSlided" [isSlided]="isSlided"></app-copy-box>

<ng-template #popupContent>
  <div class="popup" *ngIf="isPopupVisible" clickStopPropagation>
    <div class="popup-body">
      <a *ngIf="!isAnchorLink && !smallPopup" [href]="getShareUrl('facebook')" target="_blank" class="popup-body-share-item" aria-label="Facebook">
        <kesma-icon name="vg-facebook-circle" [size]="20"></kesma-icon>
        <span class="popup-body-share-item-label">Facebook</span>
      </a>

      <a *ngIf="!smallPopup" [href]="getShareUrl('email')" target="_blank" class="popup-body-share-item" aria-label="E-mail">
        <kesma-icon name="vg-mail" [size]="20"></kesma-icon>
        <span class="popup-body-share-item-label">Email</span>
      </a>

      <a [href]="getShareUrl('twitter')" target="_blank" class="popup-body-share-item" aria-label="X">
        <kesma-icon name="vg-twitter" [size]="20"></kesma-icon>
        <span class="popup-body-share-item-label">Twitter</span>
      </a>

      <a [href]="getShareUrl('linkedin')" target="_blank" class="popup-body-share-item" aria-label="LinkedIn">
        <kesma-icon name="vg-linkedin" [size]="20"></kesma-icon>
        <span class="popup-body-share-item-label">LinkedIn</span>
      </a>

      <div *ngIf="!smallPopup" class="popup-body-share-item" [copyToClipboard]="getShareUrl()" (click)="showCopyConfirmation()">
        <kesma-icon name="vg-link" [size]="20"></kesma-icon>
        <span class="popup-body-share-item-label">Link másolása</span>
      </div>
    </div>
  </div>
</ng-template>
