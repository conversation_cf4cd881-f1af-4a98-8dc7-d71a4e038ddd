import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { map, startWith, Subject, switchMap, timer } from 'rxjs';
import {
  ClickOutsideDirective,
  ClickStopPropagationDirective,
  CopyToClipboardDirective,
  getEmailShareUrl,
  getFacebookShareUrl,
  getLinkedinShareUrl,
  getTwitterShareUrl,
  IconComponent,
} from '@trendency/kesma-ui';
import { SeoService } from '@trendency/kesma-core';
import { CopyBoxComponent } from '../copy-box/copy-box.component';
import { AsyncPipe, NgIf, NgTemplateOutlet } from '@angular/common';

type SocialPlatform = 'facebook' | 'twitter' | 'email' | 'linkedin';

@Component({
  selector: 'app-social-share',
  templateUrl: './social-share.component.html',
  styleUrls: ['./social-share.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgTemplateOutlet, CopyBoxComponent, AsyncPipe, IconComponent, CopyToClipboardDirective, ClickOutsideDirective, ClickStopPropagationDirective],
})
export class SocialShareComponent {
  constructor(private readonly seoService: SeoService) {}

  @Input() @HostBinding('class.small-popup') smallPopup = false;
  @Input() @HostBinding('class.only-icons') onlyIcons = false;
  @Input() @HostBinding('class.light') isLight = false;
  @Input() @HostBinding('class.light-icon') isLightIcon = false;
  @Input() @HostBinding('class.bordered') isBordered = false;
  @Input() @HostBinding('class.news-feed') isNewsFeedType = false;
  @Input() @HostBinding('class.interview-type') isInterviewType = false;
  @Input() emailSubject: string;
  @Input() isAnchorLink = false;
  @Input() linkToCopy?: string;
  @Input() isOutline = false;
  @Input() customMobileShareIcon?: string;
  @Input() customGap = 24;
  @Input() customShareText?: string;

  private readonly urlCopiedSubject$: Subject<void> = new Subject<void>();
  urlCopied$ = this.urlCopiedSubject$.pipe(
    switchMap(() => {
      return timer(3000).pipe(
        map(() => false),
        startWith(true)
      );
    })
  );
  isPopupVisible = false;

  getShareUrl(platform?: SocialPlatform): string {
    const articleUrl = this.linkToCopy || this.seoService.currentUrl;

    if (platform === 'facebook') return getFacebookShareUrl(articleUrl);
    if (platform === 'email') return getEmailShareUrl(articleUrl, this.emailSubject);
    if (platform === 'twitter') return getTwitterShareUrl(articleUrl);
    if (platform === 'linkedin') return getLinkedinShareUrl(articleUrl);

    return articleUrl;
  }

  showCopyConfirmation(): void {
    this.urlCopiedSubject$.next();
  }
}
