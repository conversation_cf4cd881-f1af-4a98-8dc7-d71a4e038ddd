@use 'shared' as *;

.copy-container {
  position: fixed;
  top: 180px;
  left: -300px;
  width: 300px;
  height: auto;
  padding: 15px;
  background: var(--kui-deep-teal-400);
  border-radius: 5px;
  text-align: center;

  &-message {
    font-size: 14px;
  }

  &.slided {
    display: block;
    animation: slide 3s forwards;
    font-style: normal;
    font-weight: 700;
    line-height: 130%;
  }
}

@keyframes slide {
  25% {
    left: -5px;
  }
  50% {
    left: -5px;
  }
  75% {
    left: -5px;
  }
  100% {
    left: -300px;
  }
}
