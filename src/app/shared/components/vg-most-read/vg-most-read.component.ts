import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ArticleCard, BaseComponent } from '@trendency/kesma-ui';
import { ArticleCardType } from '../../definitions';
import { VgArticleCardComponent } from '../vg-article-card/vg-article-card.component';
import { NgFor } from '@angular/common';

@Component({
  selector: 'vg-most-read',
  templateUrl: './vg-most-read.component.html',
  styleUrls: ['./vg-most-read.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgFor, VgArticleCardComponent],
})
export class VgMostReadComponent extends BaseComponent<ArticleCard[]> {
  articleCardType = ArticleCardType;
}
