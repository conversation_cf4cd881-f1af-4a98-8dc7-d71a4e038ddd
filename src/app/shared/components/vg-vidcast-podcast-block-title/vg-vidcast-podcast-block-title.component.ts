import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { BaseComponent, BlockTitle, IconComponent } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';

export type VgVidcastPodcastBlockTitle = Pick<BlockTitle, 'url' | 'urlName' | 'isDark'>;

@Component({
  selector: 'vg-vidcast-podcast-block-title',
  templateUrl: './vg-vidcast-podcast-block-title.component.html',
  styleUrls: ['../vg-block-title/vg-block-title.component.scss', './vg-vidcast-podcast-block-title.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, IconComponent],
})
export class VgVidcastPodcastBlockTitleComponent extends BaseComponent<VgVidcastPodcastBlockTitle> {
  @Input() desktopWidth = 12;
  @Input() @HostBinding('class.is-sidebar') isSidebar = false;
  @HostBinding('class.is-dark') isDark = false;

  override ngOnInit(): void {
    this.isDark = this.data?.isDark || false;
  }
}
