@use 'shared' as *;
@mixin mobileOverrides {
  .block {
    align-items: flex-start;
    &-title {
      font-size: 18px;
      flex-direction: column;
      &-divider {
        display: none !important;
      }
    }
    &-url {
      span {
        font-size: 14px;
      }
    }
  }
}

:host {
  @include media-breakpoint-down(sm) {
    @include mobileOverrides;
  }
  &.is-sidebar {
    @include mobileOverrides;
  }
  .block-title {
    display: flex;
    align-items: center;
    gap: 16px !important;
    span {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    &-divider {
      &::before {
        display: inline-block;
        content: '\002F';
      }
    }
  }
}
