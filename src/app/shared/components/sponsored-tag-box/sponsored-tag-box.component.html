@if (articles()?.length) {
  <a class="slogan" [href]="urlHref()"
    ><h3 [style.color]="textColor() || 'var(--kui-black)'">{{ slogan() }}</h3></a
  >
  <div class="content">
    <div class="article-list">
      @for (article of articles() | slice: 0 : 4; track article.id) {
        <vg-article-card [data]="article" [styleID]="ArticleCardType.FeaturedTitle"></vg-article-card>
      }
    </div>
    @if (logoUrl(); as logo) {
      <div class="sponsor-container">
        <div>
          <a [href]="urlHref()" class="sponsor-logo">
            <img
              [src]="logo"
              [class.without-height-and-width]="!logoWidth() && !logoHeight()"
              [alt]="slogan()"
              [style.width]="logoWidth() || 'auto'"
              [style.height]="logoHeight() || 'auto'"
              loading="lazy"
            />
          </a>
          @if (articlesCount()) {
            <a [routerLink]="['/', 'cimke', tagSlug()]" class="article-count">{{ articlesCount() }} cikk</a>
          }
        </div>
      </div>
    }
  </div>
  <span class="ad-label"> Hirdetés </span>
}
