<a
  class="external-blog-header"
  [routerLink]="moreArticleLink"
  target="_blank"
  [class.isSmallView]="styleID === ExternalBlogType.CENTURY"
  [attr.aria-label]="styleID"
>
  <ng-container [ngSwitch]="styleID">
    <ng-container *ngSwitchCase="ExternalBlogType.MNB">
      <ng-container *ngTemplateOutlet="blogHeaderLogo; context: { src: './assets/images/external-blog-logo-1.svg', alt: 'MNB logo' }"></ng-container>
    </ng-container>
    <ng-container *ngSwitchCase="ExternalBlogType.CENTURY">
      <ng-container *ngTemplateOutlet="blogHeaderLogo; context: { src: './assets/images/external-blog-logo-2.svg', alt: 'Századvég logo' }"></ng-container>
    </ng-container>
    <ng-container *ngSwitchCase="ExternalBlogType.INDICATOR">
      <ng-container *ngTemplateOutlet="blogHeaderLogo; context: { src: './assets/images/indikator.svg', alt: 'Indikátor logo' }"></ng-container>
    </ng-container>
  </ng-container>
  <ng-container *ngIf="styleID !== ExternalBlogType.INDICATOR">
    @if (styleID !== ExternalBlogType.CENTURY) {
      <div class="external-blog-header-separator"></div>
    }
    <span class="font-bold-32 external-blog-header-title">Elemzői blog</span>
    <ng-template #analyst>
      <img src="./assets/images/external-blog-logo-1.svg" loading="lazy" alt="" />
    </ng-template>
  </ng-container>
</a>
<div class="external-blog-content" [ngClass]="{ 'cms-blog': isCMS }">
  <ng-container [ngSwitch]="styleID">
    <ng-container *ngSwitchCase="ExternalBlogType.INDICATOR">
      <ng-container *ngFor="let article of data; let last = last; trackBy: trackByFn">
        <div class="external-blog-article" [class.no-padding-bottom]="!last">
          <vg-article-card [styleID]="ArticleCardType.FeaturedTopImgTagTitle" [data]="article"></vg-article-card>
        </div>
      </ng-container>
    </ng-container>
    <ng-container *ngSwitchDefault>
      <ng-container *ngFor="let article of data; let last = last; let i = index; trackBy: trackByFn">
        <article>
          <div class="external-blog-article" [class.no-padding-bottom]="!last">
            @if (article?.title) {
              <a [routerLink]="article | articleLink" [attr.aria-label]="article?.title">
                <span class="font-bold-20 external-blog-article-title">
                  {{ article?.title }}
                </span>
              </a>
            }
          </div>
        </article>
      </ng-container>
    </ng-container>
  </ng-container>

  <div class="external-blog-more-content-container">
    <div class="external-blog-more-content">
      <a class="external-blog-more-content-link" [routerLink]="moreArticleLink" aria-label="Továbbiak"
        >Továbbiak
        <kesma-icon name="vg-chevron-diagonal-right"></kesma-icon>
      </a>
    </div>
  </div>
</div>

<ng-template #blogHeaderLogo let-src="src" let-alt="alt" let-column="column">
  <img class="external-blog-header-logo" [src]="src" [alt]="alt" />
</ng-template>
