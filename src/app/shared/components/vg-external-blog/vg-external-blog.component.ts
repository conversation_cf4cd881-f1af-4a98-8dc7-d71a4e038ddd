import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { ArticleCard, ArticleLinkPipe, BaseComponent, IconComponent } from '@trendency/kesma-ui';
import { ArticleCardType, ExternalBlogType } from '../../definitions';
import { VgArticleCardComponent } from '../vg-article-card/vg-article-card.component';
import { Ng<PERSON><PERSON>, <PERSON>F<PERSON>, NgIf, NgSwitch, NgSwitchCase, Ng<PERSON>witchDefault, NgTemplateOutlet } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'vg-external-blog',
  templateUrl: 'vg-external-blog.component.html',
  styleUrls: ['vg-external-blog.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    RouterLink,
    NgSwitch,
    NgSwitchCase,
    NgTemplateOutlet,
    NgIf,
    Ng<PERSON><PERSON>,
    NgF<PERSON>,
    VgArticleCardComponent,
    NgS<PERSON>Default,
    ArticleLinkPipe,
    IconComponent,
  ],
})
export class VgExternalBlogComponent extends BaseComponent<ArticleCard[]> {
  @HostBinding('class') hostClass = 'external-blog';

  @Input() set styleID(styleId: ExternalBlogType) {
    this.hostClass += ` ${styleId}`;
    this.#styleID = styleId;
  }

  @Input() isCMS?: boolean = false;

  ExternalBlogType = ExternalBlogType;
  ArticleCardType = ArticleCardType;

  get styleID(): ExternalBlogType {
    return this.#styleID as ExternalBlogType;
  }

  get moreArticleLink(): string[] {
    switch (this.styleID) {
      case ExternalBlogType.CENTURY:
        return ['/', 'rovat', 'szazadveg-blog'];
      case ExternalBlogType.INDICATOR:
        return ['/', 'rovat', 'indikator-blog'];
      case ExternalBlogType.MNB:
        return ['/', 'rovat', 'mnb-blog'];
      default:
        return ['/'];
    }
  }

  #styleID: ExternalBlogType = ExternalBlogType.MNB;
}
