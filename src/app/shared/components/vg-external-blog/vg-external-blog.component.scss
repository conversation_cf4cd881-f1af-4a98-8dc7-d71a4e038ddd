@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  container-type: inline-size;

  &.external-blog {
    &.cms-blog {
      pointer-events: none;
    }
    &:hover {
      .external-blog-content {
        border-left: 1px solid var(--kui-deep-teal-400);
        border-right: 1px solid var(--kui-deep-teal-400);
      }

      .external-blog-more-content-container {
        border-bottom: 1px solid var(--kui-deep-teal-400);
      }
    }
    .external-blog {
      &-header {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        gap: 16px;
        transition: 0.3s;

        &:hover {
          border: 2px solid var(--kui-deep-teal-400);
          .external-blog-header-title {
            color: var(--kui-deep-teal-400);
          }
        }

        @include media-breakpoint-up(sm) {
          min-height: 115px;
        }

        @include media-breakpoint-down(sm) {
          padding: 25px 19px;
        }

        &-separator {
          height: 24px;
          width: 1px;
        }

        &-title {
          line-height: normal;
          transition: color 0.3s ease-out;
          font-weight: 400 !important;

          @include media-breakpoint-down(sm) {
            font-size: 24px;
          }
        }
      }

      &-article {
        padding: 16px;
        display: flex;
        flex-direction: column;
        gap: 8px;

        &:hover {
          .external-blog-article-title {
            text-decoration-color: var(--kui-deep-teal-400);
          }
        }

        &.no-padding-bottom {
          padding-bottom: 0;
        }

        &-title {
          font-style: normal;
          transition: all 0.3s;
        }

        &-title {
          text-decoration: underline transparent;
          text-decoration-thickness: 1px;
          text-underline-offset: 5px;
        }

        &-title {
          @include media-breakpoint-down(sm) {
            font-size: 18px;
          }
        }
      }

      &-more-content-container {
        transition: border-bottom 0.3s;
        &:hover {
          .external-blog-more-content-link {
            color: var(--kui-amethyst-500);
          }

          .icon {
            filter: invert(37%) sepia(24%) saturate(6358%) hue-rotate(245deg) brightness(101%) contrast(97%);
          }
          border-bottom: 1px solid var(--kui-amethyst-500);
        }
      }

      &-more-content-container {
        &:not(:hover) {
          transition: border-bottom 0.3s;
        }
      }

      &-content {
        transition: border 0.3s;
      }

      &-more-content {
        display: flex;
        flex-direction: row;
        justify-content: flex-end;
        align-items: center;
        padding: 0px 16px 16px 16px;

        &-link {
          display: flex;
          align-items: center;
          gap: 16px;
          transition: color 0.3s;
        }
      }
    }
  }

  .isSmallView {
    @container (max-width: 313px) {
      flex-wrap: wrap;
    }
  }

  &.mnb {
    .external-blog {
      &-header {
        padding: 12px 18px;
        border: 2px solid var(--kui-brand-mnb);

        &-separator {
          background-color: var(--kui-brand-mnb);
        }
        &-title {
          color: var(--kui-brand-mnb);
        }

        &-logo {
          @include media-breakpoint-down(sm) {
            height: 64px;
            width: 64px;
          }
        }
      }

      &-more-content-container {
        border-bottom: 1px solid var(--kui-brand-mnb);
      }

      &-content {
        border-left: 1px solid var(--kui-brand-mnb);
        border-right: 1px solid var(--kui-brand-mnb);
        border-top: none;
      }
    }
  }

  &.century {
    .external-blog {
      &-header {
        padding: 12px 18px;
        border: 2px solid var(--kui-brand-szazadveg);

        &-separator {
          background-color: var(--kui-brand-szazadveg);
        }

        &-title {
          color: var(--kui-brand-szazadveg);
        }

        &-logo {
          @include media-breakpoint-down(sm) {
            height: auto;
            width: 110px;
          }
        }
      }

      &-more-content-container {
        border-bottom: 1px solid var(--kui-brand-szazadveg);
      }

      &-content {
        border-left: 1px solid var(--kui-brand-szazadveg);
        border-right: 1px solid var(--kui-brand-szazadveg);
        border-top: none;
      }
    }
  }

  &.indicator {
    .external-blog {
      &-header {
        border: 2px solid var(--kui-brand-indikator);
      }

      &-content {
        border-left: 1px solid var(--kui-black);
        border-right: 1px solid var(--kui-black);
        border-top: none;
      }

      &-more-content-container {
        border-bottom: 1px solid var(--kui-black);
      }
    }
  }

  .icon {
    height: 16px;
    width: 16px;
  }
}
