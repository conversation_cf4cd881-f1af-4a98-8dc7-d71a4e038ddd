import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { ArticleCard, BaseComponent, buildArticleUrl, buildTagUrl, FocusPointDirective, IconComponent, Sponsorship, Tag } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { NgIf } from '@angular/common';
import { SponsoredEnum } from '../../definitions';

@Component({
  selector: 'vg-sponsored-content',
  templateUrl: './vg-sponsored-content.component.html',
  styleUrls: ['./vg-sponsored-content.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, FocusPointDirective, IconComponent],
})
export class VgSponsoredContentComponent extends BaseComponent<ArticleCard> {
  @HostBinding('class') hostClass = 'sponsored';
  @Input() sponsorship?: Sponsorship;
  articleLink: string[] = [];
  displayedTagIndex: number = 0;
  displayedTag?: Tag;
  readonly buildTagUrl = buildTagUrl;
  #styleID: SponsoredEnum = SponsoredEnum.SponsoredArticleImageTop;

  get styleID(): SponsoredEnum {
    return this.#styleID as SponsoredEnum;
  }

  @Input() set styleID(styleId: SponsoredEnum) {
    this.hostClass += ` ${SponsoredEnum[styleId]}`;
    this.#styleID = styleId;
  }

  protected override setProperties(): void {
    if (this.data) {
      this.articleLink = buildArticleUrl(this.data);
      const tags = this.data?.tags;
      this.displayedTag = tags?.find(({ id }) => id === this.data?.firstTagId) || tags?.[this.displayedTagIndex];

      if (this.displayedTag) {
        this.displayedTagIndex = tags?.indexOf(this.displayedTag) ?? this.displayedTagIndex;
      }
    }
  }
}
