<ng-container *ngIf="data as article">
  <div class="vg-sponsor" [class.without-sponsor-header]="!sponsorship" [routerLink]="articleLink">
    <ng-container *ngIf="!sponsorship; else sponsoredHeader">
      <div class="vg-sponsor-header">
        <h3 class="font-medium-16 vg-sponsor-header-title">Szponzorált tartalom</h3>
      </div>
    </ng-container>
    <div class="vg-sponsor-container">
      <a class="vg-sponsor-thumbnail-box" [routerLink]="articleLink">
        <img
          class="vg-sponsor-article-thumbnail"
          withFocusPoint
          [data]="article?.thumbnailFocusedImages"
          [displayedAspectRatio]="{ desktop: '16:9' }"
          [displayedUrl]="article.thumbnail?.url || article.thumbnailUrl || './assets/images/vg-placeholder-16-9.svg'"
          [alt]="article.title || ''"
          loading="lazy"
          [class.placeholder]="!article.thumbnail?.url && !article.thumbnailUrl"
        />
        <kesma-icon class="vg-sponsor-article-thumbnail-icon" name="vg-chevron-diagonal-right" [size]="24"></kesma-icon>
      </a>

      <div class="vg-sponsor-article-items">
        <a class="font-medium-16 vg-sponsor-article-tag" *ngIf="displayedTag as firstTag" [routerLink]="buildTagUrl(article, displayedTagIndex)">
          {{ firstTag.title }}
        </a>

        <h2 class="font-medium-16 vg-sponsor-article-title">
          <a [routerLink]="articleLink">
            {{ article.recommendedTitle || article.title }}
          </a>
        </h2>
      </div>
    </div>
  </div>

  <ng-template #sponsoredHeader>
    <a class="vg-sponsor-header vg-sponsor" [style.background-color]="sponsorship?.highlightedColor" [href]="sponsorship?.url" target="_blank">
      <h3 class="font-medium-16 vg-sponsor-header-title" [style.color]="sponsorship?.fontColor">{{ sponsorship?.title }}</h3>
      <img class="vg-sponsor-header-thumbnail" loading="lazy" [src]="sponsorship?.thumbnailUrl || './assets/vg-placeholder-1-1.svg'" alt="" />
    </a>
  </ng-template>
</ng-container>
