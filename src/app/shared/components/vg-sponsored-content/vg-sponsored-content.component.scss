@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  .vg-sponsor {
    display: flex;
    flex-direction: column;
    gap: 8px;
    border: 1px solid var(--kui-gray-100);
    padding-bottom: 8px;
    transition: border 0.3s;

    &.without-sponsor-header {
      padding-top: 4px;
    }

    &:hover {
      border: 1px solid var(--kui-deep-teal-400);
      .vg-sponsor-article-title {
        text-decoration-color: var(--kui-deep-teal-400);
      }
    }

    &-header {
      padding: 8px;
      padding-top: 6px;

      &-title {
        color: var(--kui-deep-teal-400);
      }

      &.vg-sponsor {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
      }

      &-thumbnail {
        max-height: 30px;
        object-fit: cover;
      }
    }

    &-container {
      display: flex;
      gap: 8px;
      padding-inline: 8px;
    }

    &-thumbnail-box {
      position: relative;
      overflow: hidden;
      border-radius: 2px;
    }

    &-article {
      &-thumbnail {
        object-fit: cover;

        &.placeholder {
          object-fit: contain;
          background: var(--kui-gray-100);
        }
      }

      &-tag,
      &-title {
        font-weight: 700;
        line-height: 22px;
        transition: all 0.3s;
      }

      &-title {
        text-decoration: underline;
        text-decoration-color: transparent;
        text-decoration-thickness: 1px;
        text-underline-offset: 5px;
      }

      &-tag {
        display: inline-block;

        &:hover {
          color: var(--kui-deep-teal-400);
        }
      }
    }
  }

  &.SponsoredArticleImageTop {
    &:hover {
      .vg-sponsor-header-title {
        color: var(--kui-gray-950);
      }
    }
    .vg-sponsor {
      &-header {
        &-title {
          transition: color 0.3s;
        }
      }
      &-container {
        flex-direction: column;
        padding: 0;
      }

      &-article {
        &-tag {
          margin-bottom: 4px;
        }

        &-thumbnail {
          width: 100%;
          object-fit: cover;
        }

        &-thumbnail-icon {
          display: none;
        }

        &-items {
          padding: 0 8px;
        }
      }
    }
  }

  &.SponsoredArticleImageRight {
    &:hover {
      .vg-sponsor-article-thumbnail {
        transform: scale(1.2);
      }

      kesma-icon {
        color: var(--kui-amethyst-500);
      }
    }
    .vg-sponsor {
      &-container {
        flex-direction: row-reverse;
      }

      &-article {
        &-items {
          flex: 1;
        }

        &-thumbnail {
          aspect-ratio: 1 / 1;
          height: 112px;
          width: 112px;
          transition: all 0.3s ease-in-out;
          border-radius: 2px;
        }

        &-thumbnail-icon {
          position: absolute;
          bottom: 8px;
          right: 8px;
          color: var(--kui-deep-teal-400);
          transition: 0.3s;
        }

        &-tag {
          margin-bottom: 8px;
        }
      }
    }
  }
}
