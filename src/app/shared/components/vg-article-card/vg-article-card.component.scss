@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  // Global styles
  .article-card {
    &-thumbnail {
      width: 100%;
      object-fit: cover;
      transition: transform 0.5s;

      &-wrapper {
        flex: 0 0 auto;
      }

      &-card {
        position: relative;
        overflow: hidden;
      }

      &-meta {
        position: absolute;

        &.layer {
          bottom: 0;
          padding: 8px 24px;
          display: flex;
          align-items: center;
          background-color: var(--kui-gray-950-65);
          border-radius: 2px;
          color: var(--kui-white);
          line-height: 22px;
          font-weight: 700;
        }
      }
    }

    &-category {
      @extend %category;
      align-items: center;
      width: fit-content;
      font-weight: 700;
      border-radius: 2px;
      line-height: 22px;
      padding: 1px 8px;
    }

    &-tag-with-column {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      column-gap: 17px;
      row-gap: 5px;
    }

    &-separator {
      width: 1px;
      height: 12px;
      background-color: var(--kui-gray-100);
    }

    &-thumbnail,
    &-link {
      border-radius: 2px;
    }

    &-link {
      display: block;

      .arrow-icon {
        position: absolute;
        bottom: 0;
        right: 0;
        color: var(--kui-deep-teal-300);
      }

      &:hover {
        .arrow-icon {
          color: var(--kui-amethyst-500);
        }
      }
    }

    &-tag {
      line-height: 22px;
      font-weight: 700;
      display: flex;
      align-items: center;
      gap: 4px;

      kesma-icon {
        transition: transform 0.5s;
      }

      &:hover {
        color: var(--kui-deep-teal-400);

        kesma-icon {
          transform: rotate(-180deg);
          color: var(--kui-deep-teal-400);
        }
      }
    }

    &-title {
      font-size: 20px;
      line-height: 26px;
      font-weight: 700;
    }

    &-badges {
      margin-top: 5px;
      display: flex;
      align-items: center;
      gap: 8px;

      .badge-title {
        font-size: 12px;
        font-weight: 700;
        line-height: 14px;
        margin-left: -4px;
      }

      .purple {
        color: var(--kui-amethyst-500);
      }

      .red {
        color: var(--kui-red-500);
      }
    }

    &-author {
      &:hover {
        color: var(--kui-deep-teal-400);

        .article-card-author-avatar {
          border-width: 3px;
        }

        .article-card-separator {
          background-color: var(--kui-deep-teal-400);
        }
      }

      &-avatar {
        border-radius: 50%;
        border: 1px solid var(--kui-deep-teal-400-o80);
        object-fit: cover;
      }
    }

    &-title,
    &-lead {
      transition: all 0.3s;
      text-decoration: underline transparent solid 1px;
      text-underline-offset: 5px;
    }

    &-author,
    &-tag {
      transition: all 0.3s;
    }

    &-guest {
      &-datas {
        color: var(--kui-deep-teal-400);
        font-size: 14px;
        font-weight: 500;
        line-height: normal;
        padding: 0 8px;
        margin-top: 8px;
      }
    }

    &-podcast {
      position: absolute;
      inset: 0;
      margin: auto;
      width: fit-content;
      height: fit-content;

      &-thumbnail {
        position: relative;
        overflow: hidden;
        transition: all 0.5s;
        flex: 0 0 auto;

        &:hover {
          .article-card-podcast-type-column {
            color: var(--kui-deep-teal-400);
          }
        }

        &-wrapper {
          @include media-breakpoint-down(xs) {
            @media (hover: none) {
              max-width: 50%;
            }
          }
        }
      }

      &-type {
        background-color: var(--kui-gray-950-65);
        border: 1px solid var(--kui-deep-teal-400);
        border-radius: 2px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 15px 14px 18px;
        color: var(--kui-white);
        transition: all 0.3s;
        gap: 5px;
        text-transform: uppercase;
        min-width: 140px;
        height: 68px;

        @include media-breakpoint-down(sm) {
          padding: 6px 8px;
        }

        &-column {
          font-size: 19px;
          letter-spacing: 0.4px;
          font-weight: 700;
          transition: 0.5s;
          text-align: center;
        }

        &-label {
          font-size: 12px;
          letter-spacing: 3.5px;
          font-weight: 300;
          color: var(--kui-gray-200);
          text-align: center;
        }
      }

      &-length {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        padding: 9px 0;
        border-radius: 2px;
        background-color: var(--kui-gray-950-85);
        color: var(--kui-white);
        line-height: 14px;
        font-size: 12px;
        font-weight: 500;
        transition: all 0.3s;
        height: 32px;

        @include media-breakpoint-down(sm) {
          padding: 2px;
        }
      }
    }
  }

  // Sidebar
  &.is-sidebar {
    .article-card {
      &-category {
        font-size: 14px;
        line-height: 18px;
      }

      &-tag-with-column {
        gap: 12px;
      }
    }
  }

  // Global hover states
  &:hover {
    .article-card {
      &-lead,
      &-title:not(.disable-hover) {
        text-decoration-color: var(--kui-deep-teal-400);
      }

      &-thumbnail {
        transform: scale(1.1);
      }
    }
  }

  // @hasOutline property
  .outlined {
    border: 1px solid var(--kui-gray-100);
    border-radius: 2px 2px 0 0;

    &:hover {
      border-color: var(--kui-deep-teal-400);
    }

    &.light-mode {
      border: 1px solid var(--kui-gray-700);
    }
  }

  &.style-RelatedArticlePlus,
  &.style-FeaturedTitle,
  &.style-FeaturedTagTitle {
    .outlined {
      .article-card-link {
        margin: 8px;
      }
    }
  }

  // @isLight property
  .light-mode {
    .article-card {
      &-tag,
      &-author,
      &-date,
      &-lead,
      &-title {
        color: var(--kui-white);
      }
    }
  }

  &.style-FeaturedTopImgTagTitle {
    .article-card {
      &-data {
        margin: 8px;
      }

      &-tag {
        margin-bottom: 4px;
      }

      &-author {
        display: flex;
        flex-direction: row-reverse;
        justify-content: space-between;
        margin-top: 8px;
        gap: 24px;

        &-avatar {
          @include size(56px);
        }

        &-name {
          font-size: 16px;
          line-height: 26px;
        }

        &-rank {
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px;
        }
      }

      &-badges {
        kesma-icon {
          @include size(24px);
        }
      }
    }
  }

  &.style-FeaturedBigTopImgTagTitleLead {
    .article-card {
      &-data {
        display: flex;
        flex-direction: column;
        margin-top: 20px;
        gap: 20px;

        @include media-breakpoint-down(sm) {
          gap: 16px;
          margin: 20px 24px 8px;
        }
      }

      &-title {
        font-size: 36px;
        line-height: 40px;

        @include media-breakpoint-down(sm) {
          font-size: 28px;
          font-weight: 700;
        }
      }

      &-lead {
        font-size: 24px;
        line-height: normal;
        font-weight: 500;

        @include media-breakpoint-down(sm) {
          font-size: 20px;
        }
      }

      &-thumbnail-meta {
        font-size: 20px;
        padding: 16px 24px;
      }

      &-badges {
        margin-top: -10px;

        kesma-icon {
          @include size(24px);
        }
      }
    }

    .outlined {
      .article-card-data {
        margin: 20px 8px 8px 8px;
      }
    }
  }

  &.style-ExternalRecommendation {
    .article-card {
      &-link {
        padding: 4px 4px 8px;

        &:hover {
          background-color: var(--kui-deep-teal-100);
        }
      }

      &-title {
        margin-top: 4px;
        font-weight: 400;
        font-size: 14px;
        line-height: 18px;
      }

      &-thumbnail-meta {
        font-size: 12px;
        padding: 4px 8px;
        font-weight: 500;
        line-height: 14px;
      }

      &-data {
        color: var(--kui-white);
      }
    }
  }

  &.style-FeaturedRightImgTagTitleLead {
    @mixin mobileView() {
      .article-card {
        &-thumbnail {
          width: 112px;
          height: 112px;

          &-card {
            flex-basis: 112px;
          }
        }

        &-data {
          flex-direction: column-reverse;

          img {
            width: 100%;
            height: auto;
            max-width: unset;
          }
        }

        &-lead {
          margin-top: 8px;
        }

        &-badges {
          margin-top: 5px;
        }

        &-author {
          gap: 16px;

          &-details {
            display: block;
          }

          &-avatar {
            @include size(40px);
          }

          &-rank,
          &-name {
            font-size: 16px;
            line-height: 22px;
          }
        }

        &-tag {
          font-size: 14px;
          line-height: 18px;
        }

        &-separator {
          display: none;
        }
      }
    }

    .article-card {
      &-data {
        display: flex;
        justify-content: space-between;
        gap: 24px;

        @include media-breakpoint-down(sm) {
          width: 100%;
          overflow: hidden;
        }
      }

      &-row {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      &-tag {
        @include media-breakpoint-down(sm) {
          overflow: hidden;
          text-overflow: ellipsis;
          display: inline-block;
        }
      }

      &-title {
        line-height: 22px;
      }

      &-thumbnail {
        max-width: 160px;
        height: 90px;

        &-card {
          flex: 0 0 160px;
        }
      }

      &-lead {
        font-weight: 400;
        line-height: 24px;
      }

      &-category {
        font-size: 14px;
        line-height: 18px;
      }

      &-title,
      &-lead {
        font-size: 15px;
      }

      &-badges {
        margin-top: -5px;
      }

      &-author {
        margin-top: 8px;

        &,
        &-details {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        &-rank,
        &-name {
          font-size: 12px;
          line-height: 14px;
        }

        &-avatar {
          @include size(24px);
        }
      }

      &-podcast {
        &-type {
          min-width: auto;
          height: auto;
          justify-content: center;

          &-column {
            display: none;
          }
        }
      }
    }

    .mobile-view {
      @include mobileView();

      .mobile {
        display: flex;
      }

      .desktop {
        display: none;
      }
    }

    @include media-breakpoint-down(sm) {
      @include mobileView();
    }

    .outlined {
      .article-card-link {
        margin: 8px;
      }
    }
  }

  &.style-FeaturedTagTitle {
    .article-card {
      &-title {
        margin-top: 8px;
        font-size: 16px;
        line-height: 22px;
      }

      &-tag {
        font-size: 14px;
        line-height: 18px;
      }
    }
  }

  &.style-FeaturedTopImgTagTitleLead {
    .article-card {
      &-data {
        margin: 8px;
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      &-tag {
        font-size: 14px;
        line-height: 18px;
      }

      &-lead {
        font-size: 18px;
        line-height: 24px;
      }

      &-badges {
        margin-top: -3px;
      }
    }
  }

  &.style-RelatedArticle,
  &.style-FeaturedRightImgTagTitle,
  &.style-MostRead {
    .article-card {
      &-link {
        display: flex;
        justify-content: space-between;
        gap: 16px;
        @include media-breakpoint-down(sm) {
          flex-direction: column-reverse;
          .article-card-thumbnail-card {
            height: auto;
          }
          img {
            width: 100%;
            height: auto;
          }
        }
      }

      &-data {
        display: flex;
        flex-direction: column;
        margin: 8px;
        gap: 8px;
      }

      &-title {
        font-size: 18px;
        line-height: normal;
      }

      &-tag {
        font-size: 14px;
        line-height: 18px;
      }

      &-thumbnail {
        height: 112px;
        width: 112px;
        object-fit: cover;

        &-card {
          flex: 0 0 112px;
          height: 112px;
        }
      }

      &-badges {
        margin-top: -3px;
      }
    }
  }

  &.style-MostRead {
    .outlined {
      border-color: var(--kui-deep-teal-200);
      border-radius: 2px;
      transition: all 0.5s;
    }

    .article-card {
      &-tag {
        font-size: 12px;
        line-height: 14px;
        font-weight: 500;
      }

      &-data {
        margin: 8px 0 8px 8px;
      }

      &-badges {
        margin-top: -8px;
      }

      &-title {
        font-size: 14px;
        line-height: 18px;
        text-underline-offset: 3px;
      }

      &-thumbnail {
        height: 132px;
        width: 132px;
        object-fit: cover;
        @include media-breakpoint-down(sm) {
          height: 112px;
        }

        &-index {
          position: absolute;
          bottom: 0;
          right: 0;
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--kui-deep-teal-300);
          border-radius: 2px;
          border: 2px solid var(--kui-deep-teal-300);
          transition: all 0.5s;
        }
      }

      &-thumbnail-card {
        @include media-breakpoint-up(xs) {
          height: 132px;
          flex: 0 0 132px;
          top: -1px;
          right: -1px;
        }
      }
    }

    &:hover {
      .outlined {
        border-color: var(--kui-deep-teal-400);
      }

      .article-card-thumbnail {
        transform: scale(1);

        &-index {
          border: 1px solid var(--kui-deep-teal-400);
          width: 100%;
          height: 100%;
        }
      }
    }
  }

  &.style-FreshNews {
    .article-card {
      &-data {
        display: flex;
        flex-direction: column;
        gap: 1px;
      }

      &-tag {
        display: inline;
      }

      &-publish-date {
        color: var(--kui-deep-teal-400);
      }

      &-row {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      &-separator {
        height: 20px;
        display: inline-grid;
        margin-right: 8px;
        margin-left: 4px;
        margin-bottom: -4px;
      }

      &-title {
        font-size: 16px;
        line-height: 22px;
        display: inline;
        transition: all 0.3s;
        text-decoration-color: var(--kui-white);
      }
    }

    &:hover {
      .article-card {
        &-title {
          text-decoration: underline var(--kui-deep-teal-400);
        }
      }
    }
  }

  &.style-RelatedArticle {
    .article-card {
      &-tag {
        font-size: 12px;
        line-height: 14px;
        font-weight: 500;
      }

      &-title {
        font-size: 14px;
        line-height: 18px;
      }

      &-data {
        gap: 0;
      }

      &-badges {
        kesma-icon {
          margin: 7px 0 0;
          @include size(16px);
        }
      }

      &-thumbnail {
        height: 104px;

        &-card {
          flex: 0 0 104px;
          height: 104px;
          position: relative;

          kesma-icon {
            position: absolute;
            bottom: 0;
            right: 0;
            color: var(--kui-deep-teal-300);
          }
        }
      }
    }

    &:hover .article-card-title {
      text-underline-offset: 3px;
    }

    .outlined {
      border-color: var(--kui-deep-teal-100);
    }
  }

  &.style-RelatedArticlePlus {
    .article-card {
      &-link {
        display: flex;
        align-items: flex-start;
        gap: 8px;

        kesma-icon {
          min-width: 20px;
        }

        .vg-plus {
          color: var(--kui-deep-teal-400);
          transition: all 0.3s;
        }

        &:hover {
          .vg-plus {
            color: var(--kui-amethyst-500);
          }
        }
      }

      &-data {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
      }

      &-title {
        font-size: 16px;
        line-height: 22px;
      }

      &-badges {
        margin-top: 0;
      }
    }
  }

  &.style-OpinionMain {
    .article-card {
      color: var(--kui-white);

      &-link {
        display: flex;
        justify-content: space-between;

        @include media-breakpoint-down(md) {
          flex-direction: column-reverse;
          gap: 10px;
        }
      }

      &-tag {
        display: flex;
        flex-direction: column;
        align-items: center;

        @include media-breakpoint-down(md) {
          flex-direction: row;
          font-size: 14px;
          line-height: 18px;
        }

        kesma-icon {
          width: 48px;
          height: 48px;
          color: var(--kui-deep-teal-400);

          @include media-breakpoint-down(md) {
            width: 24px;
            height: 24px;
          }
        }

        &:hover {
          color: var(--kui-deep-teal-400);

          kesma-icon {
            color: var(--kui-deep-teal-400);
          }
        }
      }

      &-title {
        font-size: 24px;
        line-height: normal;
        text-align: center;
        font-weight: 700;
        letter-spacing: 0.96px;

        @include media-breakpoint-down(md) {
          font-size: 20px;
          line-height: 26px;
          letter-spacing: 0.4px;
        }
      }

      &-data {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 25px;
        padding: 0 16px;
        flex: 1;

        @include media-breakpoint-down(md) {
          gap: 16px;
        }
      }

      &-title,
      &-lead,
      &-tag {
        color: var(--kui-white);
      }

      &-lead {
        line-height: 24px;
        text-align: center;
        font-size: 16px;
        font-weight: 400;
      }

      &-author {
        display: flex;
        align-items: center;
        gap: 24px;
        color: var(--kui-white);
        margin-top: 16px;
        padding-bottom: 16px;
        font-size: 18px;
        line-height: normal;
        letter-spacing: 0.36px;

        @include media-breakpoint-down(md) {
          margin: 8px 0;
          font-size: 16px;
          line-height: 22px;
          letter-spacing: unset;
          gap: 16px;
        }

        @include media-breakpoint-down(md) {
          gap: 16px;
        }

        &-rank {
          font-size: 12px;
          font-weight: 400;
          line-height: 14px;
        }

        &:hover {
          .article-card-author-name,
          .article-card-author-rank {
            color: var(--kui-deep-teal-400);
          }
        }

        &-avatar {
          width: 56px;
          height: 56px;

          @include media-breakpoint-down(md) {
            width: 40px;
            height: 40px;
          }
        }
      }

      &-thumbnail {
        width: 422px;
        height: 422px;

        @include media-breakpoint-down(md) {
          width: 100%;
          height: unset;
        }

        &-card {
          @include media-breakpoint-down(xl) {
            flex: 1;
          }
        }
      }
    }
  }

  &.style-OpinionSecondary {
    height: 50%;

    .article-card {
      &-row {
        display: flex;
        flex-direction: row;
        width: 100%;
        gap: 5px;
        justify-content: space-between;
        @include media-breakpoint-down(xs) {
          flex-direction: column-reverse;
        }
      }

      &-data {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        height: 100%;
        gap: 8px;

        @include media-breakpoint-down(xs) {
          max-width: 100%;
        }

        &-meta-row {
          display: flex;
          gap: 12px;

          @include media-breakpoint-down(xs) {
            flex-wrap: wrap;
          }
        }
      }

      &-column {
        background-color: var(--kui-gray-950-65);
        padding: 8px;
        border: 1px solid var(--kui-gray-50);
        border-radius: 2px;

        &:hover {
          background-color: var(--kui-deep-teal-400-o80);
          border-color: var(--kui-deep-teal-400);
          color: var(--kui-white);
        }
      }

      &-title,
      &-lead,
      &-column,
      &-tag {
        color: var(--kui-white);
      }

      &-tag,
      &-column {
        font-size: 14px;
        font-weight: 700;
        line-height: 18px;
      }

      &-tag {
        &:hover {
          color: var(--kui-deep-teal-400);

          kesma-icon {
            color: var(--kui-deep-teal-400);
          }
        }
      }

      &-thumbnail {
        &-wrapper {
          margin-left: 10px;
          @include media-breakpoint-down(xs) {
            margin: 0;
          }
        }
      }

      &-thumbnail-card {
        width: 100%;
        max-width: 112px;
        max-height: 112px;
        flex-basis: fit-content;
        @include media-breakpoint-down(xs) {
          max-width: 100%;
          max-height: 100%;
        }
      }

      &-title {
        font-size: 18px;
        line-height: normal;

        @include media-breakpoint-down(sm) {
          font-size: 16px;
          line-height: 22px;
        }
      }

      &-lead {
        display: none;
        margin-top: 8px;
        font-size: 16px;
        font-weight: 400;
        line-height: 22px;

        @include media-breakpoint-down(sm) {
          display: block;
        }
      }

      &-badges {
        display: none;

        @include media-breakpoint-down(sm) {
          display: flex;
        }
      }

      &-author {
        display: flex;
        gap: 24px;
        align-items: center;
        margin: 16px 0;

        @include media-breakpoint-down(md) {
          gap: 16px;
          margin: 8px 0;
        }

        &:hover {
          .article-card-author-name,
          .article-card-author-rank {
            color: var(--kui-deep-teal-400);
          }
        }

        &-avatar {
          height: 56px;
          width: 56px;

          @include media-breakpoint-down(md) {
            width: 40px;
            height: 40px;
          }
        }

        &-name {
          color: var(--kui-white);
          font-size: 18px;
          font-weight: 700;
          line-height: normal;

          @include media-breakpoint-down(md) {
            font-size: 16px;
            line-height: 22px;
          }
        }

        &-rank {
          color: var(--kui-white);
          font-size: 12px;
          font-weight: 400;
          line-height: 14px;
        }
      }
    }
  }

  &.style-FeaturedTitle {
    .article-card {
      &-title {
        font-size: 14px;
        line-height: 18px;
      }

      &-badges kesma-icon {
        @include size(16px);
      }
    }

    &:hover .article-card-title {
      text-underline-offset: 3px;
    }
  }

  &.style-FeaturedImgAbsoluteTagTitle4Per3Line,
  &.style-FeaturedImgAbsoluteTagTitle4Per3,
  &.style-FeaturedImgAbsoluteTagTitle4Per3Separator,
  &.style-FeaturedImgAbsoluteTagTitle1Per1,
  &.style-FeaturedImgAbsoluteTagTitle {
    .article-card {
      &-thumbnail-meta {
        @extend %category;
        bottom: 0;
        flex-direction: column-reverse;
        padding: 16px 16px 24px;
        width: 100%;
        height: 100%;
      }

      &-data {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .chevron-wrapper {
          margin-left: auto;

          kesma-icon {
            color: var(--kui-white);
          }
        }
      }

      &-title {
        font-size: 14px;
        line-height: 18px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 4;
        color: var(--kui-white);
      }

      &-tag {
        color: var(--kui-white);
        font-size: 12px;
        line-height: 14px;

        kesma-icon {
          color: var(--kui-white);
        }
      }
    }

    &:hover {
      .article-card-title {
        text-underline-offset: 3px;
      }

      .article-card-thumbnail-meta {
        background-color: var(--kui-gray-950-65);
      }
    }
  }

  &.style-FeaturedImgAbsoluteTagTitle {
    .article-card {
      &-thumbnail-meta {
        padding: 16px;
      }

      &-title {
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
  }

  &.style-FeaturedImgAbsoluteTagTitle1Per1,
  &.style-FeaturedImgAbsoluteTagTitle4Per3Separator,
  &.style-FeaturedImgAbsoluteTagTitle4Per3 {
    .article-card {
      &-thumbnail {
        &-meta {
          padding: 8px 24px 16px;
        }
      }

      &-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: var(--kui-deep-teal-400);
        height: 34px;
      }

      &-separator {
        width: 100%;
        height: 1px;
        margin: 8px 0;
      }

      &-badges .icon {
        @include size(20px);
      }

      &-icon {
        display: flex;
        align-items: center;
        margin-left: auto;
        gap: 10px;

        kesma-icon {
          color: var(--kui-deep-teal-400);
        }
      }
    }

    .read-more {
      color: var(--kui-deep-teal-400);

      @include media-breakpoint-down(sm) {
        display: none;
      }
    }
  }

  &.style-FeaturedImgAbsoluteTagTitle1Per1 {
    .article-card {
      &-data {
        display: block;
        z-index: 500;
      }

      &-row {
        flex-direction: column;
        align-items: flex-start;
        height: auto;

        &.fixed-height {
          height: 77px;
          justify-content: flex-start;
        }
      }

      &-tag {
        margin-bottom: 8px;
      }

      &-title {
        -webkit-line-clamp: 3;
        color: var(--kui-white);
      }

      &-icon {
        width: 100%;
        justify-content: flex-end;

        kesma-icon {
          margin-left: unset;
        }

        &:hover {
          kesma-icon,
          .read-more {
            color: var(--kui-amethyst-500);
          }

          border-bottom: 1px solid var(--kui-amethyst-500);
        }
      }
    }

    .read-more {
      display: block;
      padding: 8px 0;
      font-size: 14px;
    }
  }

  &.style-FeaturedTopAuthorImgTagTitle,
  &.style-FeaturedRightAuthorImgTagTitle {
    .article-card {
      &-link {
        justify-content: space-between;
        display: flex;
        margin: 8px;
        gap: 16px;
      }

      &-title {
        font-size: 14px;
        line-height: 18px;
      }

      &-tag {
        font-size: 12px;
        line-height: 14px;
        font-weight: 500;
      }

      &-author {
        &-name {
          font-size: 14px;
          line-height: 18px;
          color: var(--kui-deep-teal-600);
          margin-bottom: 8px;
          display: block;

          &:hover {
            color: var(--kui-gray-950);
          }
        }

        &-avatar {
          height: 88px;

          &-wrapper {
            flex: 0 0 88px;
          }
        }
      }
    }

    &:hover {
      .outlined {
        border-color: var(--kui-deep-teal-400);
      }
    }
  }

  &.style-FeaturedTopAuthorImgTagTitle {
    .article-card {
      &-link {
        display: flex;
        flex-direction: column;
        gap: 8px;

        @include media-breakpoint-down(sm) {
          flex-direction: row-reverse;
          gap: 16px;
        }
      }

      &-author {
        &-avatar {
          width: 88px;

          @include media-breakpoint-up(sm) {
            margin: 0 auto;
          }

          &-wrapper {
            display: flex;
          }
        }

        &-name {
          @include media-breakpoint-up(sm) {
            text-align: center;
          }
        }
      }
    }
  }

  &.style-FeaturedRightImgDateTagTitle {
    .article-card {
      &-link {
        display: flex;

        @include media-breakpoint-down(sm) {
          flex-direction: column;
        }
      }

      &-separator {
        @include media-breakpoint-up(sm) {
          display: none;
        }
      }

      &-date {
        font-weight: 700;
        font-size: 12px;
        line-height: 18px;
        flex: 1 0 100px;
        padding: 28px 8px 8px 0;
        max-width: 100px;
        display: flex;
        flex-direction: column;
        gap: 4px;

        @include media-breakpoint-down(sm) {
          font-weight: 500;
          padding: 8px 8px 8px 0;
          max-width: unset;
          flex: unset;
          flex-direction: row;
          gap: 16px;
          line-height: 14px;
        }
      }

      &-tag {
        font-size: 14px;
        line-height: 18px;
      }

      &-title {
        font-size: 18px;
        line-height: normal;
      }

      &-badges {
        margin-top: -5px;

        kesma-icon {
          @include size(24px);
        }
      }

      &-data {
        display: flex;
        justify-content: space-between;
        gap: 24px;
        border-left: 1px solid var(--kui-gray-950);
        padding-left: 24px;
        width: 100%;
        overflow: hidden;

        @include media-breakpoint-down(sm) {
          border-top: 1px solid var(--kui-gray-950);
          border-left: none;
          padding-left: 0;
          padding-top: 8px;
          gap: 16px;
        }
      }

      &-row {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      &-thumbnail {
        width: 200px;
        height: 112.5px;

        &-card {
          flex: 0 0 200px;
          height: 112.5px;
        }

        @include media-breakpoint-down(sm) {
          width: 112.5px;

          &-card {
            flex-basis: 112px;
          }
        }
      }

      &-podcast {
        &-type {
          @include media-breakpoint-down(sm) {
            min-width: auto;
            height: auto;
            justify-content: center;
          }

          &-column {
            @include media-breakpoint-down(sm) {
              display: none;
            }
          }
        }
      }

      &-author {
        &-rank,
        &-avatar {
          display: none;
        }

        &-name {
          font-size: 14px;
          line-height: 18px;

          @include media-breakpoint-down(sm) {
            font-size: 12px;
            line-height: 14px;
          }
        }
      }
    }

    &:not(:has(.highlighted)) {
      @include media-breakpoint-down(sm) {
        .article-card-tag {
          font-size: 12px;
          line-height: 14px;
        }

        .article-card-title {
          font-size: 16px;
          line-height: 22px;
        }
      }
    }

    // Highlighted appearance at the top of article lists
    .highlighted {
      .article-card-thumbnail {
        width: 424px;
        height: 238px;

        &-card {
          flex: 0 0 424px;
          height: 238px;
        }

        @include media-breakpoint-down(sm) {
          &-card {
            flex-basis: 100%;
            height: unset;
          }
        }
      }

      .article-card-title {
        font-size: 20px;
      }

      @include media-breakpoint-down(sm) {
        .article-card-data {
          flex-direction: column;
          gap: 8px;
        }
      }
    }

    .outlined {
      .article-card-link {
        margin: 8px;
      }
    }

    .mobile-view {
      .article-card-data {
        flex-direction: column;
      }

      .article-card-thumbnail {
        &-card {
          flex-basis: 112px;
        }
      }
    }
  }

  &.style-ColumnMain {
    &.is-sidebar {
      .article-card-data {
        flex-direction: column;
        gap: 16px;
      }
    }

    .article-card {
      &-data {
        display: flex;
        gap: 24px;
        justify-content: space-between;

        @include media-breakpoint-down(sm) {
          flex-direction: column;
          gap: 16px;
        }
      }

      &-row {
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      &-tag {
        font-size: 14px;
        line-height: 18px;
      }

      &-title {
        letter-spacing: 0.4px;

        @include media-breakpoint-down(sm) {
          font-size: 18px;
          line-height: normal;
          letter-spacing: 0.36px;
        }
      }

      &-lead {
        font-size: 18px;
        line-height: 24px;
        letter-spacing: 0.36px;
        font-weight: 400;

        @include media-breakpoint-down(sm) {
          font-size: 16px;
          letter-spacing: normal;
        }
      }

      &-badges {
        margin-top: -5px;

        kesma-icon {
          @include size(20px);

          @include media-breakpoint-up(sm) {
            @include size(24px);
          }
        }
      }

      &-author {
        font-size: 12px;
        font-weight: 700;
        line-height: 14px;

        @include media-breakpoint-up(sm) {
          display: none;
        }
      }

      &-thumbnail {
        max-width: 368px;

        @include media-breakpoint-down(sm) {
          max-width: none;
        }

        &-wrapper {
          flex: 0 0 auto;
        }
      }
    }
  }

  &.style-Podcast {
    &.is-sidebar.column-title-length {
      &-m {
        .article-card-podcast-type-column {
          font-size: 10px !important;
        }
      }

      &-l {
        .article-card-podcast-type-column {
          font-size: 7px !important;
        }
      }
    }

    .article-card {
      &-link {
        border: 1px solid var(--kui-gray-700);
        gap: 8px;
        padding-bottom: 8px;
        height: 100%;

        &:hover {
          border: 1px solid var(--kui-deep-teal-400);
        }

        &.highlighted {
          .article-card {
            &-data {
              flex-direction: column;
            }

            &-tag {
              margin-bottom: 16px;
            }

            &-title {
              font-size: 20px;
              line-height: 26px;
              letter-spacing: 0.4px;
            }

            &-guest {
              &-datas {
                font-weight: 400;

                & > div:first-child {
                  font-size: 16px;
                  font-weight: 500;
                  line-height: 24px;
                }
              }
            }

            &-podcast {
              @include media-breakpoint-down(sm) {
                max-width: none;
              }

              &-thumbnail {
                height: 176px;
                width: 100%;
              }

              &-type {
                @include media-breakpoint-down(sm) {
                  width: auto;
                  height: 68px;
                }
              }

              &-length {
                @include media-breakpoint-down(sm) {
                  padding: 9px 0;
                }
              }
            }
          }
        }
      }

      &-link:not(.highlighted) {
        @include media-breakpoint-down(sm) {
          .article-card-podcast-type {
            height: auto;
          }
          .article-card-podcast-type-column {
            font-size: 8px;
          }
        }
      }

      &-thumbnail {
        height: 100%;
      }

      &-tag {
        font-size: 14px;
        line-height: 18px;
        margin-bottom: 4px;
        transition: all 0.3s;

        &:hover {
          color: var(--kui-deep-teal-400);
        }
      }

      &-title {
        font-size: 18px;
        line-height: 24px;

        @include media-breakpoint-down(sm) {
          font-size: 16px;
          line-height: 22px;
        }
      }

      &-data {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }

      &-body {
        padding: 0 8px;

        @include media-breakpoint-down(sm) {
          width: 100%;
          padding: 4px 8px 0 8px;
        }
      }

      &-lead {
        color: var(--kui-deep-teal-400);
        text-decoration: none;
        font-size: 14px;
        line-height: 18px;
        font-weight: 500;
        padding: 0 8px;
        margin-top: 8px;
      }

      &-podcast {
        @include media-breakpoint-down(sm) {
          max-width: 78px;
        }

        &-type {
          background-color: var(--kui-gray-950-65);
          border: 1px solid var(--kui-deep-teal-400);
          border-radius: 2px;
          display: flex;
          gap: 5px;
          flex-direction: column;
          align-items: center;
          padding: 11px 14px;
          color: var(--kui-white);
          transition: all 0.3s;
          height: 68px;
          min-width: 140px;
          text-transform: uppercase;

          @include media-breakpoint-down(sm) {
            padding: 6px 8px;
            height: 38px;
            min-width: auto;
            justify-content: center;
          }

          &-column {
            font-size: 15px;
            letter-spacing: 0.4px;
            font-weight: 700;

            &.podcast-box-on-article-page {
              font-size: 12px;
            }
          }

          &-label {
            font-size: 12px;
            letter-spacing: 3.5px;
            font-weight: 300;
            color: var(--kui-gray-200);

            @include media-breakpoint-down(sm) {
              font-size: 10px;
              letter-spacing: 2px;
            }
          }
        }

        &-thumbnail {
          height: 200px;

          @include media-breakpoint-down(sm) {
            width: 100%;
            border-radius: 2px 0 0 2px;
          }

          &:hover {
            background-size: 120%;

            .article-card-podcast-type {
              color: var(--kui-deep-teal-400);
            }
          }
        }
      }
    }

    &.is-sidebar {
      .article-card-podcast-type {
        min-width: auto;
        justify-content: center;

        &-label {
          font-size: 11px;
          letter-spacing: 2px;
        }
      }
    }

    .mobile-view {
      .article-card {
        &-title {
          font-size: 16px;
          line-height: 22px;
        }

        &-data {
          flex-direction: row-reverse;
        }

        &-body {
          width: 100%;
          padding: 4px 8px 0 8px;
        }

        &-podcast {
          max-width: 78px;

          &-type {
            padding: 6px 8px;

            kesma-icon {
              height: 22px;
              width: 62px;
            }
          }

          &-thumbnail {
            min-width: 112px;
            height: 112px;
            width: 112px;
            border-radius: 2px 0 0 2px;
          }
        }
      }
    }
  }

  &.style-PodcastLight {
    .article-card {
      &-data {
        margin: 8px;
      }

      &-tag {
        margin-top: 8px;
        margin-bottom: 4px;
      }

      &-guest {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        margin-top: 8px;
        gap: 24px;

        &-avatar {
          @include size(56px);
          border-radius: 50%;
          border: 1px solid var(--kui-deep-teal-400-o80);
        }

        &-datas {
          color: var(--kui-gray-950);
          padding: 0;

          &-name {
            font-size: 16px;
            line-height: 26px;
            letter-spacing: 0.4px;
            font-weight: 700;
          }

          &-rank {
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px;
          }

          &-title {
            font-size: 18px;
            line-height: 24px;
            letter-spacing: 0.36px;
          }
        }
      }

      &-podcast {
        &-thumbnail {
          .isPlaceHolder {
            object-fit: unset;
          }

          &:hover {
            background-size: 120%;

            .article-card-podcast-type {
              color: var(--kui-deep-teal-400);
            }
          }
        }
      }
    }

    .outlined {
      .article-card-podcast-thumbnail {
        @extend %outlined-thumbnail;
      }
    }
  }

  .mobile {
    display: none;

    @include media-breakpoint-down(sm) {
      display: flex; // Flex for badges.
    }
  }

  .desktop {
    display: flex; // Flex for badges.

    @include media-breakpoint-down(sm) {
      display: none;
    }
  }
}

%category {
  background-color: var(--kui-deep-teal-950-o65);
  color: var(--kui-white);
  display: flex;
}

%outlined-thumbnail {
  margin-inline: -8px;
  margin-top: -8px;
  width: calc(100% + 16px);
}
