<ng-container *ngIf="data" [ngSwitch]="styleID">
  <article [class.light-mode]="isLight" [class.mobile-view]="isMobileView" [class.outlined]="hasOutline">
    <ng-container *ngSwitchCase="ArticleCardType.FeaturedTopImgTagTitle">
      <div class="article-card-link">
        @if (data?.isPodcastType) {
          <a [routerLink]="buildUrlByContentType(data)" class="article-card-thumbnail-wrapper" [attr.aria-label]="data?.title">
            <ng-container *ngTemplateOutlet="podcastArticleThumbnail; context: { displayedAspectRatio: { desktop: '16:9' } }"></ng-container>
          </a>
        } @else {
          <div class="article-card-thumbnail-wrapper">
            <ng-container
              *ngTemplateOutlet="
                thumbnailTemplate;
                context: {
                  showCategoryName: true,
                  isLayer: true,
                  displayedAspectRatio: { desktop: '16:9' },
                }
              "
            >
            </ng-container>
          </div>
        }

        <div class="article-card-data">
          <ng-container *ngTemplateOutlet="firstTagTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="badgesTemplate"></ng-container>
        </div>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.FeaturedBigTopImgTagTitleLead">
      <div class="article-card-link">
        @if (data?.isPodcastType) {
          <a [routerLink]="buildUrlByContentType(data)" class="article-card-thumbnail-wrapper" [attr.aria-label]="data?.title">
            <ng-container *ngTemplateOutlet="podcastArticleThumbnail; context: { displayedAspectRatio: { desktop: '16:9' } }"></ng-container>
          </a>
        } @else {
          <div [routerLink]="buildUrlByContentType(data)" class="article-card-thumbnail-wrapper">
            <ng-container
              *ngTemplateOutlet="
                thumbnailTemplate;
                context: {
                  showCategoryName: true,
                  isLayer: true,
                  displayedAspectRatio: { desktop: '16:9' },
                }
              "
            >
            </ng-container>
          </div>
        }

        <div class="article-card-data">
          <ng-container *ngTemplateOutlet="firstTagTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="leadTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="badgesTemplate"></ng-container>
        </div>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.ExternalRecommendation">
      <div class="article-card-link">
        @if (data?.url) {
          <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { customTemplate: contentTemplate, isLayer: true, hasOuterLink: true }"></ng-container>

          <ng-template #contentTemplate>
            <a [href]="data?.url" target="_blank" class="article-card-data">{{ data?.category?.name || data?.columnTitle }}</a>
          </ng-template>

          <a [href]="data?.url" target="_blank" [attr.aria-label]="data?.title">
            <h2 class="article-card-title disable-hover">{{ data?.title }}</h2>
          </a>
        } @else {
          <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { customTemplate: contentTemplate, isLayer: true }"></ng-container>
          <ng-template #contentTemplate>
            <a [routerLink]="buildUrlByContentType(data)" class="article-card-data">{{ data?.category?.name || data?.columnTitle }}</a>
          </ng-template>
          <a [routerLink]="buildUrlByContentType(data)" [attr.aria-label]="data?.title">
            <h2 class="article-card-title disable-hover">{{ data?.title }}</h2>
          </a>
        }
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.FeaturedRightImgTagTitleLead">
      <div class="article-card-link">
        <div class="article-card-data">
          <div class="article-card-row">
            <div class="article-card-tag-with-column">
              <ng-container *ngTemplateOutlet="categoryTemplate"></ng-container>
              <ng-container *ngTemplateOutlet="firstTagTemplate"></ng-container>
            </div>
            <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
            <ng-container *ngTemplateOutlet="leadTemplate; context: { customClass: 'desktop' }"></ng-container>
            <ng-container *ngTemplateOutlet="badgesTemplate; context: { customClass: 'desktop' }"></ng-container>
          </div>

          @if (data?.isPodcastType) {
            <a [routerLink]="buildUrlByContentType(data)" class="article-card-thumbnail-wrapper" [attr.aria-label]="data?.title">
              <ng-container
                *ngTemplateOutlet="
                  podcastArticleThumbnail;
                  context: {
                    isMobileSquare: true,
                    displayedAspectRatio: { desktop: isMobileView ? '1:1' : '16:9', mobile: '1:1' },
                  }
                "
              >
              </ng-container>
            </a>
          } @else {
            <div [routerLink]="buildUrlByContentType(data)" class="article-card-thumbnail-wrapper">
              <ng-container
                *ngTemplateOutlet="
                  thumbnailTemplate;
                  context: {
                    isMobileSquare: true,
                    displayedAspectRatio: { desktop: isMobileView ? '1:1' : '16:9', mobile: '1:1' },
                  }
                "
              >
              </ng-container>
            </div>
          }
        </div>
        <ng-container *ngTemplateOutlet="leadTemplate; context: { customClass: 'mobile' }"></ng-container>
        <ng-container *ngTemplateOutlet="badgesTemplate; context: { customClass: 'mobile' }"></ng-container>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.FeaturedTagTitle">
      <div class="article-card-link">
        <ng-container *ngTemplateOutlet="firstTagTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="badgesTemplate"></ng-container>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.FeaturedTopImgTagTitleLead">
      <div class="article-card-link">
        @if (data?.isPodcastType) {
          <a [routerLink]="buildUrlByContentType(data)" class="article-card-thumbnail-wrapper" [attr.aria-label]="data?.title">
            <ng-container *ngTemplateOutlet="podcastArticleThumbnail; context: { displayedAspectRatio: { desktop: '16:9' } }"></ng-container>
          </a>
        } @else {
          <div [routerLink]="buildUrlByContentType(data)" class="article-card-thumbnail-wrapper">
            <ng-container
              *ngTemplateOutlet="
                thumbnailTemplate;
                context: {
                  showCategoryName: true,
                  isLayer: true,
                  displayedAspectRatio: { desktop: '16:9' },
                }
              "
            >
            </ng-container>
          </div>
        }

        <div class="article-card-data">
          <ng-container *ngTemplateOutlet="firstTagTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="leadTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="badgesTemplate"></ng-container>
        </div>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.FeaturedRightImgTagTitle">
      <div class="article-card-link">
        <div class="article-card-data">
          <ng-container *ngTemplateOutlet="firstTagTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="badgesTemplate"></ng-container>
        </div>

        <div class="article-card-thumbnail-wrapper">
          <ng-container
            *ngTemplateOutlet="
              thumbnailTemplate;
              context: {
                isDesktopSquare: true,
                isMobileSquare: true,
                displayedAspectRatio: { desktop: '1:1' },
              }
            "
          >
          </ng-container>
        </div>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.RelatedArticle">
      <div class="article-card-link">
        <div class="article-card-data">
          <ng-container *ngTemplateOutlet="firstTagTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="badgesTemplate"></ng-container>
        </div>

        <div class="article-card-thumbnail-wrapper">
          <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { displayedAspectRatio: { desktop: '16:9' } }"></ng-container>
        </div>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.RelatedArticlePlus">
      <div class="article-card-link">
        <a [routerLink]="buildUrlByContentType(data)" [attr.aria-label]="data?.title">
          <kesma-icon [size]="20" class="vg-plus" name="vg-plus"></kesma-icon>
        </a>

        <div class="article-card-data">
          <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="badgesTemplate"></ng-container>
        </div>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.FeaturedTitle">
      <div class="article-card-link">
        <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="badgesTemplate"></ng-container>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.FeaturedImgAbsoluteTagTitle">
      <div class="article-card-link">
        @if (data?.isPodcastType) {
          <a [routerLink]="buildUrlByContentType(data)" class="article-card-thumbnail-wrapper" [attr.aria-label]="data?.title">
            <ng-container *ngTemplateOutlet="podcastArticleThumbnail; context: { displayedAspectRatio: { desktop: '16:9' } }"></ng-container>
          </a>
        } @else {
          <div class="article-card-thumbnail-wrapper">
            <ng-container
              *ngTemplateOutlet="
                thumbnailTemplate;
                context: {
                  customTemplate: contentTemplate,
                  displayedAspectRatio: { desktop: '16:9' },
                }
              "
            >
            </ng-container>
          </div>
        }

        <ng-template #contentTemplate>
          <div class="article-card-data">
            <ng-container *ngTemplateOutlet="firstTagTemplate"></ng-container>
            <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
            <a [routerLink]="buildUrlByContentType(data)" class="chevron-wrapper" [attr.aria-label]="data?.title">
              <kesma-icon name="vg-chevron-diagonal-right"></kesma-icon>
            </a>
          </div>
        </ng-template>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.FeaturedImgAbsoluteTagTitle4Per3">
      <ng-container *ngTemplateOutlet="featuredImgAbsoluteTagTitle4Per3Template"></ng-container>
    </ng-container>
    <ng-container *ngSwitchCase="ArticleCardType.FeaturedImgAbsoluteTagTitle4Per3Separator">
      <ng-container *ngTemplateOutlet="featuredImgAbsoluteTagTitle4Per3Template"></ng-container>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.FeaturedImgAbsoluteTagTitle1Per1">
      <div class="article-card-link">
        @if (data?.isPodcastType) {
          <a [routerLink]="buildUrlByContentType(data)" class="article-card-thumbnail-wrapper" [attr.aria-label]="data?.title">
            <ng-container *ngTemplateOutlet="podcastArticleThumbnail; context: { displayedAspectRatio: { desktop: '1:1' } }"></ng-container>
          </a>
        } @else {
          <div class="article-card-thumbnail-wrapper">
            <ng-container
              *ngTemplateOutlet="
                thumbnailTemplate;
                context: {
                  customTemplate: contentTemplate,
                  displayedAspectRatio: { desktop: '1:1' },
                }
              "
            >
            </ng-container>
          </div>
        }

        <ng-template #contentTemplate>
          <div class="article-card-data">
            <ng-container *ngTemplateOutlet="firstTagTemplate"></ng-container>
            <div *ngIf="hasSeparator" class="article-card-separator"></div>
            <div class="article-card-row fixed-height">
              <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
              <ng-container *ngTemplateOutlet="badgesTemplate"></ng-container>
            </div>
            <div class="article-card-row">
              <a [routerLink]="buildUrlByContentType(data)" class="article-card-icon" [attr.aria-label]="data?.title">
                <span class="read-more">Olvasd tovább</span>
                <kesma-icon name="vg-chevron-diagonal-right"></kesma-icon>
              </a>
            </div>
          </div>
        </ng-template>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.FeaturedRightAuthorImgTagTitle">
      <div class="article-card-link">
        <div class="article-card-data">
          <a *ngIf="data?.author?.name" [routerLink]="buildAuthorUrl(data)" class="article-card-author" [attr.aria-label]="data?.title">
            <strong class="article-card-author-name">
              {{ data?.author?.name }}
            </strong>
          </a>
          <ng-container *ngTemplateOutlet="firstTagTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="badgesTemplate"></ng-container>
        </div>

        <a [routerLink]="buildUrlByContentType(data)" class="article-card-author-avatar-wrapper" [attr.aria-label]="data?.title">
          <img [alt]="data?.author?.name || ''" [src]="data?.author?.avatarUrl || placeholderAvatarUrl" class="article-card-author-avatar" loading="lazy" />
        </a>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.FeaturedTopAuthorImgTagTitle">
      <div class="article-card-link">
        <a [routerLink]="buildUrlByContentType(data)" class="article-card-author-avatar-wrapper" [attr.aria-label]="data?.title">
          <img alt="" [src]="data?.author?.avatarUrl || placeholderAvatarUrl" class="article-card-author-avatar" loading="lazy" />
        </a>

        <div class="article-card-data">
          <a *ngIf="data?.author?.name" [routerLink]="buildAuthorUrl(data)" class="article-card-author" [attr.aria-label]="data?.author?.name">
            <strong class="article-card-author-name">
              {{ data?.author?.name }}
            </strong>
          </a>
          <ng-container *ngTemplateOutlet="firstTagTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="badgesTemplate"></ng-container>
        </div>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.FeaturedRightImgDateTagTitle">
      <div [class.highlighted]="showHighlightedView" class="article-card-link">
        <a
          [routerLink]="data?.isPodcastType && !data?.contentType ? ['/podcastok', data.slug] : buildUrlByContentType(data)"
          class="article-card-date"
          [attr.aria-label]="data?.title"
        >
          <ng-container *ngIf="showYear">
            <span>{{ publishDate | dfnsFormat: 'yyyy' }}</span>
            <div class="article-card-separator"></div>
          </ng-container>

          <span>{{ publishDate | dfnsFormat: 'LLLL d.' | titlecase }}</span>
          <div class="article-card-separator"></div>
          <span>{{ publishDate | dfnsFormat: 'EEEE p' }}</span>
        </a>

        <div class="article-card-data">
          <div class="article-card-row">
            <ng-container *ngTemplateOutlet="firstTagTemplate"></ng-container>

            <a
              [routerLink]="data?.isPodcastType && !data?.contentType ? ['/podcastok', data.slug] : buildUrlByContentType(data)"
              [attr.aria-label]="data?.title"
            >
              <h2 class="article-card-title">{{ data?.title }}</h2>
            </a>

            <ng-container *ngIf="displayLead">
              <a
                [routerLink]="data?.isPodcastType && !data?.contentType ? ['/podcastok', data.slug] : buildUrlByContentType(data)"
                [attr.aria-label]="data?.title"
              >
                <div *ngIf="!hideLead && !showLeadInsteadOfExcerpt && data?.excerpt" class="article-card-lead">
                  {{ data?.excerpt }}
                </div>
                <div *ngIf="!hideLead && showLeadInsteadOfExcerpt && data?.lead" class="article-card-lead">{{ data?.lead }}</div>
              </a>
            </ng-container>

            <ng-container *ngTemplateOutlet="badgesTemplate"></ng-container>
            <ng-container *ngTemplateOutlet="publicAuthorTemplate"></ng-container>
          </div>

          @if (data?.isPodcastType) {
            <a
              [routerLink]="data?.isPodcastType && !data?.contentType ? ['/podcastok', data.slug] : buildUrlByContentType(data)"
              class="article-card-thumbnail-wrapper"
              *ngIf="showThumbnail"
              [attr.aria-label]="data?.title"
            >
              <ng-container
                *ngTemplateOutlet="
                  podcastArticleThumbnail;
                  context: {
                    isMobileSquare: true,
                    displayedAspectRatio: { desktop: isMobileView ? '1:1' : '16:9', mobile: showHighlightedView ? '16:9' : '1:1' },
                  }
                "
              >
              </ng-container>
            </a>
          } @else {
            <div class="article-card-thumbnail-wrapper" *ngIf="showThumbnail">
              <ng-container
                *ngTemplateOutlet="
                  thumbnailTemplate;
                  context: {
                    showCategoryName: true,
                    isLayer: true,
                    isMobileSquare: true,
                    displayedAspectRatio: { desktop: isMobileView ? '1:1' : '16:9', mobile: showHighlightedView ? '16:9' : '1:1' },
                  }
                "
              >
              </ng-container>
            </div>
          }
        </div>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.ColumnMain">
      <div class="article-card-link">
        <div class="article-card-data">
          <div class="article-card-row">
            <ng-container *ngTemplateOutlet="firstTagTemplate"></ng-container>
            <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
            <ng-container *ngTemplateOutlet="leadTemplate"></ng-container>
            <ng-container *ngTemplateOutlet="badgesTemplate"></ng-container>
            <a (click)="stopEventPropagation($event)" *ngIf="data?.author?.name" [routerLink]="buildAuthorUrl(data)" class="article-card-author">
              {{ data?.author?.name }}
            </a>
          </div>

          <div class="article-card-thumbnail-wrapper">
            @if (data?.isPodcastType) {
              <a [routerLink]="buildUrlByContentType(data)" [attr.aria-label]="data?.title">
                <ng-container *ngTemplateOutlet="podcastArticleThumbnail; context: { displayedAspectRatio: { desktop: '16:9' } }"></ng-container>
              </a>
            } @else {
              <div class="article-card-thumbnail-wrapper">
                <ng-container
                  *ngTemplateOutlet="
                    thumbnailTemplate;
                    context: {
                      showCategoryName: true,
                      isLayer: true,
                      displayedAspectRatio: { desktop: '16:9' },
                    }
                  "
                >
                </ng-container>
              </div>
            }
          </div>
        </div>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.MostRead">
      <div class="article-card-link">
        <div class="article-card-data">
          <ng-container *ngTemplateOutlet="firstTagTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="badgesTemplate"></ng-container>
        </div>

        <ng-container
          *ngTemplateOutlet="
            thumbnailTemplate;
            context: {
              isDesktopSquare: true,
              isMobileSquare: true,
              displayedAspectRatio: { desktop: '1:1' },
            }
          "
        >
        </ng-container>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.Podcast">
      <div [class.highlighted]="showHighlightedView" class="article-card-link">
        <div class="article-card-data">
          <a [routerLink]="buildUrlByContentType(data)" [attr.aria-label]="data?.title">
            <ng-container
              *ngTemplateOutlet="
                podcastArticleThumbnail;
                context: {
                  isMobileSquare: true,
                  displayedAspectRatio: { desktop: showHighlightedView ? '16:9' : '1:1' },
                }
              "
            >
            </ng-container>
          </a>
          <ng-container *ngTemplateOutlet="podcastArticleBody"></ng-container>
        </div>
        <ng-container *ngTemplateOutlet="guestTemplate; context: { showAvatarImg: false, showGuestLabel: true }"></ng-container>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.FreshNews">
      <div class="article-card-link">
        <div class="article-card-data">
          <a [routerLink]="buildUrlByContentType(data)" [attr.aria-label]="data?.title">
            <div class="article-card-publish-date font-bold-18">{{ publishDate | dfnsFormat: 'HH:mm' }}</div>
          </a>

          <div class="article-card-contents">
            <ng-container *ngTemplateOutlet="firstTagTemplate"></ng-container>
            <div *ngIf="!hideTag && displayedTag?.title" class="article-card-separator"></div>
            <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
          </div>
        </div>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.OpinionMain">
      <div class="article-card-link">
        <div class="article-card-data">
          <div class="article-card-tag">
            <ng-container *ngTemplateOutlet="firstTagTemplate"></ng-container>
          </div>
          <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="leadTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="publicAuthorTemplate"></ng-container>
        </div>

        <div>
          @if (data?.isPodcastType) {
            <a [routerLink]="buildUrlByContentType(data)" class="article-card-thumbnail-wrapper" [attr.aria-label]="data?.title">
              <ng-container *ngTemplateOutlet="podcastArticleThumbnail; context: { displayedAspectRatio: { desktop: '1:1', mobile: '16:9' } }"></ng-container>
            </a>
          } @else {
            <div [routerLink]="buildUrlByContentType(data)" class="article-card-thumbnail-wrapper">
              <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { displayedAspectRatio: { desktop: '1:1', mobile: '16:9' } }"></ng-container>
            </div>
          }
        </div>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.OpinionSecondary">
      <div class="article-card-link">
        <div class="article-card-row">
          <div class="article-card-data">
            <div class="article-card-data-meta-row">
              <a
                (click)="$event.stopPropagation()"
                *ngIf="data?.columnTitle && data?.columnEmphasizeOnArticleCard"
                [routerLink]="['/rovat', data?.columnSlug]"
                class="article-card-column"
              >
                {{ data?.columnTitle }}
              </a>
              <ng-container *ngTemplateOutlet="firstTagTemplate"></ng-container>
            </div>
            <ng-container *ngTemplateOutlet="titleTemplate; context: { limit: 120 }" />
          </div>

          @if (data?.isPodcastType) {
            <a [routerLink]="buildUrlByContentType(data)" class="article-card-thumbnail-wrapper" [attr.aria-label]="data?.title">
              <ng-container *ngTemplateOutlet="podcastArticleThumbnail; context: { displayedAspectRatio: { desktop: '1:1' } }"></ng-container>
            </a>
          } @else {
            <div class="article-card-thumbnail-wrapper">
              <ng-container *ngTemplateOutlet="thumbnailTemplate; context: { displayedAspectRatio: { desktop: '1:1' } }"></ng-container>
            </div>
          }
        </div>
        <ng-container *ngTemplateOutlet="leadTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="badgesTemplate"></ng-container>
        <ng-container *ngTemplateOutlet="publicAuthorTemplate"></ng-container>
      </div>
    </ng-container>

    <ng-container *ngSwitchCase="ArticleCardType.PodcastLight">
      <div class="article-card-link">
        <div class="article-card-data">
          <a [routerLink]="buildUrlByContentType(data)" [attr.aria-label]="data?.title">
            <ng-container *ngTemplateOutlet="podcastArticleThumbnail; context: { displayedAspectRatio: { desktop: '16:9' } }"></ng-container>
          </a>
          <ng-container *ngTemplateOutlet="firstTagTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
          <ng-container *ngTemplateOutlet="guestTemplate; context: { showAvatarImg: true, showGuestLabel: false }"></ng-container>
        </div>
      </div>
    </ng-container>
  </article>
</ng-container>

<ng-template #featuredImgAbsoluteTagTitle4Per3Template>
  <div class="article-card-link">
    @if (data?.isPodcastType) {
      <a [routerLink]="buildUrlByContentType(data)" class="article-card-thumbnail-wrapper" [attr.aria-label]="data?.title">
        <ng-container *ngTemplateOutlet="podcastArticleThumbnail; context: { displayedAspectRatio: { desktop: '4:3' } }"></ng-container>
      </a>
    } @else {
      <div [routerLink]="buildUrlByContentType(data)" class="article-card-thumbnail-wrapper">
        <ng-container
          *ngTemplateOutlet="
            thumbnailTemplate;
            context: {
              customTemplate: contentTemplate,
              displayedAspectRatio: { desktop: '4:3' },
            }
          "
        >
        </ng-container>
      </div>
    }

    <ng-template #contentTemplate>
      <div class="article-card-data">
        <ng-container *ngTemplateOutlet="firstTagTemplate"></ng-container>
        <div *ngIf="hasSeparator" class="article-card-separator"></div>
        <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
        <div class="article-card-row">
          <ng-container *ngTemplateOutlet="badgesTemplate"></ng-container>
          <a [routerLink]="buildUrlByContentType(data)" class="article-card-icon" [attr.aria-label]="data?.title">
            <span class="read-more">Olvasd tovább</span>
            <kesma-icon name="vg-chevron-diagonal-right"></kesma-icon>
          </a>
        </div>
      </div>
    </ng-template>
  </div>
</ng-template>

<ng-template #leadTemplate let-customClass="customClass">
  <a [routerLink]="buildUrlByContentType(data)" [attr.aria-label]="data?.title">
    <div *ngIf="!hideLead && !showLeadInsteadOfExcerpt && data?.excerpt" [class]="customClass" class="article-card-lead">
      {{ data?.excerpt }}
    </div>
    <div *ngIf="!hideLead && showLeadInsteadOfExcerpt && data?.lead" [class]="customClass" class="article-card-lead">{{ data?.lead }}</div>
  </a>
</ng-template>

<ng-template #badgesTemplate let-customClass="customClass">
  <ng-container *ngIf="badges(data)?.hasBadges">
    <div *ngIf="!hideBadges && badges(data)?.items as badge" [class]="customClass" class="article-card-badges">
      <kesma-icon *ngIf="badge?.isAdultsOnly" [size]="20" class="red" name="vg-adult"></kesma-icon>
      <ng-container *ngIf="badge?.isVideoType">
        <kesma-icon [size]="20" class="purple" name="vg-video"></kesma-icon>
        <span class="badge-title purple">Videó</span>
      </ng-container>
      <ng-container *ngIf="badge?.isPodcastType">
        <kesma-icon [size]="20" class="purple" name="vg-podcast"></kesma-icon>
        <span class="badge-title purple">Podcast</span>
      </ng-container>
      <ng-container *ngIf="badge?.hasGallery">
        <kesma-icon [size]="20" class="purple" name="vg-gallery"></kesma-icon>
        <span class="badge-title purple">Galéria</span>
      </ng-container>
    </div>
  </ng-container>
</ng-template>

<ng-template #publicAuthorTemplate let-showSeparator="showSeparator">
  <a
    (click)="stopEventPropagation($event)"
    *ngIf="!hideAuthor && data && data?.author?.name"
    [routerLink]="buildAuthorUrl(data)"
    class="article-card-author"
    [attr.aria-label]="data?.author?.name"
  >
    <img alt="" [src]="data?.author?.avatarUrl || placeholderAvatarUrl" class="article-card-author-avatar" loading="lazy" />
    <div class="article-card-author-details">
      <strong class="article-card-author-name">
        {{ data?.author?.name }}
      </strong>
      <div *ngIf="showSeparator" class="article-card-separator"></div>
      <div *ngIf="data?.author?.rank" class="article-card-author-rank">
        {{ data?.author?.rank }}
      </div>
    </div>
  </a>
</ng-template>

<ng-template #categoryTemplate>
  <a *ngIf="data?.columnEmphasizeOnArticleCard" [routerLink]="['/', 'rovat', data?.category?.slug || data?.columnSlug]" class="article-card-category">
    {{ data?.category?.name || data?.columnTitle }}
  </a>
</ng-template>

<ng-template
  #thumbnailTemplate
  let-customTemplate="customTemplate"
  let-hasOuterLink="hasOuterLink"
  let-isDesktopSquare="isDesktopSquare"
  let-isLayer="isLayer"
  let-isMobileSquare="isMobileSquare"
  let-showCategoryName="showCategoryName"
  let-displayedAspectRatio="displayedAspectRatio"
>
  <div class="article-card-thumbnail-card">
    @if (hasOuterLink) {
      <a [href]="data?.url" target="_blank" [attr.aria-label]="data?.title">
        <img
          [alt]="displayedThumbnailAlt"
          [src]="(isDesktopSquare && !smallView) || (isMobileSquare && smallView) ? displayedThumbnailUrlSquare : displayedThumbnailUrl"
          class="article-card-thumbnail"
          loading="lazy"
        />
      </a>
      <a
        *ngIf="showCategoryName && data?.columnEmphasizeOnArticleCard"
        [class.layer]="isLayer"
        [href]="data?.url"
        target="_blank"
        class="article-card-thumbnail-meta"
        clickStopPropagation
      >
        {{ data?.category?.name || data?.columnTitle }}
      </a>
    } @else {
      <a [routerLink]="buildUrlByContentType(data)" [attr.aria-label]="data?.title">
        <img
          withFocusPoint
          [data]="data?.thumbnailFocusedImages"
          [displayedAspectRatio]="displayedAspectRatio"
          [displayedUrl]="(isDesktopSquare && !smallView) || (isMobileSquare && smallView) ? displayedThumbnailUrlSquare : displayedThumbnailUrl"
          [alt]="displayedThumbnailAlt"
          class="article-card-thumbnail"
          loading="lazy"
        />
      </a>
      <a
        *ngIf="showCategoryName && data?.columnEmphasizeOnArticleCard"
        [class.layer]="isLayer"
        [routerLink]="['/', 'rovat', data?.category?.slug || data?.columnSlug]"
        class="article-card-thumbnail-meta"
        clickStopPropagation
      >
        {{ data?.category?.name || data?.columnTitle }}
      </a>
    }

    <div *ngIf="customTemplate" [class.layer]="isLayer" class="article-card-thumbnail-meta">
      <ng-container *ngTemplateOutlet="customTemplate"></ng-container>
    </div>

    <a *ngIf="index" [routerLink]="buildUrlByContentType(data)" [attr.aria-label]="data?.title">
      <div class="article-card-thumbnail-index font-bold-32">{{ index }}</div>
    </a>

    <kesma-icon *ngIf="displayLinkIcon" [hasTransition]="true" [size]="24" class="arrow-icon" name="vg-chevron-diagonal-right"></kesma-icon>
  </div>
</ng-template>

<ng-template #firstTagTemplate>
  <ng-container *ngIf="data && data?.foundationTagTitle; else noFoundationTag">
    <a
      (click)="$event.stopPropagation()"
      *ngIf="!hideTag"
      [routerLink]="['/', 'cimke', data?.foundationTagSlug]"
      class="article-card-tag"
      [attr.aria-label]="data?.foundationTagTitle"
    >
      <kesma-icon *ngIf="data?.isOpinion || isOpinion" [size]="16" name="vg-quote"></kesma-icon>
      {{ data?.foundationTagTitle }}
    </a>
  </ng-container>
</ng-template>

<ng-template #noFoundationTag>
  <a
    (click)="$event.stopPropagation()"
    *ngIf="!hideTag && data && displayedTag?.title"
    [routerLink]="buildTagUrl(data, displayedTagIndex)"
    class="article-card-tag"
    [attr.aria-label]="displayedTag?.title"
  >
    <kesma-icon *ngIf="data?.isOpinion || isOpinion" [size]="16" name="vg-quote"></kesma-icon>
    {{ displayedTag?.title }}
  </a>
</ng-template>

<ng-template #titleTemplate let-disableHover="disableHover" let-limit="limit">
  <a [routerLink]="buildUrlByContentType(data)" [attr.aria-label]="data?.title">
    <h2 [class.disable-hover]="disableHover" class="article-card-title">{{ !limit ? data?.title : (data?.title | limitString: limit) }}</h2>
  </a>
</ng-template>

<ng-template #podcastArticleBody>
  <div class="article-card-body">
    <ng-container *ngTemplateOutlet="firstTagTemplate"></ng-container>
    <ng-container *ngTemplateOutlet="titleTemplate"></ng-container>
  </div>
</ng-template>

<ng-template #podcastArticleThumbnail let-isDesktopSquare="isDesktopSquare" let-isMobileSquare="isMobileSquare" let-displayedAspectRatio="displayedAspectRatio">
  <div class="article-card-podcast-thumbnail">
    <img
      withFocusPoint
      [data]="data?.thumbnailFocusedImages"
      [displayedAspectRatio]="displayedAspectRatio"
      [displayedUrl]="(!isDesktopSquare && !smallView) || (!isMobileSquare && smallView) ? displayedThumbnailUrl : displayedThumbnailUrlSquare"
      [alt]="displayedThumbnailAlt"
      class="article-card-thumbnail"
      [class.isPlaceHolder]="isPlaceHolderImg"
      loading="lazy"
    />
  </div>
</ng-template>

<ng-template #guestTemplate let-showAvatarImg="showAvatarImg" let-showGuestLabel="showGuestLabel">
  <div class="article-card-guest">
    <div class="article-card-guest-datas">
      <div *ngIf="data?.podcastGuestName" class="article-card-guest-datas-name">
        <ng-container *ngIf="showGuestLabel">Vendég:</ng-container>
        {{ data?.podcastGuestName }}
        <ng-container *ngIf="showGuestLabel && data?.podcastGuestNameTitle">,</ng-container>
      </div>
      <div *ngIf="data?.podcastGuestNameTitle" class="article-card-guest-datas-title">{{ data?.podcastGuestNameTitle }}</div>
    </div>
    <img
      *ngIf="showAvatarImg"
      [alt]="data?.podcastGuestName || ''"
      [src]="data?.podcastGuestAvatarThumbnailUrl || placeholderAvatarUrl"
      class="article-card-guest-avatar"
      loading="lazy"
    />
  </div>
</ng-template>
