import { ChangeDetectionStrategy, Component, HostBinding, inject, Input, OnInit } from '@angular/core';
import {
  ArticleCard,
  BaseComponent,
  buildAuthorUrl,
  buildTagUrl,
  ClickStopPropagationDirective,
  FocusPointDirective,
  IconComponent,
  LimitStringPipe,
  Tag,
} from '@trendency/kesma-ui';
import { ArticleCardType } from '../../definitions';
import { buildUrlByContentType, getArticleBadges } from '../../utils';
import { backendDateToDate, UtilService } from '@trendency/kesma-core';
import { DateFnsModule } from 'ngx-date-fns';
import { RouterLink } from '@angular/router';
import { NgIf, NgSwitch, NgSwitchCase, NgTemplateOutlet, TitleCasePipe } from '@angular/common';

@Component({
  selector: 'vg-article-card',
  templateUrl: 'vg-article-card.component.html',
  styleUrls: ['vg-article-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgIf,
    NgSwitch,
    NgSwitchCase,
    RouterLink,
    NgTemplateOutlet,
    FocusPointDirective,
    TitleCasePipe,
    ClickStopPropagationDirective,
    DateFnsModule,
    IconComponent,
    LimitStringPipe,
  ],
})
export class VgArticleCardComponent extends BaseComponent<ArticleCard> implements OnInit {
  private readonly utils = inject(UtilService);
  @HostBinding('class') hostClass?: string;

  @Input() hasOutline: boolean = false;
  @Input() hideLead: boolean = false;
  @Input() hideBadges: boolean = false;
  @Input() hideTag: boolean = false;
  @Input() hideAuthor: boolean = false;
  @Input() @HostBinding('class.is-sidebar') isSidebar: boolean = false;
  @Input() hasSeparator: boolean = false; // Only for FeaturedImgAbsoluteTagTitle4Per3
  @Input() showHighlightedView: boolean = false; // Only for the list pages (FeaturedRightImgDateTagTitle)
  @Input() isLight: boolean = false;
  @Input() desktopWidth: number = 12;
  @Input() index: number = 0; // MostRead StyleID
  @Input() displayLead: boolean = false;
  @Input() displayLinkIcon: boolean = false; // RelatedArticle StyleID
  @Input() isOpinion: boolean = false;
  @Input() showYear: boolean = false;
  @Input() showThumbnail: boolean = true;
  @Input() showLeadInsteadOfExcerpt: boolean = false;
  @Input() isInPodcastBox: boolean = false;
  @Input() isPodcastBoxOnArticlePage: boolean = false;
  displayedThumbnailUrl?: string;
  displayedThumbnailUrlSquare?: string;
  displayedThumbnailAlt?: string;
  displayedTagIndex: number = 0;
  displayedTag?: Tag;
  publishDate: Date = new Date();
  isPlaceHolderImg?: boolean;
  isMobile = this.utils.isBrowser() ? window.innerWidth <= 768 : false;
  smallView = false;

  badges = getArticleBadges;
  readonly ArticleCardType = ArticleCardType;
  readonly buildUrlByContentType = buildUrlByContentType;
  readonly buildTagUrl = buildTagUrl;
  readonly buildAuthorUrl = buildAuthorUrl;
  readonly placeholderUrl = './assets/images/vg-placeholder-16-9.svg';
  readonly placeholderUrlSquare = './assets/images/vg-placeholder-1-1.svg';
  readonly placeholderAvatarUrl = './assets/images/vg-placeholder-avatar.svg';
  #styleID?: ArticleCardType;

  get styleID(): ArticleCardType {
    return this.#styleID as ArticleCardType;
  }

  get isMobileView(): boolean {
    return this.desktopWidth < 6 || this.isSidebar;
  }

  override ngOnInit(): void {
    super.ngOnInit();
    this.isMobile = this.utils.isBrowser() ? window.innerWidth <= 768 : false;
    this.smallView = this.isMobile || this.desktopWidth < 6 || this.isSidebar;
    this.hostClass += ` ${this.getPodcastTitleClass()}`;
  }

  @Input()
  set styleID(styleID: ArticleCardType) {
    this.hostClass = `article-card style-${ArticleCardType[styleID]}`;
    this.#styleID = styleID;
  }

  protected override setProperties(): void {
    this.displayedThumbnailUrl =
      this.data?.thumbnail?.url ||
      (typeof this.data?.thumbnail === 'string' && (this.data?.thumbnail as unknown as string)) ||
      (typeof this.data?.thumbnailUrl === 'string' && (this.data?.thumbnailUrl as unknown as string)) ||
      this.placeholderUrl;
    this.displayedThumbnailUrlSquare =
      this.data?.thumbnail?.url ||
      (typeof this.data?.thumbnail === 'string' && (this.data?.thumbnail as unknown as string)) ||
      (typeof this.data?.thumbnailUrl === 'string' && (this.data?.thumbnailUrl as unknown as string)) ||
      this.placeholderUrlSquare;
    this.isPlaceHolderImg =
      !this.data?.thumbnail?.url &&
      !(typeof this.data?.thumbnail === 'string' && (this.data?.thumbnail as unknown as string)) &&
      !(typeof this.data?.thumbnailUrl === 'string' && (this.data?.thumbnailUrl as unknown as string));
    this.displayedThumbnailAlt = this.data?.thumbnail?.alt || '';
    this.publishDate = this.data?.publishDate instanceof Date ? this.data?.publishDate : (backendDateToDate(this.data?.publishDate as string) as Date);

    const tags = this.data?.tags;
    this.displayedTag = tags?.find(({ id }) => id === this.data?.firstTagId) || tags?.[this.displayedTagIndex];

    if (this.displayedTag) {
      this.displayedTagIndex = tags?.indexOf(this.displayedTag) ?? this.displayedTagIndex;
    }
  }

  getPodcastTitleClass(): string {
    if (!this.isInPodcastBox || !this.data?.columnTitle) return '';
    if (this.data.columnTitle.length > 6 && this.data.columnTitle.length <= 10) {
      return 'column-title-length-m';
    }
    return 'column-title-length-l';
  }

  stopEventPropagation(event: Event): void {
    event.stopPropagation();
  }
}
