<div class="newsletter">
  <ng-container *ngIf="styleID === NewsletterBoxType.Basic">
    <div class="newsletter-text">Ne maradjon le a Világgazdaság legjobb írásairól, olvassa őket mindennap!</div>
    <a [routerLink]="'/hirlevel-feliratkozas'" class="newsletter-btn">Feliratkozom a hírlevélre</a>
  </ng-container>

  <ng-container *ngIf="styleID === NewsletterBoxType.SocialRight">
    <span class="title mobile">Ne maradjon le a Világgazdaság híreiről, olvassa őket mindennap!</span>

    <ul class="newsletter-list">
      <li class="newsletter-list-item">
        <kesma-icon name="vg-bullet"></kesma-icon>
        <div class="newsletter-list-item-inner">
          <span class="desktop-view">Ne maradjon le a Világgazdaság híreiről, olvassa őket mindennap!</span>
          <a [routerLink]="'/hirlevel-feliratkozas'" class="highlighted">Iratkozzon fel hírlevelünkre</a>
        </div>
        <div class="mobile" style="height: 24px; width: 24px">
          <ng-container *ngTemplateOutlet="email"></ng-container>
        </div>
      </li>
      <li class="newsletter-list-item">
        <kesma-icon name="vg-bullet"></kesma-icon>
        <div>
          Csatlakozzon hozzánk a közösségi médiában:
          <a [href]="socialLink.facebook" target="_blank" class="highlighted">Facebook,</a>
          <a [href]="socialLink.linkedin" target="_blank" class="highlighted"> LinkedIn</a>
          és
          <a [href]="socialLink.twitter" target="_blank" class="highlighted">Twitter</a>
        </div>
      </li>
      <li class="newsletter-list-item">
        <kesma-icon name="vg-bullet"></kesma-icon>
        <div>
          Kövesse csatornáinkat: <a [href]="socialLink.videa" target="_blank" class="highlighted">Videa,</a>
          <a [href]="socialLink.youtube" target="_blank" class="highlighted"> YouTube,</a>
          <a [href]="socialLink.spotify" target="_blank" class="highlighted"> Spotify</a>
          és
          <a [href]="socialLink.rss" target="_blank" class="highlighted">RSS</a>
        </div>
      </li>
    </ul>

    <div class="newsletter-social desktop-view">
      <div class="wrapper">
        <div class="desktop-view">
          <ng-container *ngTemplateOutlet="email"></ng-container>
        </div>
        <ng-container *ngTemplateOutlet="facebook"></ng-container>
        <ng-container *ngTemplateOutlet="linkedin"></ng-container>
        <ng-container *ngTemplateOutlet="twitter"></ng-container>
      </div>

      <div class="wrapper">
        <ng-container *ngTemplateOutlet="videa"></ng-container>
        <ng-container *ngTemplateOutlet="youtube"></ng-container>
        <ng-container *ngTemplateOutlet="spotify"></ng-container>
        <ng-container *ngTemplateOutlet="rss"></ng-container>
      </div>
    </div>

    <div class="newsletter-social mobile">
      <ng-container *ngTemplateOutlet="facebook"></ng-container>
      <ng-container *ngTemplateOutlet="linkedin"></ng-container>
      <ng-container *ngTemplateOutlet="twitter"></ng-container>
      <ng-container *ngTemplateOutlet="videa"></ng-container>
      <ng-container *ngTemplateOutlet="youtube"></ng-container>
      <ng-container *ngTemplateOutlet="spotify"></ng-container>
      <ng-container *ngTemplateOutlet="rss"></ng-container>
    </div>
  </ng-container>

  <ng-container *ngIf="styleID === NewsletterBoxType.SocialSorted">
    <div class="title">Ne maradjon le a Világgazdaság híreiről, olvassa őket mindennap!</div>

    <ul class="newsletter-list">
      <li class="newsletter-list-item">
        <div class="inner">
          <kesma-icon name="vg-bullet"></kesma-icon>
          <div class="inner-content">
            <a [routerLink]="'/hirlevel-feliratkozas'" class="highlighted">Iratkozzon fel hírlevelünkre</a>
            <span class="mobile">
              <ng-container *ngTemplateOutlet="email"></ng-container>
            </span>
          </div>
        </div>

        <div class="desktop-view">
          <ng-container *ngTemplateOutlet="email"></ng-container>
        </div>
      </li>

      <li class="newsletter-list-item">
        <div class="inner">
          <kesma-icon name="vg-bullet"></kesma-icon>
          <div>
            Csatlakozzon hozzánk a közösségi médiában:
            <a [href]="socialLink.facebook" target="_blank" class="highlighted">Facebook,</a>
            <a [href]="socialLink.linkedin" target="_blank" class="highlighted"> LinkedIn</a>
            és
            <a [href]="socialLink.twitter" target="_blank" class="highlighted">Twitter</a>
          </div>
        </div>

        <div class="desktop-view">
          <ng-container *ngTemplateOutlet="facebook"></ng-container>
          <ng-container *ngTemplateOutlet="linkedin"></ng-container>
          <ng-container *ngTemplateOutlet="twitter"></ng-container>
        </div>
      </li>

      <li class="newsletter-list-item">
        <div class="inner">
          <kesma-icon name="vg-bullet"></kesma-icon>
          <div>
            Kövesse csatornáinkat: <a [href]="socialLink.videa" target="_blank" class="highlighted">Videa,</a>
            <a [href]="socialLink.youtube" target="_blank" class="highlighted"> YouTube,</a>
            <a [href]="socialLink.spotify" target="_blank" class="highlighted"> Spotify</a>
            és
            <a [href]="socialLink.rss" target="_blank" class="highlighted">RSS</a>
          </div>
        </div>

        <div class="desktop-view">
          <ng-container *ngTemplateOutlet="videa"></ng-container>
          <ng-container *ngTemplateOutlet="youtube"></ng-container>
          <ng-container *ngTemplateOutlet="spotify"></ng-container>
          <ng-container *ngTemplateOutlet="rss"></ng-container>
        </div>
      </li>
    </ul>

    <div class="newsletter-social mobile">
      <ng-container *ngTemplateOutlet="facebook"></ng-container>
      <ng-container *ngTemplateOutlet="linkedin"></ng-container>
      <ng-container *ngTemplateOutlet="twitter"></ng-container>
      <ng-container *ngTemplateOutlet="videa"></ng-container>
      <ng-container *ngTemplateOutlet="youtube"></ng-container>
      <ng-container *ngTemplateOutlet="spotify"></ng-container>
      <ng-container *ngTemplateOutlet="rss"></ng-container>
    </div>
  </ng-container>

  <ng-container *ngIf="styleID === NewsletterBoxType.Gong">
    <img src="/assets/images/vg-gong-email.svg" alt="Email ikon" class="icon" />
    <div class="newsletter-text">
      <span class="title">Gong előtt</span> - Heti befektetői összefoglaló: Szakértőink segítenek percek alatt képbe kerülni a gazdaság és a piacok trendjeiről,
      részletek
      <a class="highlighted" routerLink="/vilaggazdasag-magyar-gazdasag/2024/01/gong-elott-uj-befektetoi-hirlevelet-indit-a-vilaggazdasag"> itt. </a>
    </div>
    <a routerLink="/hirlevel-feliratkozas-2" class="newsletter-btn">Feliratkozom a hírlevélre</a>
  </ng-container>

  <ng-template #email>
    <a [href]="socialLink.email" class="icon-wrapper" aria-label="E-mail">
      <kesma-icon [hasTransition]="true" name="vg-email-circle" customHover="vg-email-circle-hover" [size]="32"></kesma-icon>
    </a>
  </ng-template>

  <ng-template #facebook>
    <a [href]="socialLink.facebook" target="_blank" class="icon-wrapper" aria-label="Facebook">
      <kesma-icon [hasTransition]="true" name="vg-facebook-circle-newsletter" class="facebook" [size]="32"></kesma-icon>
    </a>
  </ng-template>

  <ng-template #linkedin>
    <a [href]="socialLink.linkedin" target="_blank" class="icon-wrapper" aria-label="LinkedIn">
      <kesma-icon [hasTransition]="true" name="vg-linkedin-circle" customHover="vg-linkedin-circle-hover" [size]="32"></kesma-icon>
    </a>
  </ng-template>

  <ng-template #twitter>
    <a [href]="socialLink.twitter" target="_blank" class="icon-wrapper" aria-label="X">
      <kesma-icon [hasTransition]="true" name="vg-twitter-circle" customHover="vg-twitter-circle-hover" [size]="32"></kesma-icon>
    </a>
  </ng-template>

  <ng-template #videa>
    <a [href]="socialLink.videa" target="_blank" class="icon-wrapper" aria-label="Videa">
      <kesma-icon [hasTransition]="true" name="vg-videa-circle" customHover="vg-videa-circle-hover" [size]="32"></kesma-icon>
    </a>
  </ng-template>

  <ng-template #youtube>
    <a [href]="socialLink.youtube" target="_blank" class="icon-wrapper" aria-label="YouTube">
      <kesma-icon [hasTransition]="true" name="vg-youtube-circle" customHover="vg-youtube-circle-hover" [size]="32"></kesma-icon>
    </a>
  </ng-template>

  <ng-template #spotify>
    <a [href]="socialLink.spotify" target="_blank" class="icon-wrapper" aria-label="Spotify">
      <kesma-icon [hasTransition]="true" name="vg-spotify-circle" customHover="vg-spotify-circle-hover" [size]="32"></kesma-icon>
    </a>
  </ng-template>

  <ng-template #rss>
    <a [href]="socialLink.rss" target="_blank" class="icon-wrapper" aria-label="RSS">
      <kesma-icon [hasTransition]="true" name="vg-rss-circle" customHover="vg-rss-circle-hover" [size]="32"></kesma-icon>
    </a>
  </ng-template>
</div>
