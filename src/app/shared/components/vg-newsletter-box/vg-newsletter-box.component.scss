@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  .mobile {
    display: none;

    @include media-breakpoint-down(sm) {
      display: block;
    }
  }

  .desktop-view {
    @include media-breakpoint-down(sm) {
      display: none;
    }
  }

  .title {
    color: var(--kui-gray-950);
    font-size: 18px;
    font-weight: 700;
    line-height: normal;
    letter-spacing: 0.36px;

    @include media-breakpoint-down(sm) {
      font-size: 18px;
      line-height: 22px;
      letter-spacing: unset;
    }
  }

  kesma-icon {
    flex: 0 0 auto;
  }

  &.basic,
  &.gong {
    .newsletter {
      display: flex;
      width: 100%;
      align-items: stretch;
      background-color: var(--kui-deep-teal-900);
      outline: 1px solid var(--kui-deep-teal-600);
      border-radius: 2px;

      @include media-breakpoint-down(md) {
        flex-direction: column;
      }

      &-text {
        color: var(--kui-white);
        flex: 1 0 0;
        text-align: center;
        font-size: 18px;
        font-weight: 700;
        line-height: normal;
        letter-spacing: 0.4px;
        padding: 10px;

        @include media-breakpoint-down(md) {
          flex-direction: column;
          padding: 8px 16px;
          font-size: 16px;
        }
      }

      &-btn {
        padding: 16px 8px;
        font-size: 18px;
        font-weight: 700;
        line-height: normal;
        color: var(--kui-white);
        width: 312px;
        text-align: center;
        outline: 1px solid var(--kui-deep-teal-400);
        border-radius: 2px 0 0 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: 0.3s;

        &:hover {
          background: var(--kui-deep-teal-400);
        }

        @include media-breakpoint-down(md) {
          font-size: 16px;
          width: 100%;
        }
      }

      @include media-breakpoint-down(sm) {
        flex-direction: column;
      }
    }
  }

  &.social-right {
    @extend %newsletter-box;

    .newsletter {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 8px;

      &-list {
        display: flex;
        flex-direction: column;

        &-item {
          color: var(--kui-gray-950);
          line-height: 24px;
          display: flex;
          align-items: flex-start;
          gap: 8px;

          &-inner {
            .desktop-view {
              margin-right: 4px;
            }
          }

          .highlighted {
            color: var(--kui-deep-teal-600);
            font-weight: 700;
            line-height: 22px;
            padding: 0 2px;
            text-decoration: underline transparent solid 1px;
            text-underline-offset: 3px;
            transition: all 0.3s;

            &:hover {
              color: var(--kui-amethyst-500);
              text-decoration-color: var(--kui-amethyst-500);
            }
          }

          kesma-icon {
            color: var(--kui-deep-teal-400);
          }
        }

        @include media-breakpoint-down(sm) {
          gap: 16px;
        }
      }

      &-social {
        display: flex;
        column-gap: 24px;
        row-gap: 8px;
        flex-wrap: wrap;
        justify-content: flex-end;

        @include media-breakpoint-down(sm) {
          justify-content: center;
          margin: 0 auto;
        }

        .wrapper {
          display: flex;
          gap: 24px;
        }

        &.mobile {
          display: none;
        }
      }

      @include media-breakpoint-down(sm) {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
      }
    }
  }

  &.social-sorted {
    @extend %newsletter-box;

    .mobile {
      @include media-breakpoint-up(sm) {
        display: none !important;
      }
    }

    .desktop-view {
      display: flex;
      width: 176px;
      flex: 0 0 auto;
      max-height: 32px;
      gap: 16px;

      @include media-breakpoint-down(sm) {
        display: none !important;
      }
    }

    .title {
      margin-bottom: 16px;
    }

    .newsletter {
      &-list {
        display: flex;
        flex-direction: column;
        gap: 8px;

        &-item {
          color: var(--kui-gray-950);
          line-height: 24px;
          display: flex;
          gap: 8px;
          justify-content: space-between;
          align-items: center;

          .inner {
            display: flex;
            align-items: flex-start;
            gap: 8px;

            &-content {
              display: flex;
              align-items: center;
              gap: 8px;
            }
          }

          .highlighted {
            color: var(--kui-deep-teal-600);
            font-weight: 700;
            line-height: 22px;
            padding: 0 2px;
            text-decoration: underline transparent solid 1px;
            text-underline-offset: 3px;
            transition: all 0.3s;

            &:hover {
              color: var(--kui-amethyst-500);
              text-decoration-color: var(--kui-amethyst-500);
            }
          }

          kesma-icon {
            color: var(--kui-deep-teal-400);

            @include media-breakpoint-down(sm) {
              width: 24px;
              height: 24px;
            }
          }
        }
      }

      &-social {
        margin-top: 16px;
        display: flex;
        justify-content: center;
        gap: 24px;
      }
    }
  }

  &.gong {
    .icon {
      padding-left: 24px;

      @include media-breakpoint-down(sm) {
        order: 2;
        padding-left: 0;
        padding-right: 16px;
        width: 52.785px;
      }
    }

    .newsletter {
      @include media-breakpoint-down(md) {
        flex-direction: row;
        flex-wrap: wrap;
        gap: 4px;
      }

      &-text {
        padding: 20px 24px;
        font-size: 18px;
        font-style: normal;
        line-height: 24px;
        text-align: left;
        font-weight: 400;

        .title,
        .highlighted {
          font-style: normal;
          font-weight: 700;
          line-height: normal;
          letter-spacing: 0.36px;
          color: var(--kui-white);
        }

        .highlighted {
          color: var(--kui-deep-teal-400);
          transition: 0.3s;

          &:hover {
            color: var(--kui-amethyst-500);
            text-decoration: underline;
          }
        }

        @include media-breakpoint-down(sm) {
          order: 1;
          padding: 8px 4px 8px 16px;

          &,
          .title {
            font-size: 16px;
            line-height: 22px;
          }
        }
      }

      &-btn {
        color: var(--kui-white);

        @include media-breakpoint-down(sm) {
          order: 3;
          padding: 16px 8px;
        }
      }
    }
  }

  &.list-page-view {
    &.basic {
      .newsletter {
        &-btn,
        &-text {
          font-size: 14px;
        }

        &-text {
          @include media-breakpoint-up(sm) {
            padding: 8px 8px 8px 16px;
          }
        }

        &-btn {
          padding: 8px;

          @include media-breakpoint-up(md) {
            width: 200px;
          }
        }
      }
    }

    &.gong {
      .newsletter {
        .title,
        &-btn,
        &-text {
          font-size: 14px;
          line-height: 18px;

          @include media-breakpoint-down(sm) {
            font-size: 16px;
            line-height: 24px;
          }
        }

        &-text {
          @include media-breakpoint-up(sm) {
            padding: 16px;
            margin-right: 8px;
          }
        }

        &-btn {
          padding: 16px 8px;

          @include media-breakpoint-up(sm) {
            padding: 34px 8px;
            width: 200px;
          }
        }
      }

      .icon {
        @include media-breakpoint-up(sm) {
          padding-left: 16px;
          padding-right: 8px;
        }
      }
    }
  }

  &.is-sidebar {
    &.basic,
    &.gong {
      .newsletter {
        flex-direction: column;

        &-text {
          flex-direction: column;
          padding: 8px 16px;
        }

        &-btn {
          width: 100%;
          padding: 16px 8px;
          color: var(--kui-deep-teal-400);

          &:hover {
            color: var(--kui-white);
          }
        }

        &-text,
        &-btn {
          font-size: 16px;
          line-height: 22px;
        }
      }
    }

    &.gong {
      .icon {
        order: 2;
        padding-left: 0;
        padding-right: 16px;
        width: 52.785px;
      }

      .newsletter {
        flex-direction: row;
        column-gap: 4px;
        flex-wrap: wrap;

        .title {
          font-size: 16px;
          line-height: 22px;
        }

        &-text {
          order: 1;
          font-weight: 400;
          line-height: 24px;
          padding: 8px 0 8px 16px;
        }

        &-btn {
          order: 3;
          padding: 16px 8px;
        }
      }
    }

    &.social-right,
    &.social-sorted {
      .newsletter {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;

        &-list {
          gap: 16px;

          &-item {
            line-height: 22px;

            &:first-of-type {
              height: 22px;
            }

            kesma-icon {
              width: 24px;
            }
          }
        }

        .title {
          margin-bottom: 0;
          font-size: 16px;
          line-height: 22px;
        }

        &-social {
          justify-content: space-between;
          max-width: 280px;
          margin: 0 auto;
          width: 100%;
          gap: 10px;

          .icon-wrapper {
            width: 24px;
          }
        }
      }

      .desktop-view {
        display: none;
      }

      .mobile {
        display: flex !important;
      }
    }
  }

  &.full-width {
    .newsletter {
      align-items: center;
    }
  }
}

.facebook {
  color: var(--kui-deep-teal-950) !important;

  &:hover {
    color: var(--kui-facebook-color) !important;
  }
}

%newsletter-box {
  outline: 1px solid var(--kui-deep-teal-400);
  border-radius: 2px;
  padding: 16px;
}
