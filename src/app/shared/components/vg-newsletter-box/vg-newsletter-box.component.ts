import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { NewsletterBoxType } from '../../definitions';
import { RouterLink } from '@angular/router';
import { NgIf, NgTemplateOutlet } from '@angular/common';
import { IconComponent } from '@trendency/kesma-ui';

@Component({
  selector: 'vg-newsletter-box',
  templateUrl: 'vg-newsletter-box.component.html',
  styleUrls: ['vg-newsletter-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, NgTemplateOutlet, IconComponent],
})
export class VgNewsletterBoxComponent {
  @Input() @HostBinding('class') styleID: NewsletterBoxType = NewsletterBoxType.Basic;
  @Input() @HostBinding('class.list-page-view') isListPageView: boolean = false;
  @Input() @HostBinding('class.is-sidebar') isSidebar: boolean = false;
  @Input() @HostBinding('class.full-width') isFullWidth: boolean = false;

  readonly NewsletterBoxType = NewsletterBoxType;

  socialLink = {
    email: 'mailto:<EMAIL>',
    facebook: 'https://www.facebook.com/Vilaggazdasag',
    linkedin: 'https://hu.linkedin.com/company/vilaggazdasag',
    twitter: 'https://twitter.com/vg_magyarorszag',
    videa: 'https://videa.hu/csatornak/vilaggazdasag-385',
    youtube: 'https://www.youtube.com/@VilagGazdasag_hivatalos',
    spotify: 'https://open.spotify.com/show/1uwXpewEwfqCZ5NigKWTIl',
    rss: 'https://www.vg.hu/publicapi/hu/rss/vilaggazdasag/articles',
  };
}
