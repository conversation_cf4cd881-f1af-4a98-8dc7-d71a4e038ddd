import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { UtilService } from '@trendency/kesma-core';
import { IconComponent, SimplifiedMenuItem } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { NgFor, NgIf } from '@angular/common';
import { SocialBoxComponent } from '../social-box/social-box.component';

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [SocialBoxComponent, NgFor, NgIf, RouterLink, IconComponent],
})
export class FooterComponent implements OnInit {
  @Input() topMenu: SimplifiedMenuItem[] = [];
  @Input() bottomMenu: SimplifiedMenuItem[] = [];
  @Output() openCookieSettings = new EventEmitter<void>();
  year: number;

  constructor(private readonly utilsService: UtilService) {}

  ngOnInit(): void {
    if (this.utilsService.isBrowser()) {
      this.year = new Date().getFullYear();
    }
  }
}
