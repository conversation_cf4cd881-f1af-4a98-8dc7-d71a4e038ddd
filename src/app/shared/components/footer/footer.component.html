<footer class="footer">
  <div class="footer-top">
    <div class="footer-top-container">
      <div class="footer-top-wrapper">
        <app-social-box></app-social-box>
        <ul class="footer-top-menu">
          <li *ngFor="let item of topMenu">
            <a *ngIf="!item.isCustomUrl" [routerLink]="item.link" class="footer-top-menu-item">{{ item.title }}</a>
            <a *ngIf="item.isCustomUrl" [href]="item.link" [target]="item.target" class="footer-top-menu-item">{{ item.title }}</a>
          </li>
          <li>
            <a class="footer-newsletter-signup" routerLink="/hirlevel-feliratkozas">
              <kesma-icon name="vg-email"></kesma-icon>
              <span>Hírlevél</span>
            </a>
          </li>
        </ul>
        <div class="footer-top-divider"></div>
      </div>
      <ul class="footer-top-static">
        <li class="footer-bottom-menu" *ngFor="let item of bottomMenu">
          <a *ngIf="!item.isCustomUrl" [routerLink]="item.link" class="footer-bottom-menu-item">{{ item.title }}</a>
          <a *ngIf="item.isCustomUrl" [href]="item.link" [target]="item.target" class="footer-bottom-menu-item">{{ item.title }}</a>
          <div class="footer-bottom-menu-separator"></div>
        </li>
        <li class="footer-bottom-menu">
          <button (click)="openCookieSettings.emit()" class="footer-bottom-menu-item">Süti beállítások</button>
        </li>
      </ul>
    </div>
  </div>
  <div class="footer-bottom">
    <div class="footer-bottom-wrapper">
      <div class="footer-bottom-copyright">Az oldal kiadója a Mediaworks Hungary Zrt. Minden jog fenntartva</div>
      <div class="divider"></div>
      <div class="footer-bottom-icon">
        <span>© {{ year }}</span>
        <a routerLink="/">
          <div class="footer-logo">VILÁGGAZDASÁG</div>
        </a>
      </div>
    </div>
  </div>
</footer>
