@use 'shared' as *;

.footer {
  width: 100%;

  &-top {
    background-color: var(--kui-deep-teal-950);
    display: flex;
    justify-content: center;
    padding: 0 24px;

    &-container {
      width: 100%;
      display: flex;
      flex-direction: column;
      max-width: 1068px;
    }

    &-wrapper {
      display: flex;
      padding: 24px 0;
      gap: 80px;
      align-items: center;

      @include media-breakpoint-down(sm) {
        flex-direction: column;
        gap: 0;
        padding-bottom: 0;
      }

      @include media-breakpoint-down(xs) {
        align-items: flex-start;
      }
    }

    &-menu {
      width: 100%;
      display: grid;
      height: fit-content;
      grid-template-columns: repeat(4, auto);
      grid-row-gap: 16px;
      column-gap: 80px;

      @include media-breakpoint-down(lg) {
        grid-template-columns: repeat(3, auto);
        column-gap: 40px;
      }

      @include media-breakpoint-down(md) {
        grid-template-columns: repeat(2, auto);
        column-gap: 20px;
      }

      @include media-breakpoint-down(sm) {
        padding: 16px 24px;
        column-gap: 0;
      }

      &-item {
        line-height: 21px;
        font-size: 14px;
        font-weight: 700;
        color: var(--kui-white);
        cursor: pointer;
        transition: all 0.3s;
        text-decoration: underline transparent solid 1px;
        text-underline-offset: 5px;

        &:hover {
          text-decoration-color: var(--kui-deep-teal-400);
        }
      }
    }

    &-static {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
      gap: 20px;
      padding: 16px 0;

      @include media-breakpoint-down(sm) {
        padding: 8px 0 48px 24px;
        flex-direction: column;
        gap: 16px;
      }
    }

    &-divider {
      height: 2px;
      width: 100%;
      border-radius: 20px;
      background-color: var(--kui-deep-teal-900);

      @include media-breakpoint-up(md) {
        display: none;
      }
    }
  }

  &-bottom {
    background-color: var(--kui-gray-950);
    display: flex;
    justify-content: center;
    padding: 16px 0;

    @include media-breakpoint-down(sm) {
      padding: 8px 24px;
    }

    &-wrapper {
      display: flex;
      gap: 16px;
      align-items: center;

      @include media-breakpoint-down(sm) {
        flex-direction: column;
        gap: 9px;
      }

      .divider {
        background-color: var(--kui-gray-900);
        width: 2px;
        height: 20px;
        border-radius: 10px;

        @include media-breakpoint-down(sm) {
          width: 100%;
          height: 2px;
          border-radius: 20px;
        }
      }
    }

    &-copyright {
      line-height: 24px;
      font-size: 16px;
      font-weight: 400;
      color: var(--kui-white);

      @include media-breakpoint-down(sm) {
        text-align: center;
      }
    }

    &-icon {
      line-height: 22px;
      font-size: 16px;
      font-weight: 700;
      color: var(--kui-white);
      display: flex;
      align-items: center;
      gap: 8px;
    }

    &-menu {
      display: flex;
      justify-content: center;
      gap: 20px;

      @include media-breakpoint-down(sm) {
        justify-content: flex-start;
      }

      &-item {
        line-height: 18px;
        font-size: 14px;
        font-weight: 400;
        color: var(--kui-white);
        cursor: pointer;
        transition: all 0.3s;
        text-decoration: underline transparent solid 1px;
        text-underline-offset: 5px;

        &:hover {
          text-decoration-color: var(--kui-deep-teal-400);
        }
      }

      &-separator {
        width: 2px;
        height: 20px;
        border-radius: 10px;
        background-color: var(--kui-deep-teal-900);

        @include media-breakpoint-down(sm) {
          display: none;
        }
      }
    }
  }

  &-logo {
    line-height: 36px;
    font-size: 16px;
    font-weight: 700;
    color: var(--kui-deep-teal-400);
  }

  &-newsletter-signup {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: all 0.3s;
    text-decoration: underline transparent solid 1px;
    text-underline-offset: 5px;

    &:hover {
      text-decoration-color: var(--kui-deep-teal-400);
    }

    kesma-icon {
      width: 17px;
      height: 17px;
      color: var(--kui-white);
    }

    span {
      line-height: 21px;
      font-size: 14px;
      font-weight: 700;
      color: var(--kui-white);
    }
  }

  ::ng-deep {
    .icon {
      width: 20px;
      height: 20px;
    }

    .icon-wrapper {
      position: relative;
      display: flex;
      flex: 0 0 auto;

      img,
      i {
        transition: 0.3s;
        &.hover {
          opacity: 0;
          position: absolute;

          &:hover {
            opacity: 1;
          }
        }
        &.basic {
          &:hover {
            opacity: 0;

            & + .hover {
              opacity: 1;
            }
          }
        }
      }
    }
  }
}
