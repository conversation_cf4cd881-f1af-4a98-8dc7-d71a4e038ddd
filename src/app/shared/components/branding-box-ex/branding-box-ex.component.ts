import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, inject, Input, OnInit } from '@angular/core';
import { Observable } from 'rxjs';
import { TrafficDeflectorService } from 'src/app/feature/layout/services/traffic-deflector.service';
import { VgBrandingBoxArticle, VgBrandingBoxBrand } from '../../definitions';
import { VgBrandingBoxComponent } from '../vg-branding-box/vg-branding-box.component';

@Component({
  selector: 'app-vg-branding-box-ex',
  imports: [AsyncPipe, VgBrandingBoxComponent],
  templateUrl: './branding-box-ex.component.html',
  styleUrl: './branding-box-ex.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VgBrandingBoxExComponent implements OnInit {
  @Input() articleLimit: number;
  data$: Observable<VgBrandingBoxArticle[] | undefined>;
  trafficDeflectorService = inject(TrafficDeflectorService);
  private _traffickingPlatform: string;

  private _brand: VgBrandingBoxBrand;

  get brand(): VgBrandingBoxBrand {
    return this._brand;
  }

  @Input() set brand(brand: VgBrandingBoxBrand) {
    switch (brand) {
      case 'figyelo':
        this._traffickingPlatform = 'Figyelő for VG';
        break;
      case 'magyarnemzet':
        this._traffickingPlatform = 'MNO for VG';
        break;
    }
    this._brand = brand;
  }

  get traffickingPlatforms(): string {
    return this._traffickingPlatform;
  }

  ngOnInit(): void {
    this.data$ = this.trafficDeflectorService.getTrafficDeflectorData(this.traffickingPlatforms, this.articleLimit);
  }
}
