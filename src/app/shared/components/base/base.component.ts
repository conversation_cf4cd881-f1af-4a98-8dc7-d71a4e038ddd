import { AsyncPipe, DOCUMENT, NgClass, NgIf } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnDestroy, OnInit, Optional } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router, RouterOutlet } from '@angular/router';
import { REQUEST, UtilService } from '@trendency/kesma-core';
import {
  Advertisement,
  AdvertisementAdoceanComponent,
  AdvertisementAdoceanStoreService,
  AdvertisementBannerName,
  AdvertisementsByMedium,
  ALL_BANNER_LIST,
  BreakingBlock,
  DossierData,
  InitResolverData,
  MediaworksFooterCompactComponent,
  PAGE_TYPES,
  PortfolioItem,
  PortfolioResponse,
  SimplifiedMenuItem,
  TrendingTag,
} from '@trendency/kesma-ui';
import { combineLatest, interval, Observable, of, Subject, take, takeUntil } from 'rxjs';
import { delayWhen, filter, map, mergeMap, startWith } from 'rxjs/operators';
import { ApiService } from '../../services';
import { isMobileApp } from '../../utils';
import { FooterComponent } from '../footer/footer.component';
import { HeaderComponent } from '../header/header.component';

declare let __tcfapi: (command: string, version?: number, callback?: (response: any, success: boolean) => void, param?: any) => void;

@Component({
  selector: 'app-base',
  templateUrl: './base.component.html',
  styleUrls: ['./base.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, HeaderComponent, NgClass, RouterOutlet, FooterComponent, AsyncPipe, AdvertisementAdoceanComponent, MediaworksFooterCompactComponent],
})
export class BaseComponent implements OnInit, OnDestroy, AfterViewInit {
  mainMenu: SimplifiedMenuItem[] = [];
  footer0: SimplifiedMenuItem[] = [];
  footer1: SimplifiedMenuItem[] = [];
  footer2: SimplifiedMenuItem[] = [];
  trendingTags: TrendingTag[] = [];
  dossiers: DossierData[] = [];

  breakingNews?: BreakingBlock;
  isArticleUrl: boolean;

  isAdblockerActive: boolean;

  mediaworksFooter: PortfolioItem[];

  adverts?: AdvertisementsByMedium;
  isHomePage?: boolean;

  areAdsInitiated = false;

  isFullWidth$ = this.router.events.pipe(
    filter((e) => e instanceof NavigationEnd),
    startWith(null),
    map(() => this.route.snapshot.firstChild?.data?.['isFullWidth'] === true)
  );

  isMobileApp: boolean = false;

  private readonly unsubscribe$: Subject<boolean> = new Subject();

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private readonly utils: UtilService,
    private readonly adStoreAdo: AdvertisementAdoceanStoreService,
    private readonly changeRef: ChangeDetectorRef,
    private readonly apiService: ApiService,
    @Inject(DOCUMENT) private readonly documentElement: Document,
    @Optional() @Inject(REQUEST) private readonly request: Request
  ) {
    if (this.utils.isBrowser()) {
      this.isMobileApp = isMobileApp(navigator.userAgent || navigator.vendor || (window as any).opera);
    } else {
      let userAgent: string | undefined = '';

      if (this.request?.headers) {
        userAgent = Object.entries(this.request.headers).find((value) => value?.[0] === 'user-agent')?.[1];
      }

      // SSR: use request headers
      this.isMobileApp = isMobileApp(userAgent || '');
    }
  }

  ngOnInit(): void {
    if (this.isMobileApp) {
      return;
    }

    combineLatest([this.router.events as Observable<any | NavigationEnd>, this.adStoreAdo.isAdult.asObservable()])
      .pipe(
        filter<[any | NavigationEnd, boolean]>(([event]) => event instanceof NavigationEnd),
        startWith([null, false]),
        mergeMap(() => {
          if (!this.utils.isBrowser() || !this.documentElement?.location) {
            return of({} as AdvertisementsByMedium);
          }

          const [_, path1, path2] = this.documentElement?.location?.pathname.split('/') ?? ['', ''];

          this.isArticleUrl = !isNaN(parseInt(path2, 10));
          this.resetAds();
          if (this.isArticleUrl) {
            this.adStoreAdo.currentMasterIdSubject.next('');
          }

          return this.adStoreAdo.advertisemenets$.pipe(
            map<Advertisement[], AdvertisementsByMedium>((ads) => {
              const headerMediumSeparator = this.baseElementPageTypeSwitch(path1);

              return this.adStoreAdo.separateAdsByMedium(ads, headerMediumSeparator.page, headerMediumSeparator.ads);
            }),
            delayWhen((ads) =>
              this.isArticleUrl
                ? this.adStoreAdo.currentMasterIdSubject.getValue() !== ads.desktop?.layer?.masterId ||
                  this.adStoreAdo.currentMasterIdSubject.getValue() !== ads.mobile?.layer?.masterId
                  ? interval(1000)
                  : interval(0)
                : interval(0)
            )
          );
        }),
        takeUntil(this.unsubscribe$)
      )
      .subscribe((adverts) => {
        this.adverts = adverts;

        this.areAdsInitiated = true;

        this.changeRef.detectChanges();
      });
    this.apiService
      .getPortfolioFooter()
      .pipe(take(1))
      .subscribe((data: PortfolioResponse) => {
        this.mediaworksFooter = data.data;
      });

    const responseData: InitResolverData & { tags: TrendingTag[] } = this.route.snapshot.data?.['data'] ?? {};
    this.breakingNews = responseData?.init?.breakingNews;
    this.dossiers = responseData?.init?.dossiers || [];

    const {
      menu: { header, footer_0, footer_1, footer_2 },
    } = responseData || {};
    this.mainMenu = header ?? [];
    this.footer0 = footer_0 ?? [];
    this.footer1 = footer_1 ?? [];
    this.footer2 = footer_2 ?? [];
    this.trendingTags = responseData?.tags;

    this.changeRef.detectChanges();
  }

  public ngAfterViewInit(): void {
    this.adblockerActiveStatus();
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next(true);
    this.unsubscribe$.complete();
  }

  openCookieSettings(): void {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    __tcfapi('displayConsentUi', 2, () => {}, true);
  }

  private baseElementPageTypeSwitch(path: string): {
    page: string;
    ads: AdvertisementBannerName[];
  } {
    const ads = ALL_BANNER_LIST as AdvertisementBannerName[];

    switch (path) {
      case '':
        this.isHomePage = true;
        return {
          page: PAGE_TYPES.main_page,
          ads,
        };
      default:
        this.isHomePage = false;
        return {
          page: this.adStoreAdo.articleParentCategory$.getValue(),
          ads,
        };
    }
  }

  private resetAds(): void {
    this.adverts = undefined;
    this.areAdsInitiated = false;
    this.changeRef.detectChanges();
  }

  private adblockerActiveStatus(): boolean | void {
    if (!this.utils.isBrowser()) {
      //Manually override to return false, because the ado does not exist on SSR.
      return;
    }
    return (this.isAdblockerActive = typeof (<any>window).ado !== 'object');
  }
}
