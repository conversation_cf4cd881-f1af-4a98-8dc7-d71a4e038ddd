<kesma-advertisement-adocean *ngIf="adverts?.desktop?.layer as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
<kesma-advertisement-adocean *ngIf="adverts?.mobile?.layer as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>

<kesma-advertisement-adocean *ngIf="adverts?.desktop?.interstitial as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>
<kesma-advertisement-adocean *ngIf="adverts?.mobile?.interstitial as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>

<kesma-advertisement-adocean *ngIf="adverts?.desktop?.technikai_1 as ad" [ad]="ad" [hasNoParentHeight]="true"> </kesma-advertisement-adocean>

<kesma-advertisement-adocean *ngIf="adverts?.mobile?.technikai_1 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>

<kesma-advertisement-adocean *ngIf="adverts?.desktop?.technikai_2 as ad" [ad]="ad" [hasNoParentHeight]="true"> </kesma-advertisement-adocean>

<kesma-advertisement-adocean *ngIf="adverts?.mobile?.technikai_2 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>

<kesma-advertisement-adocean *ngIf="adverts?.desktop?.technikai_3 as ad" [ad]="ad" [hasNoParentHeight]="true"> </kesma-advertisement-adocean>

<kesma-advertisement-adocean *ngIf="adverts?.mobile?.technikai_3 as ad" [ad]="ad" [hasNoParentHeight]="true"></kesma-advertisement-adocean>

<kesma-advertisement-adocean
  *ngIf="!isHomePage && adverts?.desktop?.leaderboard_1 as ad"
  [ad]="ad"
  [hasNoParentHeight]="true"
  [style]="{
    background: 'var(--ad-bg-dark)',
    padding: 'var(--ad-padding)',
  }"
>
</kesma-advertisement-adocean>

<app-header
  *ngIf="!isMobileApp"
  [breakingNews]="breakingNews"
  [dossiers]="dossiers"
  [footerMenu]="footer2"
  [mainMenu]="mainMenu"
  [trendingTags]="trendingTags"
  data-skip-on-mobile-app
></app-header>

<kesma-advertisement-adocean
  *ngIf="adverts?.desktop?.szponzorcsik as ad"
  [style]="{
    background: 'var(--ad-bg-dark)',
    padding: 'var(--ad-padding)',
  }"
  [ad]="ad"
  [hasNoParentHeight]="true"
>
</kesma-advertisement-adocean>

<kesma-advertisement-adocean
  *ngIf="adverts?.mobile?.szponzorcsik as ad"
  [style]="{
    background: 'var(--ad-bg-dark)',
    padding: 'var(--ad-padding)',
  }"
  [ad]="ad"
  [hasNoParentHeight]="true"
></kesma-advertisement-adocean>

<div [ngClass]="{ 'content-wrap-full-width': (isFullWidth$ | async) === true }" class="content-wrap">
  <router-outlet></router-outlet>
</div>

<kesma-advertisement-adocean
  *ngIf="adverts?.desktop?.leaderboard_footer as ad"
  [ad]="ad"
  [style]="{
    background: 'var(--ad-bg-dark)',
    padding: 'var(--ad-padding)',
  }"
>
</kesma-advertisement-adocean>
<kesma-advertisement-adocean
  *ngIf="adverts?.mobile?.mobilrectangle_footer as ad"
  [ad]="ad"
  [style]="{
    background: 'var(--ad-bg-dark)',
    padding: 'var(--ad-padding)',
  }"
>
</kesma-advertisement-adocean>

<app-footer (openCookieSettings)="openCookieSettings()" *ngIf="!isMobileApp" [bottomMenu]="footer1" [topMenu]="footer0" data-skip-on-mobile-app></app-footer>

<kesma-mediaworks-footer-compact *ngIf="!isMobileApp" [data]="mediaworksFooter" data-skip-on-mobile-app></kesma-mediaworks-footer-compact>
