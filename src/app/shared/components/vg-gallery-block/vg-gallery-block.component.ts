import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { BaseComponent, GalleryData } from '@trendency/kesma-ui';
import { VgGalleryCardComponent } from '../vg-gallery-card/vg-gallery-card.component';
import { VgBlockTitleComponent } from '../vg-block-title/vg-block-title.component';

@Component({
  selector: 'vg-gallery-block',
  templateUrl: './vg-gallery-block.component.html',
  styleUrls: ['./vg-gallery-block.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [VgBlockTitleComponent, VgGalleryCardComponent],
})
export class VgGalleryBlockComponent extends BaseComponent<GalleryData> {
  @Input() hasMoreContent: boolean = true;
  @Input() isBackgroundDark = false;
  @Input() desktopWidth = 12;

  moreContent = {
    text: 'G<PERSON><PERSON><PERSON>',
    url: '/galeriak',
    isDark: !this.isBackgroundDark,
  };
}
