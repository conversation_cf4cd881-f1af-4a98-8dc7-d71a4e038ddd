@if (toBool(data?.isAdult) && !isAdultChoice && !isInsideAdultArticleBody) {
  <kesma-adult-overlay>
    <ng-container *ngTemplateOutlet="thumbnail"></ng-container>
  </kesma-adult-overlay>
} @else {
  <ng-container *ngTemplateOutlet="thumbnail"></ng-container>
}

<ng-template #thumbnail>
  <img
    class="gallery-card-thumbnail"
    [class.placeholder]="!data?.highlightedImageUrl"
    [src]="data?.highlightedImageUrl || './assets/images/vg-placeholder-16-9.svg'"
    [alt]="data?.title"
    loading="lazy"
  />
</ng-template>
<div class="gallery-card-wrapper" [routerLink]="galleryLink">
  <div class="gallery-card-content">
    <a class="gallery-card-tag" [routerLink]="['/', 'cimke', data?.tags?.[0]?.slug]" (click)="$event.stopPropagation()" *ngIf="data?.tags?.[0]">
      {{ data?.tags?.[0]?.title }}
    </a>
    <div class="gallery-card-divider"></div>
    <a class="gallery-card-link" [routerLink]="['/', 'galeria', data?.slug]">
      <h2 class="gallery-card-title">{{ data?.title }}</h2>
      <div class="gallery-card-photo-wrapper font-small-14" *ngIf="data?.photographer">Fotó: {{ data?.photographer }}</div>
    </a>
  </div>
  <a class="gallery-card-more-content" [routerLink]="['/', 'galeriak']" *ngIf="hasMoreContent" aria-label="Galériák">
    <kesma-icon name="vg-chevron-diagonal-right" [size]="24"></kesma-icon>
  </a>
</div>
