@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  aspect-ratio: 1 /1;
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
  border-radius: 2px;
  border: 2px solid var(--kui-gray-700);
  cursor: pointer;

  &:hover {
    border-color: var(--kui-deep-teal-400);

    .gallery-card {
      &-thumbnail {
        scale: 1.1;
      }

      &-title {
        text-decoration-color: var(--kui-deep-teal-400);
      }

      &-more-content {
        color: var(--kui-deep-teal-400);
      }
    }
  }

  .gallery-card {
    &-thumbnail {
      width: 100%;
      height: 100%;
      object-fit: cover;
      transition: all 0.3s;

      &.placeholder {
        object-fit: contain;
        background-color: var(--kui-gray-100);
      }
    }

    &-wrapper {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background-color: var(--kui-deep-teal-950-o60);
    }

    &-content {
      display: flex;
      flex-direction: column;
      gap: 16px;
      padding: 16px 16px 48px 16px;
      justify-content: flex-end;
      height: calc(100% - 40px);

      @include media-breakpoint-down(md) {
        padding-bottom: 8px;
      }
    }

    &-more-content {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding-right: 8px;
      height: 40px;
      color: var(--kui-white);
    }

    &-tag {
      font-size: 14px;
      font-weight: 700;
      line-height: 18px;
      color: var(--kui-white);
      transition: all 0.3s;

      &:hover {
        color: var(--kui-deep-teal-400);
      }
    }

    &-divider {
      width: 100%;
      height: 1px;
      background-color: var(--kui-gray-200);
    }

    &-title {
      font-size: 20px;
      font-weight: 700;
      line-height: 26px;
      color: var(--kui-white);
      transition: all 0.3s;
      text-decoration: underline transparent solid 1px;
      text-underline-offset: 5px;

      @include media-breakpoint-down(md) {
        font-size: 18px;
        line-height: 24px;
      }
    }

    &-photo-wrapper {
      margin-top: 4px;
      display: flex;
      transition: all 0.3s;
      color: var(--kui-deep-teal-400);
    }
  }
}
