import { ChangeDetectionStrategy, Component, inject, Input, OnInit } from '@angular/core';
import { AdultOverlayComponent, BaseComponent, GalleryData, IconComponent, toBool } from '@trendency/kesma-ui';
import { StorageService } from '@trendency/kesma-core';
import { RouterLink } from '@angular/router';
import { NgIf, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'vg-gallery-card',
  templateUrl: './vg-gallery-card.component.html',
  styleUrls: ['./vg-gallery-card.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgTemplateOutlet, RouterLink, NgIf, AdultOverlayComponent, IconComponent],
})
export class VgGalleryCardComponent extends BaseComponent<GalleryData> implements OnInit {
  private slug: string = '';
  @Input() isInsideAdultArticleBody = false;
  @Input() hasMoreContent: boolean = true;
  isAdultChoice = false;

  private readonly storage = inject(StorageService);

  readonly toBool = toBool;

  override ngOnInit(): void {
    if (!this.data) {
      return;
    }
    this.isAdultChoice = this.storage.getSessionStorageData('isAdultChoice') ?? false;
    this.slug = this.data.slug;
  }

  public get galleryLink(): string[] {
    return ['/', 'galeria', this.slug, '1'];
  }
}
