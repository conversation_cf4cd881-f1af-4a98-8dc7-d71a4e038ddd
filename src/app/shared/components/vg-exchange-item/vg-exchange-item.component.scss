@use 'shared' as *;

.exchange-item {
  color: var(--kui-white);
  padding: 8px;
  border: 1px solid var(--kui-gray-700);
  border-radius: 2px;
  transition: 0.3s;
  font-size: 12px;
  font-weight: 700;
  line-height: 14px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 100%;

  &-name {
    white-space: nowrap;
  }

  &-value,
  &-percentage {
    color: var(--kui-deep-teal-400);
  }

  &-percentage {
    font-weight: 500;
  }

  kesma-icon {
    width: 16px;
    color: var(--kui-deep-teal-400);
  }

  &.large {
    font-size: 14px;
    line-height: 18px;
    border-color: var(--kui-gray-700);

    &-percentage {
      font-weight: 700;
    }
  }

  &.large-900 {
    font-size: 14px;
    line-height: 18px;
    border-color: var(--kui-gray-900);

    &-percentage {
      font-weight: 700;
    }
  }

  @media (hover: hover) {
    &:hover {
      border-color: var(--kui-deep-teal-400);
    }
  }

  &.up {
    @media (hover: hover) {
      &:hover {
        border-color: var(--kui-green-500);
      }
    }

    .exchange-item-value,
    .exchange-item-percentage {
      color: var(--kui-green-500);
    }

    kesma-icon {
      color: var(--kui-green-500);
    }
  }

  &.down {
    @media (hover: hover) {
      &:hover {
        border-color: var(--kui-red-500);
      }
    }

    .exchange-item-value,
    .exchange-item-percentage {
      color: var(--kui-red-500);
    }

    kesma-icon {
      color: var(--kui-red-500);
    }
  }
}
