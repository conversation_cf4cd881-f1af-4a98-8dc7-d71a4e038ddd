import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { BaseComponent, IconComponent } from '@trendency/kesma-ui';
import { VgExchangeItemData } from '../../definitions';
import { RouterLink } from '@angular/router';
import { DecimalPipe, NgClass, NgIf } from '@angular/common';

@Component({
  selector: 'vg-exchange-item',
  templateUrl: './vg-exchange-item.component.html',
  styleUrls: ['./vg-exchange-item.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgClass, RouterLink, DecimalPipe, IconComponent],
})
export class VgExchangeItemComponent extends BaseComponent<VgExchangeItemData> {
  @Input() styleID: 'normal' | 'large' | 'large-900' = 'normal';
}
