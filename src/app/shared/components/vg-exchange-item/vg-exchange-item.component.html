<a *ngIf="data" [ngClass]="styleID" [routerLink]="data.link" class="exchange-item {{ data.direction }}">
  <span class="exchange-item-name">{{ data.name }}</span>
  <span class="exchange-item-value">{{ data.value !== null ? (data.value | number: '1.0-2') : 'N.A.' }}</span>
  <span *ngIf="data.value !== null" class="exchange-item-percentage"> {{ data.percentage > 0 ? '+' : '' }}{{ data.percentage | number: '1.0-2' }}% </span>
  <kesma-icon [name]="'vg-exchange-' + data.direction" [size]="16"></kesma-icon>
</a>
