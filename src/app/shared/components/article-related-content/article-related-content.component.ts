import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { ArticleCard, BaseComponent } from '@trendency/kesma-ui';
import { NgFor, NgIf } from '@angular/common';
import { ArticleCardType } from '../../definitions';
import { VgArticleCardComponent } from '../vg-article-card/vg-article-card.component';

@Component({
  selector: 'app-article-related-content',
  templateUrl: './article-related-content.component.html',
  styleUrls: ['./article-related-content.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, VgArticleCardComponent],
})
export class ArticleRelatedContentComponent extends BaseComponent<ArticleCard[]> {
  readonly ArticleCardType = ArticleCardType;
  @Input() withImage = false;
}
