<div class="related-content-wrapper-title"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></div>
<div class="related-content-wrapper" *ngIf="!withImage; else withImageWrapper">
  <ng-container *ngFor="let article of data; trackBy: trackByFn">
    <vg-article-card [data]="article" [styleID]="ArticleCardType.RelatedArticlePlus"></vg-article-card>
  </ng-container>
</div>

<ng-template #withImageWrapper>
  <div class="related-content-wrapper-img" *ngIf="data">
    <ng-container *ngFor="let article of data; trackBy: trackByFn">
      <vg-article-card [data]="article" [hasOutline]="true" [styleID]="ArticleCardType.RelatedArticle" [displayLinkIcon]="true"> </vg-article-card>
    </ng-container>
  </div>
</ng-template>
