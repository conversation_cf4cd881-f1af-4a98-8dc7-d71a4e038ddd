@use 'shared' as *;

:host {
  display: block;
}

.related-content-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 32px;

  &-img {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px 32px;
    margin-bottom: 32px;

    @include media-breakpoint-down(lg) {
      grid-template-columns: repeat(1, 1fr);
    }

    @include media-breakpoint-down(sm) {
      grid-template-columns: repeat(2, 1fr);
    }

    @include media-breakpoint-down(xs) {
      grid-template-columns: repeat(1, 1fr);
      margin-bottom: 24px;
    }
  }

  &-title {
    margin-bottom: 16px;
    color: var(--kui-deep-teal-900);
    font-size: 16px;
    line-height: 22px;
    font-weight: 700;
  }
}

vg-article-card {
  &.style-RelatedArticle {
    ::ng-deep {
      .article-card-badges {
        margin-top: 0 !important;

        kesma-icon {
          margin-top: 0 !important;
        }
      }
    }
  }
}
