import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { BaseComponent, FocusPointDirective, IconComponent } from '@trendency/kesma-ui';
import { ArticleCardType, VgDossierRecommendedData } from '../../definitions';
import { VgArticleCardComponent } from '../vg-article-card/vg-article-card.component';
import { RouterLink } from '@angular/router';
import { NgFor, NgIf } from '@angular/common';

@Component({
  selector: 'vg-dossier-recommender',
  templateUrl: './vg-dossier-recommender.component.html',
  styleUrls: ['./vg-dossier-recommender.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, NgFor, VgArticleCardComponent, FocusPointDirective, IconComponent],
})
export class VgDossierRecommenderComponent extends BaseComponent<VgDossierRecommendedData> {
  @Input() articlesCount?: number;
  ArticleCardType = ArticleCardType;
}
