@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  &:hover {
    .dossier-recommended-main-article-thumbnail {
      transform: scale(1.1);
    }

    .dossier-recommended-header {
      border: 1px solid var(--kui-amethyst-500);
      border-bottom: none;
    }

    .dossier-recommended-details {
      @include media-breakpoint-up(sm) {
        border: 1px solid var(--kui-amethyst-500);
        border-top: none;
      }
    }

    .dossier-recommended-articles {
      @include media-breakpoint-down(sm) {
        border-inline: 1px solid var(--kui-amethyst-500);
      }
    }

    .font-bold-18,
    .vg-icon-plus {
      color: var(--kui-amethyst-500);
    }
  }
}

.dossier-recommended {
  &-header {
    border: 1px solid var(--kui-deep-teal-400);
    border-bottom: none;
    padding: 8px 8px 24px 8px;

    @include media-breakpoint-down(sm) {
      padding: 8px 8px 16px 8px;
    }
  }

  &-header-wrapper {
    display: flex;
    gap: 16px;
  }

  &-lead {
    margin-top: 4px;
    display: inline-block;

    @include media-breakpoint-up(sm) {
      padding-left: 36px;
    }
  }

  &-details {
    @include media-breakpoint-up(sm) {
      display: flex;
      justify-content: space-between;
      gap: 24px;
      border: 1px solid var(--kui-deep-teal-400);
      border-top: none;
      padding-inline: 8px;
      padding-bottom: 8px;
    }
  }

  &-main-article-thumbnail-wrapper {
    height: 100%;
    position: relative;
    overflow: hidden;
  }

  &-main-article-thumbnail {
    object-fit: cover;
    width: 312px;
    transition: transform 0.3s;

    @include media-breakpoint-down(sm) {
      width: 100%;
    }
  }

  &-articles {
    display: flex;
    flex: 1;
    flex-direction: column;
    gap: 16px;

    @include media-breakpoint-up(sm) {
      padding-left: 36px;
    }

    @include media-breakpoint-down(sm) {
      gap: 8px;
      padding: 0px 8px 16px 8px;
      border-inline: 1px solid var(--kui-deep-teal-400);
    }
  }

  &-article-count {
    position: absolute;
    display: flex;
    gap: 8px;
    right: 0;
    bottom: 0;
    padding: 8px 24px;
    border: 1px solid var(--kui-gray-750);
    background-color: var(--kui-gray-950-65);
  }

  &-articles,
  &-details,
  &-header {
    transition: all 0.3s;
  }
}

.font-bold-18,
.vg-icon-plus {
  transition: color 0.3s;
}

.font-bold-18 {
  line-height: normal;
  color: var(--kui-deep-teal-900);
}

.font-bold-16 {
  color: var(--kui-white);
}

.vg-icon-plus {
  color: var(--kui-deep-teal-400);
}

.vg-icon-chevron {
  color: var(--kui-white);
}
