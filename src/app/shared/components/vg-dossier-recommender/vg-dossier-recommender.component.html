<ng-container *ngIf="data as dossier">
  <div class="dossier-recommended-header">
    <div class="dossier-recommended-header-wrapper">
      <kesma-icon class="vg-icon-plus" name="vg-plus" [size]="20"></kesma-icon>
      <a [routerLink]="['/', 'dosszie', dossier.slug]" [attr.aria-label]="dossier.title"
        ><h2 class="font-bold-18">{{ dossier.title }}</h2></a
      >
    </div>
    <a [routerLink]="['/', 'dosszie', dossier.slug]" class="dossier-recommended-lead" [attr.aria-label]="dossier.lead"
      ><span class="font-small-14">{{ dossier.lead }}</span></a
    >
  </div>
  <div class="dossier-recommended-details">
    <div class="dossier-recommended-articles">
      <ng-container *ngFor="let article of dossier.articles; trackBy: trackByFn">
        <vg-article-card [styleID]="ArticleCardType.FeaturedTitle" [data]="article"></vg-article-card>
      </ng-container>
    </div>
    <div class="dossier-recommended-main-article-thumbnail-wrapper">
      <a [routerLink]="['/', 'dosszie', dossier.slug]">
        <img
          withFocusPoint
          [data]="data?.coverImageFocusedImages"
          [displayedUrl]="dossier.headerImage || './assets/images/vg-placeholder-16-9.svg'"
          [displayedAspectRatio]="{ desktop: '16:9' }"
          [alt]="dossier.title || ''"
          class="dossier-recommended-main-article-thumbnail"
          loading="lazy"
        />
        <div class="dossier-recommended-article-count" *ngIf="articlesCount">
          <span class="font-bold-16">{{ articlesCount }} cikk</span>
          <kesma-icon class="vg-icon-chevron" name="vg-chevron-diagonal-right"></kesma-icon>
        </div>
      </a>
    </div>
  </div>
</ng-container>
