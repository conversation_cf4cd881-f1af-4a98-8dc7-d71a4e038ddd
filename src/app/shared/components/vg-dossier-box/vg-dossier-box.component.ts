import { ChangeDetectionStrategy, Component, HostBinding, Input, OnInit } from '@angular/core';
import { ArticleCard, ArticleLinkPipe, BaseComponent, buildTagUrl, FocusPointDirective, IconComponent, Tag, toBool } from '@trendency/kesma-ui';
import { ArticleCardType, VgDossierBoxData, VgDossierType } from '../../definitions';
import { RouterLink } from '@angular/router';
import { VgArticleCardComponent } from '../vg-article-card/vg-article-card.component';
import { NgFor, NgIf, NgTemplateOutlet, SlicePipe } from '@angular/common';

@Component({
  selector: 'vg-dossier-box',
  templateUrl: './vg-dossier-box.component.html',
  styleUrls: ['./vg-dossier-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgTemplateOutlet, VgArticleCardComponent, <PERSON>F<PERSON>, RouterLink, FocusPointDirective, SlicePipe, ArticleLinkPipe, IconComponent],
})
export class VgDossierBoxComponent extends BaseComponent<VgDossierBoxData> implements OnInit {
  @HostBinding('class') hostClass?: string;

  #styleID?: VgDossierType;

  @Input() set desktopWidth(desktopWidth: number) {
    this.#desktopWidth = desktopWidth;
  } // Value from 1-12 to indicate the width of the card on desktop.
  get styleID(): VgDossierType {
    return this.#styleID as VgDossierType;
  }

  @Input()
  set styleID(styleID: VgDossierType) {
    this.hostClass = `style-${VgDossierType[styleID]}`;
    if (styleID === VgDossierType.Horizontal && this.desktopWidth < 10) {
      this.hostClass += ' column-alignment';
    }
    this.#styleID = styleID;
  }

  get desktopWidth(): number {
    return this.#desktopWidth;
  }

  #desktopWidth: number = 4;
  readonly breakpoint: number = 4;
  VgDossierType = VgDossierType;
  ArticleCardType = ArticleCardType;
  displayedTags: Tag[] = [];
  displayTagIndexes: number[] = [];
  readonly buildTagUrl = buildTagUrl;

  override ngOnInit(): void {
    this.desktopWidthHandler();
  }

  desktopWidthHandler(): VgDossierType {
    if (this.desktopWidth <= this.breakpoint) {
      return (this.styleID = this.VgDossierType.Vertical);
    }
    return (this.styleID = this.VgDossierType.Horizontal);
  }

  protected override setProperties(): void {
    const secondaryArticles = this.data?.secondaryArticles?.slice(0, 2);

    if (this.data?.mainArticle) {
      this.data.mainArticle = { ...this.data.mainArticle, isPodcastType: toBool(this.data.mainArticle?.isPodcastType) };
    }

    if (secondaryArticles?.length) {
      secondaryArticles.map((article: ArticleCard) => {
        if (article?.tags?.length) {
          const tags = article?.tags;
          const displayedTag = tags?.find(({ id }) => id === article?.firstTagId) || tags?.[0];
          if (displayedTag) {
            this.displayedTags.push(displayedTag);
            const displayedTagIndex = tags?.indexOf(displayedTag) ?? 0;
            this.displayTagIndexes.push(displayedTagIndex);
          }
        }
      });
    }
  }
}
