<ng-container *ngIf="data as dossierData">
  <ng-container *ngIf="!dossierData.sponsorship; else sponsoredHeader">
    <div class="dossier-header">
      <h2 class="dossier-header-title font-bold-24">{{ dossierData.title }}</h2>
      <ng-container *ngTemplateOutlet="moreDossierArticles; context: { isMobile: styleID === VgDossierType.Vertical }"></ng-container>
    </div>
  </ng-container>
  <div class="dossier-articles-container">
    <div class="dossier-articles-column">
      <vg-article-card
        [hasOutline]="true"
        [hideBadges]="styleID === VgDossierType.Vertical"
        [data]="dossierData.mainArticle"
        [styleID]="ArticleCardType.FeaturedTopImgTagTitleLead"
      ></vg-article-card>
    </div>
    <ng-container *ngIf="styleID === VgDossierType.Horizontal; else verticalDossierArticles">
      <div class="dossier-articles-column second">
        <ng-container *ngFor="let article of dossierData.secondaryArticles | slice: 0 : 2; let i = index; trackBy: trackByFn">
          <article class="dossier-article" *ngIf="article">
            <div class="dossier-article-details">
              <div class="dossier-article-tag-and-title">
                <div *ngIf="article.columnEmphasizeOnArticleCard" class="dossier-article-tags">
                  <div class="dossier-article-type">
                    <span class="font-small-14 dossier-article-type-title">{{ article?.columnTitle }}</span>
                  </div>
                  <a
                    class="dossier-article-first-tag font-small-14"
                    *ngIf="displayedTags?.[i] as firstTag"
                    [routerLink]="buildTagUrl(article, displayTagIndexes[i])"
                    [attr.aria-label]="firstTag.title"
                  >
                    {{ firstTag.title }}
                  </a>
                </div>
                <a [routerLink]="article | articleLink" [attr.aria-label]="article.title">
                  <h2 class="font-medium-16 dossier-article-title">{{ article.title }}</h2>
                </a>
              </div>
              <a [routerLink]="article | articleLink" class="dossier-article-thumbnail-box" [attr.aria-label]="article.title">
                <img
                  class="dossier-article-thumbnail"
                  withFocusPoint
                  [data]="article?.thumbnailFocusedImages"
                  [displayedUrl]="article.thumbnail?.url || article.thumbnailUrl || './assets/images/vg-placeholder-avatar.svg'"
                  [displayedAspectRatio]="{ desktop: '1:1' }"
                  [alt]="article.title || ''"
                  loading="lazy"
                />
                <kesma-icon class="thumbnail-icon" name="vg-chevron-diagonal-right" [size]="24"></kesma-icon>
              </a>
            </div>
            <a [routerLink]="article | articleLink" [attr.aria-label]="article.lead">
              <span class="font-medium-16 dossier-article-lead" [class.hidden-lead-on-desktop]="!i">
                {{ article.lead }}
              </span>
            </a>
            <ng-container *ngTemplateOutlet="badgesTemplate; context: { article }"></ng-container>
          </article>
        </ng-container>
      </div>
      <div class="dossier-articles-column third">
        <ng-container *ngFor="let article of dossierData.secondaryArticles | slice: 2 : 5; trackBy: trackByFn">
          <vg-article-card [data]="article" [styleID]="ArticleCardType.FeaturedTagTitle"></vg-article-card>
        </ng-container>
      </div>
    </ng-container>
    <ng-container *ngTemplateOutlet="moreDossierArticles; context: { isMobile: styleID !== VgDossierType.Vertical }"></ng-container>
  </div>

  <ng-template #verticalDossierArticles>
    <ng-container *ngFor="let article of dossierData.secondaryArticles; trackBy: trackByFn">
      <vg-article-card
        [data]="article"
        [styleID]="ArticleCardType.FeaturedRightImgTagTitle"
        [hasOutline]="true"
        [hideBadges]="true"
        [hideAuthor]="true"
      ></vg-article-card>
    </ng-container>
  </ng-template>

  <ng-template #sponsoredHeader>
    <div *ngIf="dossierData.sponsorship as sponsor" class="dossier-header sponsored">
      <a class="sponsor-details" [href]="sponsor.url" target="_blank" [attr.aria-label]="sponsor.title">
        <img
          class="sponsor-details-image"
          loading="lazy"
          [src]="sponsor.thumbnailUrl || './assets/images/vg-placeholder-16-9.svg'"
          [alt]="sponsor.title || 'szponzor logó'"
        />
        <div class="sponsor-details-separator"></div>
        <h2 class="dossier-header-title font-bold-24 mobile">{{ sponsor?.slogan }}</h2>
      </a>
      <ng-container *ngTemplateOutlet="moreDossierArticles; context: { isMobile: styleID === VgDossierType.Vertical }"></ng-container>
    </div>
    <div class="dossier-ad-label">Hirdetés</div>
  </ng-template>

  <ng-template #moreDossierArticles let-isMobile="isMobile">
    <a class="more-dossiers-container" [class.mobile]="isMobile" [routerLink]="['/', 'dosszie', dossierData.slug]">
      <span class="more-dossier-container-title font-small-14">{{
        dossierData.overwriteMoreButtonLabel?.length ? dossierData.overwriteMoreButtonLabel : 'A dosszié további hírei'
      }}</span>
      <kesma-icon name="vg-chevron-diagonal-right" [size]="16"></kesma-icon>
    </a>
  </ng-template>

  <ng-template #badgesTemplate let-article="article">
    <ng-container *ngIf="article?.isAdultsOnly || article?.isVideoType || article?.isPodcastType || article?.hasGallery">
      <div class="dossier-article-badges">
        <kesma-icon class="red" *ngIf="article?.isAdultsOnly" name="vg-adult" [size]="20"></kesma-icon>
        <ng-container *ngIf="article?.isVideoType">
          <kesma-icon class="purple" name="vg-video" [size]="20"></kesma-icon>
          <span class="badge-title purple">Videó</span>
        </ng-container>
        <ng-container *ngIf="article?.isPodcastType">
          <kesma-icon class="purple" name="vg-podcast" [size]="20"></kesma-icon>
          <span class="badge-title purple">Podcast</span>
        </ng-container>
        <ng-container *ngIf="article?.hasGallery">
          <kesma-icon class="purple" name="vg-gallery" [size]="20"></kesma-icon>
          <span class="badge-title purple">Galéria</span>
        </ng-container>
      </div>
    </ng-container>
  </ng-template>
</ng-container>
