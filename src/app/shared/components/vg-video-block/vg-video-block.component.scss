@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  @include media-breakpoint-down(xs) {
    background-color: var(--kui-gray-950);
    width: calc(100% + 48px);
    margin-left: -24px;
    padding: 24px;
  }

  vg-block-title {
    margin-bottom: 25px;
  }
  .narrow {
    flex-direction: column;
  }

  .videos {
    display: flex;
    gap: 24px;

    @include media-breakpoint-down(md) {
      flex-direction: column;
    }

    .main-video {
      flex: 1;

      vg-video-card {
        height: 100%;
      }
    }

    .sub-videos {
      display: flex;
      flex-direction: column;
      gap: 24px;
      flex: 1;
      justify-content: space-between;
    }
  }
}
