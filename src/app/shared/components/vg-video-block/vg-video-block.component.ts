import { ChangeDetectionStrategy, Component, Input, OnInit, signal } from '@angular/core';
import { ArticleCard, BaseComponent, BlockTitle } from '@trendency/kesma-ui';
import { VgVideoCardComponent } from '../vg-video-card/vg-video-card.component';
import { NgFor, NgIf } from '@angular/common';
import { VgBlockTitleComponent } from '../vg-block-title/vg-block-title.component';

@Component({
  selector: 'vg-video-block',
  templateUrl: './vg-video-block.component.html',
  styleUrls: ['./vg-video-block.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [VgBlockTitleComponent, NgIf, VgVideoCardComponent, NgFor],
})
export class VgVideoBlockComponent extends BaseComponent<ArticleCard[]> implements OnInit {
  @Input() isBackgroundDark = false;
  @Input() desktopWidth = 12;
  @Input() isSidebar = false;

  moreContent = signal<BlockTitle | null>(null);

  override ngOnInit(): void {
    this.moreContent.set({
      text: 'Videók',
      url: '/videok',
      isDark: this.isBackgroundDark,
    });
  }
}
