<vg-block-title [data]="moreContent()" [headingLevel]="2" [desktopWidth]="desktopWidth"></vg-block-title>
<ng-container *ngIf="data?.length">
  <div class="videos" [class.narrow]="isSidebar || desktopWidth < 5">
    <div class="main-video">
      <vg-video-card [data]="data?.[0]" [isLight]="isBackgroundDark" [isLayoutElement]="true"></vg-video-card>
    </div>

    <div class="sub-videos">
      <vg-video-card
        [data]="video"
        [isLight]="isBackgroundDark"
        [isSidebar]="true"
        [isLayoutElement]="true"
        *ngFor="let video of data?.slice(1, 4); trackBy: trackByFn"
      ></vg-video-card>
    </div>
  </div>
</ng-container>
