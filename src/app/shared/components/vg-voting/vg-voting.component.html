<div class="voting-header" [class.sponsored]="sponsored?.thumbnailUrl" *ngIf="!sponsored?.url">
  <ng-container *ngTemplateOutlet="headerContent"></ng-container>
</div>

<a class="voting-header sponsored" *ngIf="sponsored?.url" [href]="sponsored?.url" target="_blank" [style.background]="sponsored?.highlightedColor">
  <ng-container *ngTemplateOutlet="headerContent"></ng-container>
</a>

<div class="voting-form">
  <h2 class="voting-question font-bold-18">{{ data?.question }}</h2>
  <ul class="voting-answer-list" [class.result]="showResults">
    <ng-container *ngFor="let item of data?.answers">
      <li *ngIf="!showResults; else resultItem">
        <div class="voting-answer-list-item" (click)="setVoteId(item.id)" [class.active]="voteId === item?.id">
          <kesma-icon name="vg-bullet-voting"></kesma-icon>
          <label class="voting-answer-list-text font-bold-18" [for]="item.id"> {{ item.answer }} </label>
        </div>
      </li>
      <ng-template #resultItem>
        <div class="voting-result-container" *ngIf="data?.isResultVisible">
          <div class="voting-result">
            <span class="voting-result-text font-medium-18">{{ item.answer }}</span>
            <span class="voting-result-percentage font-medium-18">{{ item?.votePercentage ?? '0' }}%</span>
          </div>
          <div class="voting-result-line">
            <span class="voting-result-line-inner" [style.Width]="item?.votePercentage + '%'"></span>
          </div>
        </div>
      </ng-template>
    </ng-container>
  </ul>
</div>
<button *ngIf="!showResults" [disabled]="!voteId" class="voting-button" (click)="onVote()">
  <span class="voting-button-text font-bold-20">Szavazok</span>
</button>

<ng-template #headerContent>
  <h3 class="voting-header-label font-bold-18">
    {{ !showResults ? (data?.isResultVisible ? 'Szavazzon, és kövesse nyomon a szavazás állását!' : 'Szavazzon!') : 'Köszönjük válaszát!' }}
  </h3>
  <img *ngIf="sponsored?.thumbnailUrl" class="voting-header-logo" [src]="sponsored?.thumbnailUrl" alt="" loading="lazy" />
</ng-template>
