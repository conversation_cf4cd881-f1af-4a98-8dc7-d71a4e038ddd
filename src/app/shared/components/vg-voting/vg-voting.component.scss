@use 'shared' as *;

:host {
  display: block;
  width: 100%;
}

.voting {
  &-header {
    width: 100%;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--kui-deep-teal-700);

    &-label {
      color: var(--kui-white);
      text-align: center;
    }

    &.sponsored {
      justify-content: space-between;

      @include media-breakpoint-down(md) {
        flex-direction: column-reverse;
        gap: 4px;
      }

      .voting-header {
        &-label {
          text-align: start;
          justify-content: flex-start;

          @include media-breakpoint-down(md) {
            text-align: center;
          }
        }

        &-logo {
          max-height: 24px;
          max-width: 200px;
        }
      }
    }
  }

  &-form {
    display: flex;
    flex-direction: column;
    gap: 16px;
    padding: 16px 0;
    border-radius: 0px 0px 4px 4px;
    background: var(--kui-white);
  }

  &-button {
    width: 100%;
    padding: 8px;
    border-radius: 2px;
    border: 1px solid var(--kui-deep-teal-400);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--kui-white);
    transition: all 0.3s;

    &-text {
      color: var(--kui-deep-teal-400);
      font-family: var(--kui-font-primary);
    }

    &:disabled {
      cursor: default;
    }

    &:hover:enabled {
      background: var(--kui-deep-teal-400);

      .voting-button-text {
        color: var(--kui-deep-teal-950);
      }
    }
  }

  &-question {
    padding: 0 24px;
    color: var(--kui-deep-teal-700);
  }

  &-answer-list {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;

    &.result {
      gap: 24px;
    }

    li {
      width: 100%;
    }

    &-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 24px;
      width: 100%;
      cursor: pointer;
      transition: all 0.3s;

      kesma-icon {
        color: var(--kui-deep-teal-600);
      }

      &:hover {
        background-color: var(--kui-deep-teal-400);

        kesma-icon {
          color: var(--kui-white);
        }

        .voting-answer-list-text {
          color: var(--kui-white);
        }
      }

      &.active {
        background-color: var(--kui-deep-teal-600);

        kesma-icon {
          color: var(--kui-white);
        }

        .voting-answer-list-text {
          color: var(--kui-white);
        }
      }
    }

    &-text {
      color: var(--kui-deep-teal-950);
      cursor: pointer;
      transition: all 0.3s;
    }
  }

  &-result {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-container {
      padding: 0 32px;
      width: 100%;
    }

    &-text,
    &-percentage {
      color: var(--kui-gray-950);
    }

    &-line {
      height: 4px;
      background-color: var(--kui-deep-teal-100);
      display: flex;
      align-items: center;
      margin-top: 10px;
      border-radius: 2px;
    }

    &-line-inner {
      display: block;
      height: 4px;
      border-radius: 4px;
      background-color: var(--kui-deep-teal-700);
    }
  }
}
