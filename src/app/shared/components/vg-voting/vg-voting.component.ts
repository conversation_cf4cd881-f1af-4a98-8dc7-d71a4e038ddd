import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { IconComponent, Sponsorship, VotingComponent } from '@trendency/kesma-ui';
import { NgFor, NgIf, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'vg-voting',
  templateUrl: './vg-voting.component.html',
  styleUrls: ['./vg-voting.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgTemplateOutlet, NgFor, IconComponent],
})
export class VgVotingComponent extends VotingComponent {
  @Input() sponsored?: Sponsorship;
}
