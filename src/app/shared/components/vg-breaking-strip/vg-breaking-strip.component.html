<ng-container *ngIf="data as article">
  <div class="outer-wrapper">
    <div class="breaking">
      <div #breakingWrapper class="breaking-wrapper">
        <a [class.animation]="breakingWrapper.offsetWidth < row.offsetWidth" [routerLink]="buildArticleUrl(article)" class="breaking-link">
          <div #row class="breaking-row">
            <h3 class="breaking-title">Rendkívüli</h3>
            <h3 class="breaking-title">{{ article?.title }}</h3>
          </div>
        </a>
      </div>
    </div>
    <div [style.width.px]="breakingWrapper.offsetWidth" class="icon-wrapper">
      <a [routerLink]="buildArticleUrl(article)">
        <kesma-icon name="vg-arrow-right" [size]="16"></kesma-icon>
      </a>
    </div>
    <kesma-icon name="vg-circle-close" [size]="16" class="breaking-close" (click)="outOnClose.emit()"></kesma-icon>
  </div>
</ng-container>
