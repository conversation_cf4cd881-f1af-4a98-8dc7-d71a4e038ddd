@use 'shared' as *;

:host {
  display: block;
  background-color: var(--kui-red-500);
  width: 100%;
  position: relative;
  height: 32px;
  padding: 0 24px;

  .outer-wrapper {
    max-width: 1320px;
    position: relative;
    margin: 0 auto;
  }

  .breaking {
    @extend %flex;
    padding: 4px;
    gap: 4px;
    max-width: 1288px;
    margin: 0 auto;

    @include media-breakpoint-down(md) {
      justify-content: flex-start !important;
    }

    &-row {
      @extend %flex;
      gap: 17px;
    }

    &-wrapper {
      margin: 0 auto;
      white-space: nowrap;
      overflow: hidden;
      position: absolute;
      max-width: 80%;

      @include media-breakpoint-down(md) {
        max-width: 75%;
      }
    }

    &-close {
      position: absolute;
      top: calc(50% + 12px);
      right: -8px;
      transform: translate(-50%, -50%);
      cursor: pointer;
      color: var(--kui-white);
    }

    &-link {
      white-space: nowrap;
      color: var(--kui-white);
      display: inline-block;
      margin-top: 27px;

      &.animation {
        animation: loop 20s linear infinite;
        padding-left: 100%;

        @include media-breakpoint-down(lg) {
          animation-duration: 8s;
        }
      }
    }

    &-title {
      font-size: 14px;
      font-weight: 700;
      line-height: 18px;
      position: relative;

      &:first-child:after {
        content: '';
        position: absolute;
        right: -8.5px;
        width: 1px;
        height: 16px;
        background: var(--kui-white);
      }
    }
  }

  .icon-wrapper {
    @extend %flex;
    position: relative;
    max-width: 80%;
    margin: 0 auto;

    @include media-breakpoint-down(md) {
      margin-inline: 20px;
    }

    kesma-icon {
      position: absolute;
      right: -32px;
      top: calc(50% + 8px);
      transform: translate(-50%, -50%);
      color: var(--kui-white);
      cursor: pointer;
    }
  }
}

%flex {
  display: flex;
  justify-content: center;
  align-items: center;
}

@keyframes loop {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(-100%, 0);
  }
}
