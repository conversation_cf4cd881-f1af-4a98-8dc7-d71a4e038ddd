import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, EventEmitter, Output } from '@angular/core';
import { ArticleCard, BaseComponent, buildArticleUrl, IconComponent } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { NgIf } from '@angular/common';

@Component({
  selector: 'vg-breaking-strip',
  templateUrl: 'vg-breaking-strip.component.html',
  styleUrls: ['vg-breaking-strip.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, IconComponent],
})
export class VgBreakingStripComponent extends BaseComponent<ArticleCard> implements AfterViewInit {
  @Output() outOnClose: EventEmitter<void> = new EventEmitter<void>();

  readonly buildArticleUrl = buildArticleUrl;

  constructor(private readonly cdr: ChangeDetectorRef) {
    super();
  }

  ngAfterViewInit(): void {
    this.cdr.detectChanges();
  }
}
