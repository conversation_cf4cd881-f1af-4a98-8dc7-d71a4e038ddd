import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { BaseComponent, IconComponent } from '@trendency/kesma-ui';
import { AgroData } from '../../definitions';
import { Ng<PERSON>or, SlicePipe } from '@angular/common';

@Component({
  selector: 'vg-agrokep',
  templateUrl: './vg-agrokep.component.html',
  styleUrls: ['./vg-agrokep.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgFor, SlicePipe, IconComponent],
})
export class VgAgrokepComponent extends BaseComponent<AgroData[]> {
  @Input() @HostBinding('class.is-sidebar') isSidebar: boolean = false;
  @Input() articleLength = 1;
  @Input() desktopWidth!: number;
}
