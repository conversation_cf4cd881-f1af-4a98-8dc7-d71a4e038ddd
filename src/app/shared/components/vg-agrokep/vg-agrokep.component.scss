@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  .agrokep {
    &:hover {
      .agrokep-content {
        border-left: 1px solid var(--kui-deep-teal-400);
        border-right: 1px solid var(--kui-deep-teal-400);
      }
      .agrokep-more-content-container {
        border-bottom: 1px solid var(--kui-deep-teal-400);
        border-color: var(--kui-deep-teal-400);
      }
    }

    &-header {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      gap: 16px;
      padding: 12px 54px;
      transition: border 0.3s ease-out;
      border: 2px solid var(--kui-brand-agrogp);

      @include media-breakpoint-up(sm) {
        min-height: 115px;
      }

      @include media-breakpoint-down(sm) {
        padding: 25px 19px;
      }

      &:hover {
        background-color: var(--kui-brand-agrogp);

        .agrokep-header-logo {
          background-image: url('/assets/images/agrokep-logo-hover.png');

          @include media-breakpoint-down(sm) {
            background-image: url('/assets/images/agrokep-logo-hover-mobil.png');
          }
        }

        .agrokep-header-thin-logo {
          background-image: url('/assets/images/agrokep-logo-hover-mobil.png');
        }
      }

      &-thin-logo {
        background-image: url('/assets/images/agrokep-logo-mobil.png');
        @extend %logoSize;
      }

      &-logo {
        background-image: url('/assets/images/agrokep-logo.png');
        @extend %logoSize;

        @include media-breakpoint-down(sm) {
          background-image: url('/assets/images/agrokep-logo-mobil.png');
          width: 150;
          height: 34px;
        }
      }
    }

    &-article {
      &:hover {
        .agrokep-article-title {
          text-decoration-color: var(--kui-deep-teal-400);
        }
      }
      padding: 16px;
      display: flex;
      flex-direction: column;
      gap: 8px;

      &.no-padding-bottom {
        padding-bottom: 0;
      }

      &-title {
        font-style: normal;
      }

      &-title {
        font-weight: 700;
      }

      &-title {
        text-decoration: underline transparent;
        text-decoration-thickness: 1px;
        text-underline-offset: 3px;
        transition: text-decoration-color 0.3s;
      }

      &-title {
        @include media-breakpoint-down(sm) {
          font-size: 18px;
        }
      }
    }

    &-more-content-container {
      border-bottom: 1px solid var(--kui-brand-agrogp);
      transition: border-bottom 0.3s;
      &:hover {
        .agrokep-more-content-link {
          color: var(--kui-amethyst-500);
        }
        border-bottom: 1px solid var(--kui-amethyst-500) !important;
      }
    }

    &-content {
      transition: border 0.3s ease;
      border-left: 1px solid var(--kui-brand-agrogp);
      border-right: 1px solid var(--kui-brand-agrogp);
      cursor: pointer;
      &:hover {
        text-decoration-color: var(--kui-deep-teal-400);
      }
    }

    &-more-content {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
      padding: 0px 16px 16px 16px;

      &-link {
        display: flex;
        align-items: center;
        gap: 16px;
        font-size: 14px;
        line-height: 18px;
        transition: color 0.3s;
      }
    }
  }

  &.is-sidebar {
    .agrokep-header-logo,
    .agrokep-header-thin-logo {
      background-size: 133px;
    }

    .agrokep-header {
      min-height: 50px;
      padding: 0;
    }

    .agrokep-article {
      padding: 8px 16px 0 16px;
    }

    .agrokep-more-content-link {
      kesma-icon {
        color: var(--kui-deep-teal-400);

        &:hover {
          color: var(--kui-amethyst-500);
        }
      }
      span {
        display: none;
      }
    }

    .agrokep-article-title {
      font-size: 16px;
      line-height: 22px;
    }

    .agrokep-more-content {
      padding: 0 8px 8px;
    }

    .agrokep-more-content-container:hover {
      border: none;
    }
  }
}

%logoSize {
  background-repeat: no-repeat;
  background-position: right 50% top 50%;
  width: 204px;
  height: 46px;
}
