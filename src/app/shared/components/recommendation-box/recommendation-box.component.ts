import { ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, Input } from '@angular/core';
import { Mutable } from '@trendency/kesma-core';
import { ArticleCard, BlockTitle, SwiperBaseComponent } from '@trendency/kesma-ui';
import { Observable, tap } from 'rxjs';
import { ArticleCardType } from '../../definitions';
import { VgArticleCardComponent } from '../vg-article-card/vg-article-card.component';
import { VgBlockTitleComponent } from '../vg-block-title/vg-block-title.component';
import { AsyncPipe, NgForOf } from '@angular/common';

@Component({
  selector: 'app-recommendation-box',
  templateUrl: './recommendation-box.component.html',
  styleUrls: ['./recommendation-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [VgArticleCardComponent, VgBlockTitleComponent, NgForOf, AsyncPipe],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class RecommendationBoxComponent extends SwiperBaseComponent<any> {
  @Input() set recommendations$(articles$: Observable<ArticleCard[]>) {
    this.recom$ = articles$?.pipe(tap((articles) => articles.map((article: Mutable<ArticleCard>) => (article.columnEmphasizeOnArticleCard = true))));
  }

  recom$: Observable<ArticleCard[]>;

  readonly ArticleCardType = ArticleCardType;

  blockTitle: BlockTitle = {
    text: 'Címoldalról ajánljuk',
    urlName: 'Tovább a címoldalra',
    url: '/',
  };

  swiperBreakpoints = {
    768: {
      slidesPerView: 3,
      spaceBetween: 24,
    },
  };
}
