@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  .flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 24px;

    @include media-breakpoint-down(md) {
      margin-bottom: 16px;
    }
  }

  .arrows {
    display: none;
    gap: 23px;

    @include media-breakpoint-down(md) {
      display: flex;
    }

    kesma-icon {
      color: var(--kui-deep-teal-400);
    }
  }

  .mobile {
    display: none;

    @include media-breakpoint-down(md) {
      display: block;
      margin-top: 16px;
    }
  }

  ::ng-deep {
    vg-block-title {
      .block .block-url {
        width: 235px;
      }

      .block .block-title {
        @include media-breakpoint-down(md) {
          font-size: 16px;
          line-height: 22px;
        }
      }
    }

    vg-article-card {
      .article-card-thumbnail-wrapper {
        @include media-breakpoint-down(xs) {
          max-width: none !important;
        }
      }
    }
  }

  swiper-container {
    width: 100%;
  }
}
