<div class="flex">
  <vg-block-title [data]="blockTitle" [isBold]="true" [hideUrlOnMobile]="true"></vg-block-title>
  <div class="arrows">
    <!--<kesma-icon (click)="swiper.swiperRef?.slidePrev()" name="vg-chevron-left"></kesma-icon>
    <kesma-icon (click)="swiper.swiperRef?.slideNext()" name="vg-chevron-right"></kesma-icon>-->
  </div>
</div>
<swiper-container #swiper [breakpoints]="swiperBreakpoints" slides-per-view="1.7" space-between="16">
  <ng-container *ngFor="let recommendation of recom$ | async">
    <swiper-slide>
      <vg-article-card [styleID]="ArticleCardType.FeaturedImgAbsoluteTagTitle1Per1" [data]="recommendation"> </vg-article-card>
    </swiper-slide>
  </ng-container>
</swiper-container>
<vg-block-title class="mobile" [data]="blockTitle" [isBold]="true" [onlyUrl]="true"></vg-block-title>
