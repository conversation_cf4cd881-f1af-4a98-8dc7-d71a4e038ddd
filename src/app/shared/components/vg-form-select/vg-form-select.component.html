<ng-container *ngIf="formGroup && controlName" [formGroup]="formGroup">
  <kesma-form-control>
    <label *ngIf="label" class="vg-form-label" for="{{ id ?? 'select' }}"> {{ label }}</label>
    <ng-select
      #select
      (scroll)="onScroll.emit($event)"
      (scrollToEnd)="onScrollToEnd.emit()"
      [bindLabel]="bindLabel ?? 'label'"
      [bindValue]="$any(bindValue)"
      [clearable]="clearable"
      [formControlName]="controlName"
      [id]="id ?? 'select'"
      [items]="options ?? null"
      [loading]="loading"
      [multiple]="multiple"
      [placeholder]="$any(placeholder)"
      [searchable]="searchable"
      [selectOnTab]="true"
      [typeahead]="allowBackendSearch ? $any(typeahead$) : undefined"
      [virtualScroll]="true"
      class="vg-form-select"
      clearAllText="Törlés"
      loadingText="<PERSON><PERSON>rj<PERSON>k várjon..."
      notFoundText="Nincs találat a keresésre"
      typeToSearchText="Keresés..."
    >
      <ng-template let-clear="clear" let-item="item" ng-label-tmp>
        <span class="ng-value-label">{{ bindLabel ? item[bindLabel] : item }}</span>
        <span (click)="clear(item)" *ngIf="clearable" class="ng-value-icon right">
          <kesma-icon [name]="'vg-close'" [size]="12"></kesma-icon>
        </span>
      </ng-template>
    </ng-select>
  </kesma-form-control>
</ng-container>
