import { ChangeDetectionStrategy, Component } from '@angular/core';
import { FormSelectComponent, IconComponent, KesmaFormControlComponent } from '@trendency/kesma-ui';
import { NgSelectModule } from '@ng-select/ng-select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgIf } from '@angular/common';

@Component({
  selector: 'vg-form-select',
  templateUrl: './vg-form-select.component.html',
  styleUrls: ['./vg-form-select.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, FormsModule, ReactiveFormsModule, NgSelectModule, KesmaFormControlComponent, IconComponent],
})
export class VgFormSelectComponent extends FormSelectComponent {}
