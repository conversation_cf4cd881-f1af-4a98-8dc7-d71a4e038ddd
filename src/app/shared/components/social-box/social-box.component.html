<ng-container *ngIf="styleID === SocialBoxType.Footer">
  <div class="title">Kövesse a Világgazdaság híreit a közösségi médiában és csatornáinkon:</div>
</ng-container>

<ng-container *ngIf="styleID === SocialBoxType.Menu">
  <div class="title">Közösségi média oldalak és csatornáink</div>
</ng-container>

<div class="social-icons">
  <a [href]="platform.link" target="_blank" *ngFor="let platform of socialPlatforms" [attr.aria-label]="platform.name">
    <ng-container *ngIf="platform.icon !== 'vg-instagram'; else instaTemplate">
      <kesma-icon [name]="platform.icon" [customHover]="platform.hoverIcon" [hasTransition]="true"></kesma-icon>
    </ng-container>

    <ng-template #instaTemplate>
      <div class="icon-wrapper">
        <kesma-icon name="vg-instagram"></kesma-icon>
        <img src="/assets/images/icons/instagram-hover.svg" class="hover" alt="" />
      </div>
    </ng-template>
  </a>
</div>
