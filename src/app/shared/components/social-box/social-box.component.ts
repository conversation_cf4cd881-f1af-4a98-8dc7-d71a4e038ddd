import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { Ng<PERSON><PERSON>, NgIf } from '@angular/common';
import { IconComponent } from '@trendency/kesma-ui';

export enum SocialBoxType {
  Footer = 'footer',
  Menu = 'menu',
}

@Component({
  selector: 'app-social-box',
  templateUrl: './social-box.component.html',
  styleUrls: ['./social-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgFor, IconComponent],
})
export class SocialBoxComponent {
  @Input() @HostBinding('class') styleID: SocialBoxType = SocialBoxType.Footer;

  socialPlatforms = [
    {
      name: 'Facebook',
      icon: 'vg-facebook-circle-2',
      hoverIcon: 'vg-facebook-circle-hover',
      link: 'https://www.facebook.com/Vilaggazdasag',
    },
    {
      name: 'LinkedIn',
      icon: 'vg-linkedin',
      hoverIcon: 'vg-linkedin-circle-hover',
      link: 'https://hu.linkedin.com/company/vilaggazdasag',
    },
    {
      name: 'X',
      icon: 'vg-twitter',
      hoverIcon: 'vg-twitter-circle-hover',
      link: 'https://twitter.com/vg_magyarorszag',
    },
    { name: 'Instagram', icon: 'vg-instagram', link: 'https://www.instagram.com/vilaggazdasag/' },
    {
      name: 'Videa',
      icon: 'vg-videa',
      hoverIcon: 'vg-videa-circle-hover',
      link: 'https://videa.hu/csatornak/vilaggazdasag-385',
    },
    {
      name: 'YouTube',
      icon: 'vg-youtube',
      hoverIcon: 'vg-youtube-circle-hover',
      link: 'https://www.youtube.com/@VilagGazdasag_hivatalos',
    },
    {
      name: 'Spotify',
      icon: 'vg-spotify-circle-2',
      hoverIcon: 'vg-spotify-circle-hover',
      link: 'https://open.spotify.com/show/1uwXpewEwfqCZ5NigKWTIl',
    },
    {
      name: 'RSS',
      icon: 'vg-rss',
      hoverIcon: 'vg-rss-circle-hover',
      link: 'https://www.vg.hu/publicapi/hu/rss/vilaggazdasag/articles',
    },
  ];
  readonly SocialBoxType = SocialBoxType;
}
