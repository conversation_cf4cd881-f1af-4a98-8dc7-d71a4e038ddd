import { ChangeDetectionStrategy, Component, Inject, OnInit, Optional } from '@angular/core';
import { REQUEST, RESPONSE, SeoService, UtilService } from '@trendency/kesma-core';
import { defaultMetaInfo } from '../../../constants';
import type { Response } from 'express';
import { ArticleCard } from '@trendency/kesma-ui';
import { ActivatedRoute } from '@angular/router';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { isMobileApp } from '../../../utils';
import { RecommendationBoxComponent } from '../../recommendation-box/recommendation-box.component';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-404',
  templateUrl: './404.component.html',
  styleUrls: ['./404.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RecommendationBoxComponent],
})
export class Error404Component implements OnInit {
  recommendations$: Observable<ArticleCard[]> = this.route.data.pipe(map(({ data }) => data));

  isMobileApp: boolean = false;

  constructor(
    private readonly seo: SeoService,
    private readonly utilsService: UtilService,
    private readonly route: ActivatedRoute,
    private readonly utils: UtilService,
    @Optional() @Inject(REQUEST) private readonly request: Request,
    @Optional() @Inject(RESPONSE) private readonly response: Response
  ) {
    if (this.utils.isBrowser()) {
      this.isMobileApp = isMobileApp(navigator.userAgent || navigator.vendor || (window as any).opera);
    } else {
      let userAgent: string | undefined = '';

      if (this.request?.headers) {
        userAgent = Object.entries(this.request.headers).find((value) => value?.[0] === 'user-agent')?.[1];
      }

      // SSR: use request headers
      this.isMobileApp = isMobileApp(userAgent || '');
    }
  }

  ngOnInit(): void {
    this.seo.setMetaData({
      ...defaultMetaInfo,
      title: `404 - ${defaultMetaInfo.ogTitle}`,
      ogTitle: `404 - ${defaultMetaInfo.ogTitle}`,
      robots: 'noindex',
    });

    if (!this.utilsService.isBrowser() && this.response) {
      this.response.status(404);
    }
  }
}
