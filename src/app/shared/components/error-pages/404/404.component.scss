@use 'shared' as *;

:host {
  display: block;
  color: var(--kui-deep-teal-900);
  font-family: var(--kui-font-primary);
  padding: 44px 0;
  width: 100%;

  @include media-breakpoint-down(md) {
    padding: 32px 0 24px;
  }

  h1 {
    font-family: inherit;
  }

  .font-bold-18 {
    color: var(--kui-gray-950);
  }

  .content {
    max-width: 760px;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 auto;
    gap: 8px;
  }

  strong {
    font-size: 24px;
    letter-spacing: 0.96px;
    text-align: center;
    line-height: normal;

    @include media-breakpoint-down(md) {
      font-size: 18px;
    }
  }

  app-recommendation-box {
    margin-top: 36px;
  }
}
