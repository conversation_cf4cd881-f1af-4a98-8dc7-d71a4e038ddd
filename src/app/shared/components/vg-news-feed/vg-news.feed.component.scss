@use 'shared' as *;

:host {
  display: block;
  color: var(--kui-gray-950);
  width: 100%;

  .news {
    &-link {
      display: flex;
      flex-direction: column;
    }

    &-thumbnail {
      object-fit: cover;
      width: 100%;
      transition: transform 0.5s;

      &-box {
        position: relative;
        overflow: hidden;
      }
    }

    &-badge {
      color: var(--kui-white);
      background-color: var(--kui-gray-950-65);
      position: absolute;
      bottom: 0;
      padding: 5px 8px;

      &:hover {
        background-color: var(--kui-deep-teal-400);
      }
    }

    &-title {
      padding: 8px;
      line-height: normal;
      font-size: 18px;
      font-weight: 700;
      letter-spacing: 0.36px;
      border: 1px solid var(--kui-gray-100);
    }
  }

  &:hover .news {
    &-title {
      text-underline-offset: 5px;
      text-decoration: underline var(--kui-deep-teal-400) solid 1px;
      border-color: var(--kui-deep-teal-400);
    }

    &-thumbnail {
      transform: scale(1.1);
    }
  }
}
