import { ChangeDetectionStrategy, Component } from '@angular/core';
import { BaseComponent, DossierData, FocusPointDirective } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'vg-news-feed',
  templateUrl: 'vg-news.feed.component.html',
  styleUrls: ['vg-news.feed.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, FocusPointDirective],
})
export class VgNewsFeedComponent extends BaseComponent<DossierData> {}
