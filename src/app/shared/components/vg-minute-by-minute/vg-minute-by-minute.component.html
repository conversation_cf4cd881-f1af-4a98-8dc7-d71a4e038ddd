<article class="mbm" *ngIf="data as article">
  <a class="mbm-thumbnail-box" [routerLink]="buildArticleUrl(article)">
    <img
      loading="lazy"
      class="mbm-thumbnail"
      withFocusPoint
      [data]="article?.thumbnailFocusedImages"
      [displayedUrl]="article?.thumbnail?.url || './assets/images/vg-placeholder-16-9.svg'"
      [displayedAspectRatio]="{ desktop: '16:9' }"
      [alt]="article?.thumbnail?.alt || ''"
    />
    <div class="mbm-live">
      <kesma-icon name="vg-live" [size]="24"></kesma-icon> <PERSON><PERSON><PERSON> közvetí<PERSON>
      <ng-container *ngIf="article?.minuteToMinute === MinuteToMinuteState.RUNNING">
        <div class="mbm-separator"></div>
        <span class="mbm-live-text">folyamatosan frissül</span>
      </ng-container>
    </div>
  </a>

  <a class="font-bold-16" [routerLink]="buildFirstTagUrl(article)">
    {{ getFirstTag(article)?.title }}
  </a>

  <a [routerLink]="buildArticleUrl(article)">
    <h2 class="mbm-title underline-hover font-bold-36">{{ article?.title }}</h2>
    <div class="mbm-lead underline-hover" *ngIf="article?.lead as lead">{{ lead }}</div>
  </a>

  <ng-container *ngIf="badges(data)?.hasBadges">
    <div class="mbm-badges" *ngIf="badges(data)?.items as badge">
      <kesma-icon class="red" name="vg-adult" [size]="24" *ngIf="badge?.isAdultsOnly"></kesma-icon>
      <ng-container *ngIf="badge?.isVideoType">
        <kesma-icon [size]="24" class="purple" name="vg-video"></kesma-icon>
        <span class="badge-title purple">Videó</span>
      </ng-container>
      <ng-container *ngIf="badge?.isPodcastType">
        <kesma-icon [size]="24" class="purple" name="vg-podcast"></kesma-icon>
        <span class="badge-title purple">Podcast</span>
      </ng-container>
      <ng-container *ngIf="badge?.hasGallery">
        <kesma-icon [size]="24" class="purple" name="vg-gallery"></kesma-icon>
        <span class="badge-title purple">Galéria</span>
      </ng-container>
    </div>
  </ng-container>

  <div class="mbm-posts" *ngFor="let minuteByMinute of minuteByMinutes | slice: 0 : 2">
    <div class="mbm-posts-date font-bold-18" *ngIf="minuteByMinute?.date as date">
      <kesma-icon name="vg-bullet" [size]="24"></kesma-icon> {{ date?.toString() | minuteByMinuteTime }}
    </div>
    <div class="mbm-posts-separator"></div>
    <a [routerLink]="buildArticleUrl(article)" [fragment]="'pp-' + minuteByMinute?.id" class="font-bold-18 underline-hover">
      {{ minuteByMinute?.title }}
    </a>
  </div>
</article>
