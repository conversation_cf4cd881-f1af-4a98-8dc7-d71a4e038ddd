import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import {
  ArticleCard,
  BaseComponent,
  buildArticleUrl,
  buildFirstTagUrl,
  FocusPointDirective,
  getFirstTag,
  IconComponent,
  minuteByMinuteTimePipe,
  MinuteToMinuteBlock,
  MinuteToMinuteState,
} from '@trendency/kesma-ui';
import { getArticleBadges } from '../../utils';
import { RouterLink } from '@angular/router';
import { NgFor, NgIf, SlicePipe } from '@angular/common';

@Component({
  selector: 'vg-minute-by-minute',
  templateUrl: 'vg-minute-by-minute.component.html',
  styleUrls: ['vg-minute-by-minute.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, FocusPointDirective, NgFor, SlicePipe, IconComponent, minuteByMinuteTimePipe],
})
export class VgMinuteByMinuteComponent extends BaseComponent<ArticleCard> {
  @Input() minuteByMinutes?: MinuteToMinuteBlock[];

  readonly MinuteToMinuteState = MinuteToMinuteState;
  readonly buildArticleUrl = buildArticleUrl;
  readonly buildFirstTagUrl = buildFirstTagUrl;
  readonly getFirstTag = getFirstTag;
  readonly badges = getArticleBadges;
}
