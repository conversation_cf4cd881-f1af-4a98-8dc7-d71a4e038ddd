@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  .mbm {
    display: flex;
    flex-direction: column;
    gap: 16px;

    @include media-breakpoint-down(md) {
      gap: 8px;
    }

    &-thumbnail {
      object-fit: cover;
      transition: transform 0.5s;
      width: 100%;

      &:hover {
        transform: scale(1.1);
      }
    }

    &-thumbnail-box {
      position: relative;
      overflow: hidden;
      display: block;
    }

    &-live {
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      bottom: 0;
      left: 0;
      border-radius: 2px;
      border: 1px solid var(--kui-red-500);
      background: var(--kui-red-500-o75);
      padding: 16px 24px;
      color: var(--kui-white);
      font-size: 20px;
      font-weight: 700;
      transition: background 0.5s;

      @include media-breakpoint-down(md) {
        font-size: 18px;
        padding: 8px 16px;
      }

      &-text {
        font-weight: 500;

        @include media-breakpoint-down(md) {
          display: none;
        }
      }

      &:hover {
        background: var(--kui-red-500);
      }
    }

    &-lead {
      font-size: 24px;
      font-weight: 500;

      @include media-breakpoint-down(md) {
        font-size: 20px;
      }
    }

    &-title {
      margin-bottom: 16px;

      @include media-breakpoint-down(md) {
        font-size: 28px;
        line-height: normal;
        letter-spacing: 0.56px;
        margin-bottom: 8px;
      }
    }

    &-badges {
      display: flex;
      padding: 4px 0;
      align-items: center;
      gap: 8px;

      .badge-title {
        font-size: 12px;
        font-weight: 700;
        line-height: 14px;
        margin-left: -4px;

        @include media-breakpoint-down(md) {
          display: none;
        }
      }

      .purple {
        color: var(--kui-amethyst-500);
      }

      .red {
        color: var(--kui-red-500);
      }
    }

    &-separator {
      background: var(--kui-white);
      width: 1px;
      height: 20px;

      @include media-breakpoint-down(md) {
        display: none;
      }
    }

    &-posts {
      display: flex;
      align-items: flex-start;
      gap: 8px;

      &-date {
        white-space: nowrap;
        color: var(--kui-deep-teal-400);
        display: flex;
        justify-content: center;
        gap: 8px;

        kesma-icon {
          width: 24px;
        }
      }

      &-separator {
        background: var(--kui-deep-teal-400);
        width: 1px;
        height: 20px;
      }
    }
  }

  .font-bold-16 {
    &:hover {
      color: var(--kui-deep-teal-400);
    }

    @include media-breakpoint-down(md) {
      font-size: 14px;
      line-height: 18px;
    }
  }

  .underline-hover:hover {
    text-decoration: underline solid var(--kui-deep-teal-400) 1px;
    text-underline-offset: 5px;
  }
}
