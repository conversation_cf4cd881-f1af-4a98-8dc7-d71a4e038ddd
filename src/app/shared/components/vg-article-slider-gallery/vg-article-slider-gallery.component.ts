import { AfterViewInit, ChangeDetectionStrategy, Component, CUSTOM_ELEMENTS_SCHEMA, EventEmitter, Input, Output } from '@angular/core';
import { AdultOverlayComponent, ClickStopPropagationDirective, IconComponent, SliderGalleryComponent } from '@trendency/kesma-ui';
import { Async<PERSON>ip<PERSON>, NgFor, NgIf, NgTemplateOutlet } from '@angular/common';
import { SliderGalleryFullscreenLayerClickedEvent } from '../../definitions';

@Component({
  selector: 'vg-vg-article-slider-gallery',
  templateUrl: './vg-article-slider-gallery.component.html',
  styleUrls: ['./vg-article-slider-gallery.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgFor, NgTemplateOutlet, NgIf, AsyncPipe, IconComponent, AdultOverlayComponent, ClickStopPropagationDirective],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class VgArticleSliderGalleryComponent extends SliderGalleryComponent implements AfterViewInit {
  @Output() fullscreenLayerClicked = new EventEmitter<SliderGalleryFullscreenLayerClickedEvent>();

  initialSlide = 0;

  @Input() set highlightedImageId(imageId: string) {
    if (!imageId) {
      return;
    }
    const image = this.data?.images?.find(({ id }) => id === imageId);
    let slideId = 0;
    if (this.data?.images && image) {
      const indexOfResult = this.data.images.indexOf(image);
      slideId = indexOfResult === -1 ? 0 : indexOfResult;
    }
    this.initialSlide = slideId;
  }

  override ngAfterViewInit(): void {
    super.ngAfterViewInit();
    this.swiperRef()?.slideTo(this.initialSlide, 0);
  }

  override onOpenSliderLayer(): void {
    // We cannot open the layer gallery here, as it needs to be on a dedicated route.
    if (this.data) {
      this.fullscreenLayerClicked.emit({ gallery: this.data, selectedImageIndex: this.activeIndex });
    }
  }

  onSwipePrevClick(): void {
    this.swiperRef()?.slidePrev();
  }

  onSwipeNextClick(): void {
    this.swiperRef()?.slideNext();
  }
}
