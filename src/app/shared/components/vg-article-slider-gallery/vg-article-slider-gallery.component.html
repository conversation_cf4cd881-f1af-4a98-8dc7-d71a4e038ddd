<div class="slider-wrapper-main">
  <div class="swiper-wrapper" (click)="onOpenSliderLayer()">
    <div class="swiper-prev">
      <kesma-icon name="vg-chevron-left" [size]="24"></kesma-icon>
    </div>
    <div class="swiper-next">
      <kesma-icon name="vg-chevron-right" [size]="24"></kesma-icon>
    </div>
    <swiper-container #swiper auto-height="false" slides-per-view="auto" centered-slides="true" space-between="0" loop="true" class="main" init="false">
      <swiper-slide *ngFor="let elem of data?.images; trackBy: trackByFn">
        <article class="slider-slide-container">
          @if (data?.isAdult && !isAcceptedAdultContent && !isInsideAdultArticleBody) {
            <kesma-adult-overlay>
              <ng-container *ngTemplateOutlet="galleryImage"></ng-container>
              <button class="adult-btn" (click)="acceptAdultContent()" clickStopPropagation custom-overlay-content>Megnézem</button>
            </kesma-adult-overlay>
          } @else {
            <ng-container *ngTemplateOutlet="galleryImage"></ng-container>
          }
          <ng-template #galleryImage>
            <img [style.height.px]="swiperHeight" alt="{{ elem?.altText ?? elem?.title }}" src="{{ elem?.url?.fullSize }}" />
          </ng-template>
        </article>
      </swiper-slide>
    </swiper-container>
  </div>

  <div class="gallery-details">
    <div *ngIf="data?.title" class="title">{{ data?.title }}</div>
    <div *ngIf="data?.photographer" class="photographer">Fotó: {{ data?.photographer }}</div>

    <div class="flex-wrapper">
      <div class="left">
        <div class="page">{{ ((realIndex$ | async) ?? 0) + 1 }}/{{ data?.images?.length }}</div>
        <div class="description">{{ data?.images?.[(realIndex$ | async) ?? 0]?.caption || data?.title }}</div>
      </div>

      <div class="pager">
        <button type="button" class="swiper-prev" (click)="onSwipePrevClick()">
          <kesma-icon name="vg-chevron-left" [size]="24"></kesma-icon>
        </button>
        <button type="button" class="swiper-next" (click)="onSwipeNextClick()">
          <kesma-icon name="vg-chevron-right" [size]="24"></kesma-icon>
        </button>
      </div>
    </div>
  </div>
</div>
