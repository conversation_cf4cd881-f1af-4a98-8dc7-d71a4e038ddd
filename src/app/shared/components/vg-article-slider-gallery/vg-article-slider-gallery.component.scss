@use 'shared' as *;

:host {
  display: block;

  .adult-btn {
    font-size: 14px;
    line-height: 18px;
    font-weight: 700;
    width: 200px;
    background-color: var(--kui-gray-800);
    color: var(--kui-deep-teal-400);
    border: 1px solid var(--kui-deep-teal-400);
    padding: 8px;

    &:hover {
      background-color: var(--kui-deep-teal-400);
      color: var(--kui-gray-800);
    }

    @include media-breakpoint-down(md) {
      font-size: 12px;
      width: 150px;
      padding: 6px;
    }
  }

  .swiper-wrapper {
    cursor: pointer;
    position: relative;

    @include media-breakpoint-down(md) {
      margin: 0 -24px;
      width: calc(100% + 48px);
    }

    .swiper-prev {
      left: 0;
      bottom: 0;
    }

    .swiper-next {
      right: 0;
      bottom: 0;
    }

    .swiper-prev,
    .swiper-next {
      z-index: 2;
      position: absolute;

      @include media-breakpoint-up(md) {
        display: none;
      }
    }
  }

  ::ng-deep {
    .main {
      swiper-slide {
        width: 74%;

        @include media-breakpoint-down(sm) {
          width: 90%;
        }

        img {
          aspect-ratio: 16/9;
          object-fit: cover;
          width: 100%;
          cursor: pointer;
          height: 420px !important;
          object-position: top;

          @include media-breakpoint-down(sm) {
            height: 246px !important;
          }
        }
      }

      .swiper-slide {
        &-prev,
        &-next {
          position: relative;

          &:after {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            opacity: 70%;
            background:
              linear-gradient(0deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.5) 100%),
              lightgray 50% / cover no-repeat;
          }
        }
      }
    }
  }

  .gallery-details {
    margin-top: 8px;

    .title {
      font-size: 18px;
      font-weight: 700;
      line-height: 24px;
      letter-spacing: 0.36px;
      color: var(--kui-gray-950);
    }

    .photographer {
      margin-top: 4px;
      font-size: 14px;
      font-weight: 400;
      line-height: 18px;
      color: var(--kui-deep-teal-600);
    }

    .flex-wrapper {
      border-top: 1px solid var(--kui-deep-teal-400);
      margin-top: 8px;
      padding-top: 8px;
      display: flex;
      gap: 20px;
      justify-content: space-between;
      align-items: center;

      .left {
        display: flex;
        gap: 8px;
      }

      .page {
        font-size: 16px;
        font-weight: 700;
        line-height: 22px;
        color: var(--kui-deep-teal-600);
      }

      .description {
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        color: var(--kui-gray-950);
      }

      .pager {
        display: flex;
        gap: 32px;

        @include media-breakpoint-down(md) {
          display: none;
        }
      }
    }
  }

  .swiper-prev,
  .swiper-next {
    border: 1px solid var(--kui-deep-teal-400);
    padding: 4px;
    border-radius: 2px;
    cursor: pointer;
    color: var(--kui-deep-teal-400);
    height: 32px;
    width: 32px;
    background-color: var(--kui-gray-50-o50);

    &:hover {
      border-color: var(--kui-deep-teal-400);
      background-color: var(--kui-deep-teal-400);
      color: var(--kui-white);
    }
  }
}
