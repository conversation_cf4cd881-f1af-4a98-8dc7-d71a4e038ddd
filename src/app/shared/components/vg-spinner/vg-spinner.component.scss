@use 'shared' as *;

:host {
  --spinner-width: 100px;
  --spinner-height: 100px;
  --spinner-color: var(--kui-deep-teal-500);
  width: var(--spinner-width);
  height: var(--spinner-height);
  margin: auto;
}

.loader {
  width: var(--spinner-width);
  height: var(--spinner-height);
  padding: calc(var(--spinner-width) / 8);
  aspect-ratio: 1;
  border-radius: 50%;
  background: var(--spinner-color);
  --spinner-gradient: conic-gradient(transparent 10%, var(--kui-black)), linear-gradient(var(--kui-black) 0%, var(--kui-black)) content-box;
  mask: var(--spinner-gradient);
  mask-composite: subtract;
  -webkit-mask: var(--spinner-gradient);
  -webkit-mask-composite: source-out;
  animation: rotation 1s infinite linear;
}

@keyframes rotation {
  to {
    transform: rotate(1turn);
  }
}
