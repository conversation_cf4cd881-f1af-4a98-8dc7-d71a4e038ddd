<div (clickOutside)="closeMenu()">
  <header [class.sticky]="isHeaderSticky$ | async" class="header" #header>
    <div class="header-top">
      <a (click)="closeMenu()" [routerLink]="'/'" class="icon-wrapper">
        <!-- DESKTOP -->
        <img [src]="(isHeaderSticky$ | async) ? 'assets/images/logo/logo-mobile.svg' : 'assets/images/logo/logo.svg'" alt="Logo" class="header-logo desktop" />
        <img
          [src]="(isHeaderSticky$ | async) ? 'assets/images/logo/logo-mobile-green.svg' : 'assets/images/logo/logo-green.svg'"
          alt="Logo"
          class="header-logo hover desktop"
        />

        <!-- MOBILE -->
        <img alt="Logo" class="header-logo mobile" src="assets/images/logo/logo-mobile.svg" />
        <img alt="Logo" class="header-logo hover mobile" src="assets/images/logo/logo-mobile-green.svg" />
      </a>

      <ng-container *ngIf="(isHeaderSticky$ | async) === false || (isHeaderSticky$ | async) === null">
        <ng-container *ngTemplateOutlet="headerTagsTemplate"></ng-container>
      </ng-container>

      <div class="header-right">
        <ng-container *ngIf="isHeaderSticky$ | async">
          <ng-container *ngIf="isArticlePage$ | async">
            <app-social-share [emailSubject]="''" customMobileShareIcon="vg-share-mobile"></app-social-share>
          </ng-container>

          <ng-container *ngTemplateOutlet="exchangeItemsTemplate"></ng-container>
        </ng-container>

        <!--<a [routerLink]="['/kalkulatorok']" class="calculators">
        <span>Kalkulátorok</span>
        <kesma-icon name="vg-calculator" [hasTransition]="true"></kesma-icon>
      </a>-->

        <a [routerLink]="['/kereses']" aria-label="Keresés">
          <kesma-icon [hasTransition]="true" name="vg-search"></kesma-icon>
        </a>

        <button (click)="openMenu()" class="hamburger" aria-label="Menü">
          <kesma-icon [hasTransition]="true" name="vg-hamburger"></kesma-icon>
        </button>
      </div>
    </div>

    <ng-container *ngIf="isHeaderSticky$ | async">
      <div class="header-bottom">
        <div class="header-bottom-tags-wrapper">
          <ng-container *ngTemplateOutlet="headerTagsTemplate"></ng-container>
        </div>

        <div class="header-bottom-divider"></div>

        <div class="header-bottom-exchange-wrapper">
          <div class="header-bottom-exchange">
            <ng-container *ngTemplateOutlet="exchangeItemsTemplate"></ng-container>
          </div>
        </div>
      </div>
    </ng-container>
  </header>

  <app-currency-exchange-stripe></app-currency-exchange-stripe>
  <app-stock-exchange-stripe></app-stock-exchange-stripe>

  <app-mobile-menu
    class="mobile-menu"
    [class.mobile-menu-visible]="isMenuOpen"
    [(menuState)]="isMenuOpen"
    [dossiers]="dossiers"
    [footerMenu]="footerMenu"
    [mainMenu]="mainMenu"
    [tags]="trendingTags"
  >
  </app-mobile-menu>
</div>

<vg-breaking-strip *ngIf="breakingNews && (isHidden$ | async) === null" (outOnClose)="closeBreakingStrip()" [data]="breakingNews"></vg-breaking-strip>

<ng-template #headerTagsTemplate>
  <ul class="header-tags">
    <li *ngFor="let tag of trendingTags">
      <a (click)="closeMenu()" [routerLink]="['/cimke', tag.slug]" class="header-tags-item hover-underline">
        {{ tag.title }}
      </a>
    </li>
  </ul>
</ng-template>

<ng-template #exchangeItemsTemplate>
  <vg-exchange-item [data]="(top1StockData$ | async) ?? undefined" [styleID]="'large-900'"></vg-exchange-item>
  <vg-exchange-item [data]="(top1CurrencyData$ | async) ?? undefined" [styleID]="'large-900'"></vg-exchange-item>
</ng-template>
