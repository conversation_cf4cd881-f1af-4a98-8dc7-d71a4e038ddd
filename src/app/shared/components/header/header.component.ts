import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, ElementRef, Input, OnDestroy, ViewChild } from '@angular/core';
import { BreakingBlock, ClickOutsideDirective, DossierData, IconComponent, SimplifiedMenuItem, TrendingTag } from '@trendency/kesma-ui';
import { debounceTime, distinctUntilChanged, fromEvent, merge, Observable, of, Subject, takeUntil } from 'rxjs';
import { filter, map, startWith, tap } from 'rxjs/operators';
import { UtilService } from '@trendency/kesma-core';
import { ApiService, FinancialLatestDataService, HeaderService } from '../../services';
import { ActivatedRoute, NavigationEnd, Router, RouterLink } from '@angular/router';
import { MobileMenuComponent } from './mobile-menu/mobile-menu.component';
import { CurrencyExchangeStripeComponent } from '../currency-exchange-stripe/currency-exchange-stripe.component';
import { SocialShareComponent } from '../social-share/social-share.component';
import { AsyncPipe, NgFor, NgIf, NgTemplateOutlet } from '@angular/common';
import { StockExchangeStripeComponent } from '../stock-exchange-stripe/stock-exchange-stripe.component';
import { VgExchangeItemData } from '../../definitions';
import { VgExchangeItemComponent } from '../vg-exchange-item/vg-exchange-item.component';
import { VgBreakingStripComponent } from '../vg-breaking-strip/vg-breaking-strip.component';

const STICKY_POINT = 100;

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    RouterLink,
    NgIf,
    NgTemplateOutlet,
    SocialShareComponent,
    CurrencyExchangeStripeComponent,
    StockExchangeStripeComponent,
    MobileMenuComponent,
    NgFor,
    AsyncPipe,
    VgExchangeItemComponent,
    VgBreakingStripComponent,
    ClickOutsideDirective,
    IconComponent,
  ],
})
export class HeaderComponent implements AfterViewInit, OnDestroy {
  @ViewChild('header') header: ElementRef;
  @Input() mainMenu: SimplifiedMenuItem[] = [];
  @Input() footerMenu: SimplifiedMenuItem[] = [];
  @Input() trendingTags: TrendingTag[] = [];
  @Input() dossiers: DossierData[] = [];
  @Input() breakingNews?: BreakingBlock;

  isMenuOpen = false;
  isHidden$ = this.apiService.isHidden$;

  destroy$ = new Subject<boolean>();
  isHeaderSticky$ = this.utilService.isBrowser()
    ? fromEvent(window, 'scroll').pipe(
        map(() => window.scrollY > STICKY_POINT),
        distinctUntilChanged()
      )
    : of(false);
  isArticlePage$ = this.router.events.pipe(
    filter((e) => e instanceof NavigationEnd),
    startWith(null),
    map(() => this.route.firstChild?.snapshot.data?.['isArticlePage'] === true)
  );

  top1CurrencyData$: Observable<VgExchangeItemData> = this.financialLatestDataService.latestCurrencyData$.pipe(map((data: VgExchangeItemData[]) => data[0]));
  top1StockData$: Observable<VgExchangeItemData> = this.financialLatestDataService.latestStockData$.pipe(
    map((data: VgExchangeItemData[]) => ({
      ...data[0],
      link: '/reszvenyek',
    }))
  );

  constructor(
    private readonly hostElement: ElementRef,
    private readonly utilService: UtilService,
    private readonly financialLatestDataService: FinancialLatestDataService,
    public readonly headerService: HeaderService,
    private readonly cdr: ChangeDetectorRef,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly apiService: ApiService
  ) {}

  ngAfterViewInit(): void {
    if (!this.utilService.isBrowser()) return;

    this.isHeaderSticky$.pipe(takeUntil(this.destroy$)).subscribe(() => {
      this.cdr.detectChanges();
    });

    const scroll$ = fromEvent(window, 'scroll');
    const resize$ = fromEvent(window, 'resize');

    merge(scroll$, resize$)
      .pipe(
        debounceTime(250),
        tap(() => {
          const element = window.scrollY > STICKY_POINT ? this.header.nativeElement : this.hostElement.nativeElement;
          const elementRect = element.getBoundingClientRect();
          this.headerService.setHeaderTopOffset(elementRect.top);
          this.headerService.setHeaderHeight(elementRect.height);
        }),
        takeUntil(this.destroy$)
      )
      .subscribe();
  }

  ngOnDestroy(): void {
    this.destroy$.next(true);
    this.destroy$.complete();
  }

  openMenu(): void {
    this.isMenuOpen = true;
  }

  closeMenu(): void {
    this.isMenuOpen = false;
  }

  closeBreakingStrip(): void {
    this.apiService.isHiddenSubject$.next(true);
  }
}
