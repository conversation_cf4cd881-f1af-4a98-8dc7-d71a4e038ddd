@use 'shared' as *;

:host {
  display: block;
  width: 367px;
  height: 100vh;
  background: var(--kui-gray-950);
  padding-right: 36px;

  .mobile-menu {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    gap: 16px;
    padding: 8px 24px;
    height: calc(100% - 99px);

    &-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 24px;
      border-bottom: 1px solid var(--kui-gray-900);

      .close-button {
        padding: 8px;
        border-radius: 2px;
        border: 1px solid var(--kui-gray-900);
        transition: 0.3s;
        color: var(--kui-white);

        &:hover {
          border-color: var(--kui-deep-teal-400);
          color: var(--kui-deep-teal-400);
        }
      }
    }

    &-item {
      color: var(--kui-white);
      font-weight: 700;
      line-height: 22px;
      display: inline-flex;
      justify-content: space-between;
      align-items: center;
      transition: 0.3s;

      &.empty {
        position: relative;
        pointer-events: none;

        &:before {
          content: '';
          position: absolute;
          left: 0;
          bottom: -1px;
          height: 1px;
          width: 100%;
          background: var(--kui-white);
        }

        &:hover {
          color: var(--kui-white) !important;
        }
      }

      &:not(.child) {
        padding: 8px 0;
        &:before:hover {
          background: var(--kui-amethyst-500);
        }

        &:not(:hover):before {
          opacity: 1;
          background: var(--kui-white);
        }

        &:not(.no-hover):hover {
          color: var(--kui-amethyst-500);
        }
      }

      &.child {
        position: relative;
        padding-left: 20px;

        &:before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          width: 1px;
          height: 24px;
          border-radius: 2px;
          background: var(--kui-gray-800);
        }
      }
    }

    .footer-menu {
      margin: 16px 0;
      display: flex;
      flex-direction: column;
      gap: 18px;

      .mobile-menu-item {
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px;

        &:before {
          border-radius: 10px;
          background: var(--kui-deep-teal-900);
          width: 2px;
          height: 20px;
        }
      }
    }
  }

  @include media-breakpoint-down(md) {
    width: 100%;
    padding-right: 0;
  }
}

.with-logo {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 10px;

  img {
    height: 24px;
    width: 24px;
  }
}

// SCROLLBAR
/* width */
::-webkit-scrollbar {
  width: 8px;
}

/* Track */
::-webkit-scrollbar-track {
  background: var(--kui-gray-900);
  border-radius: 2px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: var(--kui-gray-50);
  border-radius: 100px;
  border: 2px solid var(--kui-gray-900);
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: var(--kui-gray-200);
}
