<div class="mobile-menu-header">
  <a class="icon-wrapper" [routerLink]="['/']">
    <img class="header-logo" src="/assets/images/logo/logo-mobile.svg" alt="Logo" />
    <img class="header-logo hover" src="assets/images/logo/logo-mobile-green.svg" alt="Logo" />
  </a>

  <button (click)="closeMenu()" class="close-button">
    <kesma-icon name="vg-close"></kesma-icon>
  </button>
</div>

<nav class="mobile-menu">
  <a [routerLink]="['/kereses']" class="mobile-menu-item hover-underline-purple" (click)="closeMenu()">
    <span>Keresés</span>
    <kesma-icon name="vg-search"></kesma-icon>
  </a>

  <!-- TAGS -->
  <ng-container *ngFor="let tag of tags">
    <div>
      <a [routerLink]="['/cimke', tag.slug]" class="mobile-menu-item child" (click)="closeMenu()">
        <span class="hover-underline">{{ tag.title | titlecase }}</span>
      </a>
    </div>
  </ng-container>

  <!-- CATEGORIES -->
  <ng-container *ngIf="categories?.length">
    <a class="mobile-menu-item empty no-hover">Rovatok</a>
  </ng-container>

  <ng-container *ngFor="let category of categories">
    <div>
      <a
        [routerLink]="category.link"
        class="mobile-menu-item child"
        [class.with-logo]="category.relatedType === RelatedType.COLUMN && category?.related?.sponsorship"
        (click)="closeMenu()"
      >
        @if (category.relatedType === RelatedType.COLUMN && category?.related?.sponsorship; as sponsorship) {
          <img [src]="sponsorship?.logo" loading="lazy" />
        }
        <span class="hover-underline">{{ category.title }}</span>
      </a>
    </div>
  </ng-container>

  <!-- DOSSIERS -->
  <ng-container *ngIf="dossiers?.length">
    <a [routerLink]="['/dosszie']" class="mobile-menu-item hover-underline-purple" (click)="closeMenu()">
      <span>Dossziék</span>
      <kesma-icon name="vg-chevron-diagonal-right"></kesma-icon>
    </a>
  </ng-container>

  <ng-container *ngFor="let dossier of dossiers">
    <div>
      <a [routerLink]="['/dosszie', dossier.slug]" class="mobile-menu-item child" (click)="closeMenu()">
        <span class="hover-underline">{{ dossier.title }}</span>
      </a>
    </div>
  </ng-container>

  <!-- MAIN MENU -->
  <ng-container *ngFor="let menuItem of mainMenu">
    <ng-container *ngTemplateOutlet="!menuItem.isCustomUrl ? basicLink : customUrl; context: { item: menuItem }"></ng-container>
  </ng-container>

  <app-social-box [styleID]="SocialBoxType.Menu"></app-social-box>

  <!-- FOOTER MENU-->
  <ng-container *ngIf="footerMenu?.length">
    <div class="footer-menu">
      <ng-container *ngTemplateOutlet="children; context: { children: footerMenu }"></ng-container>
    </div>
  </ng-container>
</nav>

<!-- Link templates -->
<ng-template #basicLink let-item="item" let-isChild="isChild">
  <a
    [routerLink]="item.link"
    [target]="item.target"
    class="mobile-menu-item"
    [class.child]="isChild"
    [class.hover-underline-purple]="!isChild && item.relatedType !== 'Dropdown'"
    [class.empty]="item.relatedType === 'Dropdown'"
    (click)="closeMenu()"
  >
    @if (item.relatedType === RelatedType.COLUMN && item?.related?.sponsorship; as sponsorship) {
      <div class="with-logo">
        <img [src]="sponsorship?.logo" loading="lazy" />
        <span [class.hover-underline]="isChild">{{ item.title }}</span>
      </div>
    } @else {
      <span [class.hover-underline]="isChild">{{ item.title }}</span>
    }

    <ng-container *ngIf="!isChild">
      <kesma-icon [name]="getIconName(item)" *ngIf="item.relatedType !== 'Dropdown'"></kesma-icon>
    </ng-container>
  </a>

  <ng-container *ngIf="item?.children?.length">
    <ng-container *ngTemplateOutlet="children; context: { children: item.children }"></ng-container>
  </ng-container>
</ng-template>

<ng-template #customUrl let-item="item" let-isChild="isChild">
  <a
    [href]="item.link"
    [target]="item.target"
    class="mobile-menu-item"
    [class.child]="isChild"
    [class.hover-underline-purple]="!isChild && item.relatedType !== 'Dropdown'"
    [class.empty]="item.relatedType === 'Dropdown'"
    (click)="closeMenu()"
  >
    <span [class.hover-underline]="isChild">{{ item.title }}</span>

    <ng-container *ngIf="!isChild">
      <kesma-icon [name]="getIconName(item)" *ngIf="item.relatedType !== 'Dropdown'"></kesma-icon>
    </ng-container>
  </a>

  <ng-container *ngIf="item?.children?.length">
    <ng-container *ngTemplateOutlet="children; context: { children: item.children }"></ng-container>
  </ng-container>
</ng-template>

<ng-template #children let-children="children">
  <ng-container *ngFor="let child of children">
    <div>
      <ng-container *ngTemplateOutlet="!child.isCustomUrl ? basicLink : customUrl; context: { item: child, isChild: true }"></ng-container>
    </div>
  </ng-container>
</ng-template>
