import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { DossierData, IconComponent, RelatedType, SimplifiedMenuItem, TrendingTag } from '@trendency/kesma-ui';
import { SocialBoxComponent, SocialBoxType } from '../../social-box/social-box.component';
import { Ng<PERSON><PERSON>, NgIf, NgTemplateOutlet, TitleCasePipe } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-mobile-menu',
  templateUrl: './mobile-menu.component.html',
  styleUrls: ['./mobile-menu.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [RouterLink, NgFor, NgIf, NgTemplateOutlet, SocialBoxComponent, TitleCasePipe, IconComponent],
})
export class MobileMenuComponent {
  @Input()
  set mainMenu(mainMenu: SimplifiedMenuItem[]) {
    this.categories = mainMenu.find((item) => item.title.toLowerCase() === 'rovatok')?.children || [];
    this.#mainMenu = mainMenu.filter((item) => item.title.toLowerCase() !== 'rovatok');
  }

  get mainMenu(): SimplifiedMenuItem[] {
    return this.#mainMenu;
  }

  @Input() footerMenu: SimplifiedMenuItem[] = [];
  @Input() tags: TrendingTag[] = [];
  @Input() dossiers: DossierData[] = [];
  @Input() menuState: boolean;
  @Output() menuStateChange = new EventEmitter<boolean>();

  #mainMenu: SimplifiedMenuItem[];
  categories: SimplifiedMenuItem[] = [];

  readonly SocialBoxType = SocialBoxType;
  readonly RelatedType = RelatedType;

  closeMenu(): void {
    this.menuStateChange.emit(false);
  }

  getIconName(menuItem: SimplifiedMenuItem): string {
    let icon = 'vg-chevron-diagonal-right';

    switch (menuItem.title?.toLowerCase()) {
      case 'podcastok':
      case 'podcast':
        icon = 'vg-podcast';
        break;
      case 'galériák':
      case 'galéria':
        icon = 'vg-gallery';
        break;
      case 'videók':
      case 'videó':
        icon = 'vg-video';
        break;
      case 'tudástár':
        icon = 'vg-tudastar';
        break;
      case 'kalkulátorok':
      case 'kalkulátor':
        icon = 'vg-calculator';
        break;
      case 'deviza árfolyamok':
      case 'deviza':
        icon = 'vg-deviza';
        break;
      case 'részvény árfolyamok':
      case 'részvény':
      case 'részvények':
        icon = 'vg-trending-lines';
        break;
      case 'napi hírlevél feliratkozás':
      case 'napi hírlevél':
      case 'gong előtt befektetői hírlevél':
      case 'befektetői hírlevél':
        icon = 'vg-email';
        break;
    }
    return icon;
  }
}
