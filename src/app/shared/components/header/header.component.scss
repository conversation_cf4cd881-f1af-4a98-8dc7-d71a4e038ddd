@use 'shared' as *;

:host {
  display: block;
  width: 100%;

  .desktop {
    @include media-breakpoint-down(md) {
      display: none;
    }
  }

  .mobile {
    display: none;

    @include media-breakpoint-down(md) {
      display: block;
    }
  }
  .mobile-menu {
    display: none;
    &-visible {
      display: block;
    }
  }

  vg-exchange-item {
    &::ng-deep .exchange-item {
      background: var(--kui-gray-950);
      border-color: var(--kui-deep-teal-400);

      &.down {
        border-color: var(--kui-red-500);
      }

      &.up {
        border-color: var(--kui-green-500);
      }
    }
  }

  ::ng-deep {
    .icon {
      width: 24px;
      height: 24px;
    }

    .icon-wrapper {
      position: relative;
      display: flex;
      flex: 0 0 auto;

      img,
      i {
        transition: 0.3s;

        &.hover {
          opacity: 0;
          position: absolute;
          //z-index: -1;

          &:hover {
            opacity: 1;
          }
        }

        &.basic {
          &:hover {
            opacity: 0;

            & + .hover {
              opacity: 1;
            }
          }
        }
      }
    }
  }

  .header {
    &-top {
      padding: 16px 60px;
      display: flex;
      gap: 16px;
      justify-content: space-between;
      align-items: center;
      background: var(--kui-gray-950);

      @include media-breakpoint-down(md) {
        padding: 16px 24px;
        flex-wrap: wrap;
      }
    }

    &-bottom {
      display: none;
      align-items: center;
      background: var(--kui-gray-900);
      flex-direction: column;

      @include media-breakpoint-down(md) {
        display: flex;
      }

      &-tags-wrapper {
        width: 100%;
        overflow-x: auto;
        display: flex;
        justify-content: center;
      }

      .header-tags {
        gap: 16px;
        padding: 12px 24px;
        order: 0;
        flex-basis: auto;

        li {
          &:not(:last-child):after {
            right: -8px;
          }
        }
      }

      &-exchange {
        display: flex;
        gap: 16px;
        align-items: center;
        justify-content: center;
        padding: 8px 24px;
        margin: 0 auto;

        &-wrapper {
          width: 100%;
          overflow-x: auto;
          display: flex;
        }
      }

      &-divider {
        width: 100%;
        height: 1px;
        background: var(--kui-gray-700);
      }
    }

    &-logo {
      height: 40px;
    }

    &-tags {
      display: flex;
      gap: 32px;
      align-items: center;
      overflow-x: auto;
      overflow-y: hidden;

      @include media-breakpoint-down(md) {
        order: 1;
        flex-basis: 100%;
      }

      li {
        position: relative;
        flex: 0 0 auto;

        &:not(:last-child):after {
          content: '';
          position: absolute;
          top: -1.5px;
          right: -16px;
          width: 1px;
          height: 24px;
          border-radius: 2px;
          background: var(--kui-gray-700);

          @include media-breakpoint-down(md) {
            right: -8px;
          }
        }
      }

      &-item {
        color: var(--kui-white);
        font-size: 14px;
        font-weight: 700;
        line-height: 21px;
      }
    }

    &-right {
      display: flex;
      align-items: center;
      gap: 16px;
      flex: 0 0 auto;

      & > *:not(vg-exchange-item) {
        display: flex;
        gap: 8px;
        padding: 8px;
        border-radius: 2px;
        border: 1px solid var(--kui-gray-900);
        transition: 0.3s;
        color: var(--kui-white);
        height: 42px;
        align-items: center;

        &:hover {
          border-color: var(--kui-deep-teal-400);

          span,
          kesma-icon {
            color: var(--kui-deep-teal-400);
          }
        }

        span {
          transition: 0.3s;
        }
      }

      vg-exchange-item {
        height: 42px;

        @include media-breakpoint-down(md) {
          display: none;
        }
      }

      .calculators {
        display: flex;
        align-items: center;
        gap: 8px;
        color: var(--kui-white);
        font-weight: 700;
        line-height: 22px;
      }

      app-social-share {
        border: 0;
        padding: 0;

        &::ng-deep {
          .share {
            color: var(--kui-white);
            font-size: 16px;
            line-height: 22px;
            padding: 9px 16px;
            border-color: var(--kui-gray-900);
            border-radius: 2px;

            .popup {
              width: 100%;
            }

            &:hover {
              background: inherit;
              border-color: var(--kui-deep-teal-400);
              color: var(--kui-deep-teal-400);

              & > kesma-icon {
                color: var(--kui-deep-teal-400);
              }
            }

            & > kesma-icon {
              color: var(--kui-white);
            }

            span {
              @include media-breakpoint-down(md) {
                display: none;
              }
            }

            @include media-breakpoint-down(md) {
              padding: 8px;
            }
          }
        }
      }
    }

    &.sticky {
      position: fixed;
      z-index: 9999;
      width: 100%;
      top: 0;
      animation: headerMove 1s;
    }
  }

  app-mobile-menu {
    position: fixed;
    z-index: 9999;
    right: 0;
    top: 0;
  }
}

.header-tags {
  overflow-x: scroll;
  -ms-overflow-style: none;
  scrollbar-width: none;
  text-transform: uppercase;

  &::-webkit-scrollbar {
    display: none;
  }
}

@keyframes headerMove {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}
