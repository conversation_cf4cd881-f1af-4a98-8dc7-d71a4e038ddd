<div #externalRecommendationsBlock class="external-recommendations">
  @for (article of externalRecommendations(); let i = $index; track article.id) {
    <div class="external-article">
      <vg-article-card [data]="article" [styleID]="ArticleCardType.ExternalRecommendation"></vg-article-card>
    </div>
    @if (i === 3) {
      <div class="full-row">
        @if (roadblock_ottboxextra(); as ad) {
          <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
        }
      </div>
    }
    @if (i === 5) {
      <div class="full-row-mobile">
        @if (mobilrectangle_ottboxextra(); as ad) {
          <kesma-advertisement-adocean [ad]="ad"></kesma-advertisement-adocean>
        }
      </div>
    }
  }
</div>
