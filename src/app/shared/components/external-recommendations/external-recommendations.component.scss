@use 'shared' as *;

.external-recommendations {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px 20px;
  margin-bottom: 32px;

  .full-row {
    grid-column: span 4;

    @include media-breakpoint-down(md) {
      display: none;
    }
  }

  .full-row-mobile {
    grid-column: span 2;

    @include media-breakpoint-up(md) {
      display: none;
    }
  }

  .external-article {
    margin-bottom: 8px;
    position: relative;

    &::after {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 0;
      width: calc(100% + 20px);
      height: 1px;
      background: var(--kui-gray-300);
    }

    &:nth-child(4n)::after {
      width: calc(100%);
    }

    @include media-breakpoint-down(md) {
      margin-bottom: 4px;
    }
  }

  @include media-breakpoint-down(md) {
    gap: 24px 16px;
  }

  @include media-breakpoint-down(md) {
    grid-template-columns: repeat(2, 1fr);

    .external-article {
      &:nth-child(2n)::after {
        width: calc(100%);
      }
    }
  }

  @include media-breakpoint-down(sm) {
    grid-template-columns: repeat(2, 1fr);

    .external-article {
      &:nth-child(2n)::after {
        width: calc(100%);
      }
    }
  }
}
