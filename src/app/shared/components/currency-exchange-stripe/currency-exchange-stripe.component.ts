import { ChangeDetectionStrategy, Component, ElementRef, ViewChild } from '@angular/core';
import { UtilService } from '@trendency/kesma-core';
import { Observable, tap } from 'rxjs';
import { FinancialLatestDataService } from '../../services';
import { RouterLink } from '@angular/router';
import { AsyncPipe, NgFor, NgIf } from '@angular/common';
import { VgExchangeItemData } from '../../definitions';
import { VgExchangeItemComponent } from '../vg-exchange-item/vg-exchange-item.component';
import { IconComponent } from '@trendency/kesma-ui';

@Component({
  selector: 'app-currency-exchange-stripe',
  templateUrl: './currency-exchange-stripe.component.html',
  styleUrls: ['./currency-exchange-stripe.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, RouterLink, NgFor, AsyncPipe, VgExchangeItemComponent, IconComponent],
})
export class CurrencyExchangeStripeComponent {
  latestData$: Observable<VgExchangeItemData[]> = this.financialLatestDataService.latestCurrencyData$.pipe(tap(() => this.startSlideShow()));

  @ViewChild('stripeWrapper', { static: false }) stripeWrapper: ElementRef<HTMLDivElement>;
  @ViewChild('stripe', { static: false }) stripe: ElementRef<HTMLDivElement>;

  constructor(
    private readonly utilsService: UtilService,
    private readonly financialLatestDataService: FinancialLatestDataService
  ) {}

  startSlideShow(): void {
    if (this.utilsService.isBrowser()) {
      const delay = 1000;
      const showUpdateOverlay = false;

      if (this.stripeWrapper?.nativeElement && showUpdateOverlay) {
        this.stripeWrapper.nativeElement.classList.add('update');
      }

      setTimeout(() => {
        // Start animation, if not started already
        if (!this.stripe.nativeElement.classList.contains('animate')) {
          this.stripe.nativeElement.classList.add('animate');
        }

        if (showUpdateOverlay) {
          this.stripeWrapper.nativeElement.classList.remove('update');
        }
      }, delay);
    }
  }

  removeMobilePausedStateWithDelay(): void {
    if (this.utilsService.isBrowser()) {
      setTimeout(() => {
        this.stripe.nativeElement.classList.remove('paused');
      }, 1000);
    }
  }
}
