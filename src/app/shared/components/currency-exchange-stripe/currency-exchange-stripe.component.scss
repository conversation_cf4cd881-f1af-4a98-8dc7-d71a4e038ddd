@use 'shared' as *;

.currency-exchange-stripe {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 60px;
  gap: 20px;
  background: var(--kui-gray-900);

  @include media-breakpoint-down(md) {
    padding: 8px 24px;
    gap: 16px;
  }

  .label {
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 700;
    line-height: 18px;
    transition: 0.3s;
    border: 1px solid var(--kui-deep-teal-400);
    border-radius: 2px;
    color: var(--kui-white);
    flex: 0 0 auto;

    @media (hover: hover) {
      &:hover {
        background: var(--kui-deep-teal-400);
      }
    }

    span {
      @include media-breakpoint-down(md) {
        display: none;
      }
    }

    kesma-icon {
      display: none;

      @include media-breakpoint-down(md) {
        display: block;
      }
    }

    @include media-breakpoint-down(md) {
      padding: 3px;
    }
  }

  // iOS css animation flickering fix
  * {
    backface-visibility: hidden;
    transform-style: preserve-3d;
  }

  .exchange-stripe {
    display: inline-flex;
    align-items: center;
    position: relative;
    transition: 0.3s linear opacity;

    @keyframes slideshow {
      to {
        transform: translateX(-50%);
      }
    }

    &.animate {
      animation: slideshow 50s linear infinite;
    }

    @media (hover: hover) {
      &:hover {
        animation-play-state: paused;
      }
    }

    vg-exchange-item {
      margin-right: 20px;
    }

    &.paused {
      animation-play-state: paused;
    }

    &-wrapper {
      overflow: hidden;
      position: relative;

      &:before {
        content: 'Frissítés...';
        display: flex;
        position: absolute;
        width: 100%;
        height: 100%;
        text-align: center;
        justify-content: center;
        align-items: center;
        opacity: 0;
        transition: 0.3s linear opacity;
        color: var(--kui-white);
      }

      &.update {
        &:before {
          opacity: 1;
        }

        > div {
          animation-play-state: paused;
          opacity: 0;
        }
      }
    }
  }
}
