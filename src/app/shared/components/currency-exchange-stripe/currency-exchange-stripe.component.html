<div *ngIf="latestData$ | async as latestData" class="currency-exchange-stripe">
  <a class="label" routerLink="/deviza-adatok">
    <span>Deviza</span>
    <kesma-icon name="vg-deviza"></kesma-icon>
  </a>
  <div #stripeWrapper class="exchange-stripe-wrapper">
    <div #stripe (touchend)="removeMobilePausedStateWithDelay()" (touchstart)="stripe.classList.add('paused')" class="exchange-stripe">
      <!-- Stripe elements (display twice to create a nice loop with css animation) -->
      <ng-container *ngFor="let item of [].constructor(2)">
        <vg-exchange-item *ngFor="let data of latestData" [data]="data"></vg-exchange-item>
      </ng-container>
    </div>
  </div>
</div>
