import { ChangeDetectionStrategy, Component } from '@angular/core';
import { HeaderImageComponent } from '@trendency/kesma-ui';
import { NgIf, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'vg-header-image',
  templateUrl: 'vg-header-image.component.html',
  styleUrls: [
    '../../../../../node_modules/@trendency/kesma-ui/src/lib/components/header-image/header-image.component.scss',
    './vg-header-image.component.scss',
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgTemplateOutlet],
})
export class VgHeaderImageComponent extends HeaderImageComponent {}
