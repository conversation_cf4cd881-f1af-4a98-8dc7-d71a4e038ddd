<a *ngIf="data?.url; else contentTemplate" [href]="data?.url" target="_blank">
  <ng-container *ngTemplateOutlet="contentTemplate"></ng-container>
</a>

<ng-template #contentTemplate>
  <div [style.background-image]="'url(' + data?.backgroundImage + ')'" [style.min-height.px]="data?.height" class="header-image">
    <div class="content-wrap">
      <section>
        <div class="wrapper">
          <ng-container *ngIf="data?.logo">
            <img [src]="data?.logo" class="header-image-logo" />
          </ng-container>

          <div class="header-image-separator"></div>

          <div class="header-image-title">{{ data?.title }}</div>
        </div>
      </section>
    </div>
  </div>
</ng-template>
