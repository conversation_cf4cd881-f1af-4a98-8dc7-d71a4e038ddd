@use 'shared' as *;

:host {
  display: block;
  position: relative;

  .header-image {
    background-size: cover;
    height: auto;
    width: 100%;
    display: flex;
    padding: 0;

    @include media-breakpoint-down(md) {
      padding: 15px 22px;
    }

    .content-wrap {
      width: calc(100% - 140px);
      margin: auto;

      @include media-breakpoint-down(lg) {
        width: 100%;
      }
    }

    .wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 32px;

      @include media-breakpoint-down(md) {
        flex-direction: column;
        gap: 16px;
      }
    }

    &-separator {
      width: 3px;
      height: 24px;
      background: var(--kui-gray-950);
      flex: 0 0 auto;

      @include media-breakpoint-down(md) {
        display: none;
      }
    }

    &-title {
      color: var(--kui-gray-950);
      font-size: 32px;
      font-weight: 500;
      line-height: normal;
      text-align: center;
    }
  }
}
