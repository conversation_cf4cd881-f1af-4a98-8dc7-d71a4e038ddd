<vg-vidcast-podcast-block-title [data]="blockTitleData" [isSidebar]="isSidebar" [desktopWidth]="desktopWidth"></vg-vidcast-podcast-block-title>

<div class="podcast-wrapper" [class.tablet-wrap]="!isSidebar">
  <ng-container *ngFor="let article of data; trackBy: trackByFn">
    <vg-article-card
      [data]="article"
      [styleID]="ArticleCardType.Podcast"
      [isSidebar]="isSidebar"
      [desktopWidth]="desktopWidth"
      [isLight]="isLight"
      [isInPodcastBox]="true"
      [isPodcastBoxOnArticlePage]="isPodcastBoxOnArticlePage"
      *ngIf="article"
    ></vg-article-card>
  </ng-container>
</div>
