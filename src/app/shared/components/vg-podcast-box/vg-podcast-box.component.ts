import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { ArticleCard, BaseComponent } from '@trendency/kesma-ui';
import { ArticleCardType } from '../../definitions';
import { VgVidcastPodcastBlockTitle, VgVidcastPodcastBlockTitleComponent } from '../vg-vidcast-podcast-block-title/vg-vidcast-podcast-block-title.component';
import { VgArticleCardComponent } from '../vg-article-card/vg-article-card.component';
import { NgFor, NgIf } from '@angular/common';

@Component({
  selector: 'vg-podcast-box',
  templateUrl: './vg-podcast-box.component.html',
  styleUrls: ['./vg-podcast-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [VgVidcastPodcastBlockTitleComponent, NgFor, NgIf, VgArticleCardComponent],
})
export class VgPodcastBoxComponent extends BaseComponent<ArticleCard[]> {
  @HostBinding('class') hostClass = '';
  @Input() blockTitleData: VgVidcastPodcastBlockTitle = {
    isDark: true,
    url: '/podcast',
  };

  @Input() isSidebar = false;
  @Input() desktopWidth = 12;
  @Input() isLight = false;
  @Input() isPodcastBoxOnArticlePage = false;

  readonly ArticleCardType = ArticleCardType;

  override ngOnInit(): void {
    this.blockTitleData = {
      ...this.blockTitleData,
      isDark: this.isLight,
    };

    if (this.desktopWidth < 6 || this.isSidebar) {
      this.hostClass = `mobile`;
    }
  }
}
