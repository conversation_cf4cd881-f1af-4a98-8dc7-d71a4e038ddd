@use 'shared' as *;

:host {
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 24px;

  @include media-breakpoint-down(sm) {
    gap: 16px;
  }

  @include media-breakpoint-down(xs) {
    background-color: var(--kui-gray-950);
    width: calc(100% + 48px);
    margin-left: -24px;
    padding: 15px 24px;
  }

  .podcast-wrapper {
    display: flex;
    gap: 24px;

    &.tablet-wrap {
      @include media-breakpoint-between(md, md) {
        flex-wrap: wrap;
        justify-content: space-evenly;
        &::ng-deep {
          vg-article-card {
            width: 200px;
          }
        }
      }
    }

    @include media-breakpoint-down(sm) {
      flex-direction: column;
    }
  }

  ::ng-deep {
    article {
      height: 100%;
    }
  }

  &.mobile {
    padding: 15px 24px;
    gap: 16px;

    .podcast-wrapper {
      flex-direction: column;
    }
  }
}
