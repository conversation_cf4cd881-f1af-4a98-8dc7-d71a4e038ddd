import { ChangeDetectionStrategy, Component } from '@angular/core';
import { ArticleCard, BaseComponent, IconComponent } from '@trendency/kesma-ui';
import { ArticleCardType } from '../../definitions';
import { RouterLink } from '@angular/router';
import { VgArticleCardComponent } from '../vg-article-card/vg-article-card.component';
import { NgFor } from '@angular/common';

@Component({
  selector: 'vg-latest-news',
  templateUrl: './vg-latest-news.component.html',
  styleUrls: ['./vg-latest-news.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgFor, VgArticleCardComponent, RouterLink, IconComponent],
})
export class VgLatestNewsComponent extends BaseComponent<ArticleCard[]> {
  articleCardType = ArticleCardType;
}
