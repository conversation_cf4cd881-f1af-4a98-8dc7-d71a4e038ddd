:host {
  display: block;
}

.latest-news {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 500px;
  overflow-y: auto;

  &-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  &-title {
    color: var(--kui-deep-teal-900);
    margin-left: 32px;
  }

  &-item {
    display: flex;
    gap: 8px;
    align-items: flex-start;
    transition: 0.3s;
    border: 1px solid var(--kui-white);
    padding-bottom: 8px;

    &:last-child {
      padding-bottom: 0;
    }

    kesma-icon {
      color: var(--kui-deep-teal-400);
    }

    &:hover {
      border: 1px solid var(--kui-deep-teal-400);
    }
  }

  &-all {
    display: flex;
    justify-content: space-between;
    padding: 8px 20px;
    border-bottom: 1px solid var(--kui-gray-950);
    cursor: pointer;
    transition: 0.3s;

    kesma-icon {
      transition: 0.3s;
    }

    .latest-news-all-title {
      transition: 0.3s;
    }

    &:hover {
      border-color: var(--kui-amethyst-500);

      kesma-icon {
        color: var(--kui-amethyst-500);
      }

      .latest-news-all-title {
        color: var(--kui-amethyst-500);
      }
    }
  }

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--kui-white);
    border: 1px solid var(--kui-gray-100);
    border-radius: 50px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--kui-gray-950);
    border-radius: 50px;
  }
}
