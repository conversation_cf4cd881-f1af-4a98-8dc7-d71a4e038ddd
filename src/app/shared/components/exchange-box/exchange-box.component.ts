import { ChangeDetectionStrategy, Component, HostBinding, Input } from '@angular/core';
import { ExchangeBoxDataType, ExchangeBoxType } from '@trendency/kesma-ui';
import { ExchangeTableBoxComponent } from '../exchange-table-box/exchange-table-box.component';
import { ExchangeChartBoxComponent } from '../exchange-chart-box/exchange-chart-box.component';
import { NgClass, NgIf } from '@angular/common';

@Component({
  selector: 'app-exchange-box',
  templateUrl: './exchange-box.component.html',
  styleUrls: ['./exchange-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, ExchangeChartBoxComponent, NgClass, ExchangeTableBoxComponent],
})
export class ExchangeBoxComponent {
  @Input() dataType: ExchangeBoxDataType = ExchangeBoxDataType.STOCK;
  @Input() @HostBinding('class') styleId: ExchangeBoxType = ExchangeBoxType.ChartAndTableColumn;
  @Input() border = false;

  ExchangeBoxType = ExchangeBoxType;
}
