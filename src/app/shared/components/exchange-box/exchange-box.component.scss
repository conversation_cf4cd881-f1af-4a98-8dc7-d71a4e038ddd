@use 'shared' as *;

:host {
  .border {
    display: block;
    border: 1px solid var(--kui-gray-700);
    position: relative;
  }

  &.ChartAndTableRow,
  &.ChartAndTableColumn {
    display: flex;
    gap: 16px;
  }

  &.ChartAndTableRow {
    @include media-breakpoint-down(sm) {
      flex-direction: column;
    }

    > * {
      width: 50%;

      @include media-breakpoint-down(sm) {
        width: 100%;
      }
    }
  }

  &.ChartAndTableColumn {
    flex-direction: column;
  }
}
