<ng-container *ngIf="data && data.length >= 3">
  <vg-block-title [data]="blockTitle" [desktopWidth]="desktopWidth" [headingLevel]="2" [isFullWidth]="true"></vg-block-title>

  <div class="opinion-block">
    <div class="opinion-block-main">
      <vg-article-card [data]="data?.[0]" [styleID]="articleCardType.OpinionMain"></vg-article-card>
    </div>
    <div class="opinion-block-secondary">
      <vg-article-card [data]="data?.[1]" [displayLinkIcon]="true" [styleID]="articleCardType.OpinionSecondary"></vg-article-card>
      <div class="separator"></div>
      <vg-article-card [data]="data?.[2]" [displayLinkIcon]="true" [styleID]="articleCardType.OpinionSecondary"></vg-article-card>
      <div class="separator last"></div>
    </div>
  </div>
</ng-container>
