import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { ArticleCard, BaseComponent, BlockTitle } from '@trendency/kesma-ui';
import { ArticleCardType } from '../../definitions';
import { VgArticleCardComponent } from '../vg-article-card/vg-article-card.component';
import { VgBlockTitleComponent } from '../vg-block-title/vg-block-title.component';
import { NgIf } from '@angular/common';

@Component({
  selector: 'vg-opinion-block',
  templateUrl: './vg-opinion-block.component.html',
  styleUrls: ['./vg-opinion-block.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, VgBlockTitleComponent, VgArticleCardComponent],
})
export class VgOpinionBlockComponent extends BaseComponent<ArticleCard[]> {
  @Input() blockTitle: BlockTitle = {
    text: 'Vélemény',
    url: '/velemeny',
    urlName: 'Továbbiak',
    isDark: true,
  };
  @Input() desktopWidth = 12;
  articleCardType = ArticleCardType;
}
