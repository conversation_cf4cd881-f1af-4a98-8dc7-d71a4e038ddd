@use 'shared' as *;

:host {
  display: block;
  width: 100%;
  background-color: var(--kui-deep-teal-950);
  padding: 16px 0;
  position: relative;

  @include media-breakpoint-down(lg) {
    padding: 24px;
  }

  @include media-breakpoint-down(lg) {
    margin-left: -15px;
    margin-right: -15px;
    width: calc(100% + 30px);
  }

  .opinion-block {
    margin-top: 40px;
    width: 100%;
    display: flex;
    gap: 24px;

    @include media-breakpoint-down(md) {
      flex-direction: column;
      margin-top: 16px;
    }

    &-main {
      display: flex;
      gap: 24px;
      width: 65%;
      @include media-breakpoint-down(md) {
        width: 100%;
      }
    }

    &-secondary {
      display: flex;
      flex-direction: column;
      width: 35%;

      @include media-breakpoint-down(md) {
        width: 100%;
      }
    }

    .separator {
      display: block;
      width: 100%;
      height: 1px;
      background-color: var(--kui-deep-teal-700);
      margin-bottom: 16px;

      @include media-breakpoint-down(md) {
        margin-top: 0;
      }

      &.last {
        margin-bottom: 0;
      }
    }
  }
}
