<ng-container *ngIf="!data?.url; else hasUrl">
  <ng-container *ngTemplateOutlet="sponsorData"></ng-container>
  <ng-container *ngTemplateOutlet="heading"></ng-container>
</ng-container>

<ng-template #hasUrl>
  <ng-container *ngTemplateOutlet="sponsorData"></ng-container>
  <a class="block" *ngIf="!isExternal; else isExternalTemplate" [routerLink]="data?.url">
    <ng-container *ngTemplateOutlet="heading"></ng-container>
    <div
      class="block-url"
      [class.desktop]="hideUrlOnMobile"
      [class.block-url-wide]="desktopWidth > 4 && !isSponsored"
      [class.block-url-sponsored-wide]="desktopWidth > 4 && isSponsored"
    >
      <span>{{ data?.urlName || 'Továbbiak' }}</span> <kesma-icon name="vg-chevron-diagonal-right" [size]="16"></kesma-icon>
    </div>
  </a>

  <ng-template #isExternalTemplate>
    <a class="block" [href]="data?.url" target="_blank" [class.dark-basded]="data?.isDark">
      <ng-container *ngTemplateOutlet="heading"></ng-container>
      <div class="block-url" [class.desktop]="hideUrlOnMobile" [class.block-url-wide]="desktopWidth > 4">
        {{ data?.urlName || 'Továbbiak' }} <kesma-icon name="vg-chevron-diagonal-right" [size]="16"></kesma-icon>
      </div>
    </a>
  </ng-template>
</ng-template>

<ng-template #heading>
  <ng-container [ngSwitch]="headingLevel">
    <h1 *ngSwitchCase="1" class="block-title">
      <kesma-icon *ngIf="data?.icon" [name]="data?.icon!" [size]="24"></kesma-icon>
      {{ data?.text }}
    </h1>
    <h2 *ngSwitchCase="2" class="block-title">
      <kesma-icon *ngIf="data?.icon" [name]="data?.icon!" [size]="24"></kesma-icon>
      {{ data?.text }}
    </h2>
    <h3 *ngSwitchCase="3" class="block-title">
      <kesma-icon *ngIf="data?.icon" [name]="data?.icon!" [size]="18"></kesma-icon>
      {{ data?.text }}
    </h3>
    <h4 *ngSwitchCase="4" class="block-title">
      <kesma-icon *ngIf="data?.icon" [name]="data?.icon!" [size]="16"></kesma-icon>
      {{ data?.text }}
    </h4>
  </ng-container>
</ng-template>

<ng-template #sponsorData>
  <ng-container *ngIf="hasSponsorship">
    <ng-container *ngIf="data?.selectedSponsorship?.url as url; else noSponsorUrl">
      <a [href]="url" target="_blank">
        <ng-container *ngTemplateOutlet="sponsorThumbnail"></ng-container>
      </a>
    </ng-container>
    <ng-template #noSponsorUrl>
      <ng-container *ngTemplateOutlet="sponsorThumbnail"></ng-container>
    </ng-template>
    <div *ngIf="data?.selectedSponsorship?.thumbnailUrl" class="separator"></div>
  </ng-container>
</ng-template>

<ng-template #sponsorThumbnail>
  <img
    *ngIf="hasSponsorship && data?.selectedSponsorship?.thumbnailUrl"
    class="sponsor-thumbnail"
    [style.height]="desktopWidth <= 4 && 'auto'"
    [alt]="data?.selectedSponsorship?.title"
    [src]="data?.selectedSponsorship?.thumbnailUrl"
  />
</ng-template>
