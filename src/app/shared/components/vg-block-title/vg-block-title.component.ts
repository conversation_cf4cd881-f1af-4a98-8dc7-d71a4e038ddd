import { ChangeDetectionStrategy, Component, HostBinding, Input, OnInit } from '@angular/core';
import { BlockTitleRowComponent, IconComponent } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { NgIf, NgSwitch, NgSwitchCase, NgTemplateOutlet } from '@angular/common';

@Component({
  selector: 'vg-block-title',
  templateUrl: './vg-block-title.component.html',
  styleUrls: ['./vg-block-title.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgTemplateOutlet, RouterLink, NgSwitch, NgSwitchCase, IconComponent],
})
export class VgBlockTitleComponent extends BlockTitleRowComponent implements OnInit {
  /**
   * Heading level to use for the title.
   * Ranges from 1 to 4.
   * @default 3
   * @type {number}
   */
  @Input() headingLevel = 3;

  @Input() @HostBinding('class.bold') isBold = false;
  @Input() @HostBinding('class.only-url') onlyUrl = false;
  @Input() hideUrlOnMobile = false;
  @HostBinding('class.sponsored-block-title') isSponsored = false;
  @HostBinding('class.is-dark') isDark = false;
  @HostBinding('attr.style') hostStyle = '';

  get hasSponsorship(): boolean {
    return !!(!Array.isArray(this.data?.selectedSponsorship) && this.data?.selectedSponsorship && this.data?.selectedSponsorship?.title);
  }

  override ngOnInit(): void {
    if (this.hasSponsorship) {
      this.isSponsored = true;
      this.hostStyle = `--sponsored-color: var(--kui-sponsor-block-title-color);
       --sponsored-background-color: var(--kui-white)`;
    }
    this.isDark = this.data?.isDark || false;
  }
}
