@use 'shared' as *;

:host {
  display: flex;
  align-items: center;
  width: 100%;

  h1 {
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    letter-spacing: 0.92px;
  }

  h2 {
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    letter-spacing: 0.92px;

    @include media-breakpoint-down(md) {
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      letter-spacing: 0.36px;
    }
  }

  h3 {
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    letter-spacing: 0.36px;
  }

  h4 {
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 22px;
  }

  h5 {
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
  }

  .block {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;
    gap: 20px;

    &-title {
      display: flex;
      align-items: center;
      gap: 8px;
      color: var(--kui-deep-teal-900);
      padding: 8px 0;
    }

    &-url {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 0;
      width: 112px; // Should change on portal side if it's needed
      color: var(--kui-gray-950);
      border-bottom: 1px solid var(--kui-gray-950);

      &-wide {
        max-width: 312px;
        width: 100%;
        @include media-breakpoint-down(sm) {
          width: 112px;
        }
      }

      &-sponsored-wide {
        max-width: 312px;
        width: 100%;
      }

      span {
        font-size: 14px;
        font-weight: 400;
        line-height: 18px;
      }

      .icon {
        width: 16px;
        height: 16px;
        filter: var(--kui-filter-gray-950);
      }
    }

    &:hover {
      .block-title {
        color: var(--kui-amethyst-500);
      }

      .block-url {
        border-color: var(--kui-amethyst-500);
        color: var(--kui-amethyst-500);
      }

      .icon {
        filter: var(--kui-filter-amethyst-500);
      }
    }
  }

  &.bold {
    .block-url {
      &,
      span {
        font-weight: bold;
      }
    }
  }

  &.only-url {
    .block-title {
      display: none;
    }

    .block-url {
      min-width: 100%;

      &-wide span {
        font-size: 16px;
        line-height: 22px;
      }
    }
  }

  &.is-dark {
    .block-title {
      color: var(--kui-white);
    }

    .block-url {
      color: var(--kui-white);
      border-color: var(--kui-white);

      span {
        font-size: 16px;
        font-weight: 700;
        line-height: 22px;
      }
    }

    .icon {
      filter: var(--kui-filter-white);
    }
  }

  &.sponsored-block-title {
    margin-bottom: 16px;
    padding: 0px 8px;
    border: 1px solid var(--sponsored-color);
    background-color: var(--sponsored-background-color);

    @include media-breakpoint-down(sm) {
      padding: 8px;
      flex-direction: row-reverse;
      justify-content: space-between;
    }

    .block {
      &-title {
        font-weight: 500;
        color: var(--sponsored-color);
        @include media-breakpoint-down(sm) {
          font-weight: 700;
          padding: 0;
        }
      }

      &-url {
        color: var(--kui-gray-950);
        border-color: var(--kui-gray-950);
        @include media-breakpoint-down(sm) {
          display: none;
        }
      }
    }
  }

  @include media-breakpoint-down(lg) {
    .desktop {
      display: none;
    }
  }
}

.sponsor {
  &-thumbnail {
    max-width: 105px;
    width: 100%;
    height: 28px;
    object-fit: cover;

    @include media-breakpoint-down(sm) {
      max-width: 136px;
      width: 100%;
      height: 36px;
    }
  }
}

.separator {
  width: 1px;
  height: 24px;
  background-color: var(--sponsored-color);
  margin-inline: 16px;
  flex: 0 0 auto;

  @include media-breakpoint-down(sm) {
    display: none;
  }
}

kesma-icon {
  min-width: 16px;
}
