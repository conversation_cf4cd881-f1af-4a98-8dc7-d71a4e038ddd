import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ElementRef,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  Output,
  SimpleChanges,
  ViewChild,
} from '@angular/core';
import { BehaviorSubject, combineLatest, forkJoin, Observable, of, Subject, tap } from 'rxjs';
import { distinctUntilChanged, map, switchMap, takeUntil } from 'rxjs/operators';
import { createChart, IChartApi, ISeriesApi, TickMarkType, UTCTimestamp } from 'lightweight-charts';
import {
  convertUTCTimestampToDate,
  financialAPICurrencyChartDataMapper,
  financialAPIStockChartDataMapper,
  financialAPIStockIndexChartDataMapper,
  getStockExchangeLink,
  isStockDailyDataAvailable,
} from '../../utils';
import {
  CurrencyChartType,
  exchangeChartBoxDefaultConfig,
  exchangeChartBoxSeriesConfig,
  FinancialAPICurrencyDateAggregatedData,
  FinancialAPICurrencyDateData,
  FinancialAPIStockGraphData,
  FinancialAPIStockGraphResult,
  FinancialAPIStockIndexGraphData,
  FinancialAPIStockIndexGraphResult,
  StockChartType,
  StockIndexChartType,
  VgExchangeItemData,
} from '../../definitions';
import { CurrencyExchangeService, FinancialLatestDataService, StockExchangeService } from '../../services';
import { format, isWeekend } from 'date-fns';
import { UtilService } from '@trendency/kesma-core';
import { ExchangeBoxDataType, IconComponent } from '@trendency/kesma-ui';
import { RouterLink } from '@angular/router';
import { AsyncPipe, NgClass, NgIf } from '@angular/common';
import { VgSpinnerComponent } from '../vg-spinner/vg-spinner.component';
import { VgExchangeItemComponent } from '../vg-exchange-item/vg-exchange-item.component';

@Component({
  selector: 'app-exchange-chart-box',
  templateUrl: './exchange-chart-box.component.html',
  styleUrls: ['./exchange-chart-box.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, NgClass, RouterLink, AsyncPipe, VgSpinnerComponent, VgExchangeItemComponent, IconComponent],
})
export class ExchangeChartBoxComponent implements OnChanges, AfterViewInit, OnDestroy {
  @Input() dataType: ExchangeBoxDataType = ExchangeBoxDataType.STOCK;
  @Output() dataTypeChange: EventEmitter<ExchangeBoxDataType> = new EventEmitter<ExchangeBoxDataType>();
  @ViewChild('chartContainer', { static: false }) chartContainer: ElementRef<HTMLDivElement>;

  dataType$: BehaviorSubject<ExchangeBoxDataType> = new BehaviorSubject<ExchangeBoxDataType>(ExchangeBoxDataType.STOCK);

  selectedStock$: BehaviorSubject<number> = new BehaviorSubject<number>(0);

  availableStocks: VgExchangeItemData[] = [];
  stockData$: Observable<VgExchangeItemData | undefined> = combineLatest([
    this.selectedStock$.pipe(distinctUntilChanged()),
    this.financialLatestDataService.latestStockData$,
  ]).pipe(
    map(([index, data]) => {
      this.availableStocks = data;
      return data[index];
    })
  );

  selectedCurrency$: BehaviorSubject<string> = new BehaviorSubject<string>(this.financialLatestDataService.availableCurrencies[0]);

  currencyData$: Observable<VgExchangeItemData | undefined> = combineLatest([
    this.selectedCurrency$.pipe(distinctUntilChanged()),
    this.financialLatestDataService.latestCurrencyData$,
  ]).pipe(map(([currency, data]) => data.find((data: VgExchangeItemData) => data.id === currency)));

  chart: IChartApi;
  dataSeries: ISeriesApi<'Area'>;
  isChartLoaded = false;
  isChartDataLoading = false;
  isChartEmpty = false;

  unsubscribe$: Subject<void> = new Subject<void>();

  ExchangeBoxDataType = ExchangeBoxDataType;

  getStockExchangeLink = getStockExchangeLink;

  constructor(
    private readonly stockExchangeService: StockExchangeService,
    private readonly currencyExchangeService: CurrencyExchangeService,
    private readonly financialLatestDataService: FinancialLatestDataService,
    private readonly cdr: ChangeDetectorRef,
    private readonly utilsService: UtilService
  ) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['dataType']) {
      this.dataType$.next(this.dataType);
    }
  }

  ngAfterViewInit(): void {
    if (this.utilsService.isBrowser()) {
      this.handleChartData();
    }
  }

  initChart(): void {
    this.chart = createChart(this.chartContainer.nativeElement, exchangeChartBoxDefaultConfig);
    this.dataSeries = this.chart.addAreaSeries(exchangeChartBoxSeriesConfig);
  }

  handleChartData(): void {
    combineLatest([
      this.dataType$.pipe(distinctUntilChanged()),
      this.stockData$.pipe(distinctUntilChanged()),
      this.selectedCurrency$.pipe(distinctUntilChanged()),
    ])
      .pipe(
        tap(() => {
          this.isChartDataLoading = true;
        }),
        switchMap(([dataType, stock, currency]) =>
          forkJoin([
            of(stock),
            dataType === ExchangeBoxDataType.STOCK && stock
              ? isStockDailyDataAvailable()
                ? stock.type === 'Index'
                  ? this.stockExchangeService
                      .getStockIndexGraphData(stock.id, stock.id === 'CETO' ? StockIndexChartType.MONTH : StockIndexChartType.DAY)
                      .pipe(map((result: FinancialAPIStockIndexGraphResult) => result.data))
                  : this.stockExchangeService.getStockGraphData(stock.id, StockChartType.DAY).pipe(map((result: FinancialAPIStockGraphResult) => result.data))
                : of(null)
              : this.currencyExchangeService.getGraphData(isWeekend(new Date()) ? CurrencyChartType.WEEK : CurrencyChartType.DAY, currency),
          ])
        ),
        takeUntil(this.unsubscribe$)
      )
      .subscribe(([stock, chartData]) => {
        if (!this.chart) {
          this.isChartLoaded = true;
          this.cdr.detectChanges();
          this.initChart();
        }

        if (this.dataType$.value === ExchangeBoxDataType.STOCK && stock) {
          if (stock.type === 'Index') {
            const data = chartData as FinancialAPIStockIndexGraphData;
            this.dataSeries.setData(financialAPIStockIndexChartDataMapper(data?.points ?? []));
            this.isChartEmpty = (data?.points?.filter((p) => !!p.currentValue)?.length ?? 0) === 0;
          } else {
            const data = chartData as FinancialAPIStockGraphData;
            this.dataSeries.setData(financialAPIStockChartDataMapper(data?.points ?? []));
            this.isChartEmpty = (data?.points?.filter((p) => !!p.lastPrice)?.length ?? 0) === 0;
          }
        } else {
          const data = chartData as FinancialAPICurrencyDateData[] | FinancialAPICurrencyDateAggregatedData[];
          this.dataSeries.setData(financialAPICurrencyChartDataMapper(data ?? [], isWeekend(new Date()) ? CurrencyChartType.WEEK : CurrencyChartType.DAY));
          this.isChartEmpty = (data?.length ?? 0) === 0;
        }

        this.applyChartTimeFormats(this.dataType$.value, stock);
        this.chart.timeScale().fitContent();

        if (this.utilsService.isBrowser()) {
          setTimeout(() => {
            this.isChartDataLoading = false;
            this.cdr.detectChanges();
          }, 300);
        } else {
          this.isChartDataLoading = false;
        }

        this.cdr.detectChanges();
      });
  }

  applyChartTimeFormats(tabType: ExchangeBoxDataType | undefined, stock?: VgExchangeItemData): void {
    this.chart.applyOptions({
      timeScale: {
        // Format datetime labels on x axis
        tickMarkFormatter: (timestamp: UTCTimestamp, tickMarkType: TickMarkType) => {
          const date: Date = convertUTCTimestampToDate(timestamp);

          if ((tabType === ExchangeBoxDataType.STOCK && stock?.id === 'CETO') || (tabType === ExchangeBoxDataType.CURRENCY && isWeekend(new Date()))) {
            if (tickMarkType === TickMarkType.Time) {
              return '';
            }
            return format(date, 'MM.dd.');
          } else {
            return format(date, 'HH:mm');
          }
        },
      },
    });
  }

  changeDataType(type: ExchangeBoxDataType): void {
    this.dataType$.next(type);
    this.dataTypeChange.next(type);
  }

  changeItems(direction: 'prev' | 'next'): void {
    const availableItems: string[] =
      this.dataType$.value === ExchangeBoxDataType.STOCK
        ? this.availableStocks.map((stock: VgExchangeItemData) => stock.id)
        : this.financialLatestDataService.availableCurrencies;

    const currentIndex: number =
      this.dataType$.value === ExchangeBoxDataType.STOCK
        ? this.selectedStock$.value
        : availableItems.findIndex((currency) => currency === this.selectedCurrency$.value);
    const nextIndex: number =
      direction === 'prev'
        ? currentIndex > 0
          ? currentIndex - 1
          : availableItems.length - 1
        : currentIndex >= availableItems.length - 1
          ? 0
          : currentIndex + 1;

    if (this.dataType$.value === ExchangeBoxDataType.STOCK) {
      this.selectedStock$.next(nextIndex);
    } else {
      this.selectedCurrency$.next(availableItems[nextIndex]);
    }
  }

  ngOnDestroy(): void {
    this.unsubscribe$.next();
    this.unsubscribe$.complete();
  }
}
