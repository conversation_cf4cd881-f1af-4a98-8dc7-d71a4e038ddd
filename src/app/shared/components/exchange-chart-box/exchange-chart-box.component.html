<div class="exchange-box">
  <div class="exchange-box-header">
    <img alt="BÉT logó" src="assets/images/icons/bet.svg" />
    <div class="bet-text">Budapesti Értéktőzsde</div>
  </div>
  <div *ngIf="dataType$ | async as dataType" class="exchange-box-content">
    <div class="exchange-box-tabs">
      <div (click)="changeDataType(ExchangeBoxDataType.STOCK)" [ngClass]="{ active: dataType === ExchangeBoxDataType.STOCK }" class="exchange-box-tab">
        Részvénypiac
      </div>
      <div
        (click)="changeDataType(ExchangeBoxDataType.CURRENCY)"
        [ngClass]="{ active: dataType === ExchangeBoxDataType.CURRENCY }"
        class="exchange-box-tab active"
      >
        Deviza árfolyam
      </div>
    </div>
    <div class="exchange-box-tab-data">
      <div class="exchange-box-items">
        <button (click)="changeItems('prev')" class="exchange-box-button left" aria-label="Előző">
          <kesma-icon [size]="24" name="vg-chevron-left"></kesma-icon>
        </button>
        <vg-exchange-item
          *ngIf="dataType === ExchangeBoxDataType.STOCK && (stockData$ | async) as stockData"
          [data]="stockData"
          [styleID]="'large'"
        ></vg-exchange-item>
        <vg-exchange-item
          *ngIf="dataType === ExchangeBoxDataType.CURRENCY && (currencyData$ | async) as currencyData"
          [data]="currencyData"
          [styleID]="'large'"
        ></vg-exchange-item>
        <button (click)="changeItems('next')" class="exchange-box-button right" aria-label="Következő">
          <kesma-icon [size]="24" name="vg-chevron-right"></kesma-icon>
        </button>
      </div>
      <div class="exchange-box-chart-wrapper">
        <div *ngIf="isChartLoaded && isChartDataLoading" class="exchange-box-data-loader">
          <vg-spinner [height]="15" [width]="15"></vg-spinner>
        </div>
        <div *ngIf="!isChartLoaded" class="exchange-box-loader">
          <vg-spinner [height]="50" [width]="50"></vg-spinner>
        </div>
        <div *ngIf="isChartEmpty" class="exchange-box-no-data">Nincs adat</div>
        <div #chartContainer class="exchange-box-chart"></div>
        <ng-container *ngIf="dataType === ExchangeBoxDataType.STOCK; else currencyLink">
          <ng-container *ngIf="stockData$ | async as stockData">
            <a
              [routerLink]="getStockExchangeLink(stockData.type, stockData.id)"
              [title]="stockData.name + ' árfolyam'"
              class="exchange-box-chart-overlay-link"
            ></a>
          </ng-container>
        </ng-container>
        <ng-template #currencyLink>
          <a
            [routerLink]="['/', 'deviza-adatok', selectedCurrency$.value.toLowerCase()]"
            [title]="selectedCurrency$.value + '/HUF deviza árfolyam'"
            class="exchange-box-chart-overlay-link"
          ></a>
        </ng-template>
      </div>
    </div>
  </div>
</div>
