@use 'shared' as *;

.exchange-box {
  background-color: var(--kui-gray-950);
  color: var(--kui-white);

  &-header {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    padding: 8px 15px;

    @include media-breakpoint-between(lg, lg) {
      padding: 8px;
    }

    .bet-text {
      font-size: 18px;
      font-weight: 700;
      line-height: normal;
      letter-spacing: 0.36px;

      @include media-breakpoint-between(lg, lg) {
        font-size: 16px;
      }
    }

    img {
      width: 43.67px;
      height: 35px;
    }
  }

  &-tabs {
    display: flex;
  }

  &-tab {
    font-size: 14px;
    line-height: 18px;
    font-weight: 700;
    background-color: var(--kui-gray-800);
    color: var(--kui-deep-teal-400);
    border: 1px solid var(--kui-deep-teal-400);
    padding: 8px;
    width: 100%;
    text-align: center;
    cursor: pointer;
    transition: 0.3s;

    &.active {
      background-color: var(--kui-deep-teal-400);
      color: var(--kui-gray-900);
    }

    @media (hover: hover) {
      &:hover {
        background-color: var(--kui-deep-teal-400);
        color: var(--kui-white);
      }
    }
  }

  &-items {
    display: flex;
    justify-content: space-between;
    align-items: center;

    vg-exchange-item {
      width: 100%;
      height: 40px;
      flex: 1;

      ::ng-deep {
        .exchange-item {
          border-radius: initial;
        }
      }
    }
  }

  &-button {
    width: 40px;
    height: 40px;
    background-color: var(--kui-gray-800);
    border: 1px solid var(--kui-gray-800);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: 0.3s;

    kesma-icon {
      transition: 0.3s;
      color: var(--kui-white);
    }

    @media (hover: hover) {
      &:hover {
        border-color: var(--kui-deep-teal-400);

        kesma-icon {
          color: var(--kui-deep-teal-400);
        }
      }
    }
  }

  &-chart-wrapper {
    position: relative;
    padding-bottom: 11px;
  }

  &-chart {
    width: 100%;
    display: block;
    z-index: 1;
  }

  &-no-data {
    position: absolute;
    width: 100%;
    height: 80%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2;
    color: var(--kui-deep-teal-400);
    font-size: 20px;
    line-height: 26px;
    font-weight: 700;
    letter-spacing: 0.4px;
  }

  &-chart-overlay-link {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: 2;
  }

  &-data-loader {
    position: absolute;
    top: 5px;
    right: 5px;
    z-index: 1001;
    border: 1px solid var(--kui-gray-700);
    background-color: var(--kui-gray-950);
    border-radius: 50%;
    padding: 5px;
  }

  &-loader {
    margin: 20px 20px 9px;
    text-align: center;

    vg-spinner {
      display: inline-block;
    }
  }
}
