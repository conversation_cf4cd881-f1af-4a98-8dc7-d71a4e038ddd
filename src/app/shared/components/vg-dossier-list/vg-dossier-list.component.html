<a class="dossier-element first" [routerLink]="['/dosszie']">
  <span>A<PERSON><PERSON><PERSON><PERSON></span>
  <div class="divider"></div>
</a>

<div
  kesma-swipe
  class="main"
  [data]="dossierData()"
  [itemTemplate]="itemTemplate"
  [previousNavigationTemplate]="previousNavigation"
  [nextNavigationTemplate]="nextNavigation"
  [useNavigation]="true"
  [breakpoints]="{
    default: {
      itemCount: 'auto',
      gap: '16px',
    },
    '768': {
      itemCount: 'auto',
      gap: '24px',
    },
  }"
></div>

<ng-template #itemTemplate let-data="data">
  <a [routerLink]="['/dosszie', data?.slug]" class="dossier-element">{{ data?.title }}</a>
</ng-template>

<ng-template #previousNavigation>
  <kesma-icon name="vg-chevron-left"></kesma-icon>
</ng-template>

<ng-template #nextNavigation>
  <kesma-icon name="vg-chevron-right"></kesma-icon>
</ng-template>

<div class="navigation"></div>
