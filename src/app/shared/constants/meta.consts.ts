export const defaultMetaInfo = {
  title: 'Világgazdaság',
  robots: 'index, follow, max-image-preview:large',
  //eslint-disable-next-line max-len
  description: 'A Világgazdaság megbízható, naprak<PERSON>z, objektív információforrás, az üzleti élet befolyásos, mértékadó lapja.',
  ogTitle: 'Világgazdaság',
  ogImageWidth: '1200',
  ogImageHeight: '600',
  ogLocale: 'hu_HU',
  //eslint-disable-next-line max-len
  ogDescription: 'A Világgazdaság megbízható, naprakész, objektív információforrás, az üzleti élet befolyásos, mértékadó lapja.',
  ogSiteName: 'Világgazdaság',
  ogType: 'website',
};

export const galleryMetaInfo = {
  title: 'Galéria - Világgazdaság',
  robots: 'index, follow, max-image-preview:large',
  ogType: 'gallery',
};
export const weatherMetaInfo = {
  title: 'Napi időjá<PERSON>, előrejelzés',
  //eslint-disable-next-line max-len
  description:
    'Budapest és Pest megyei települések részletes időjárási adatai, hőmérséklet, csapadék, légnyomás értékek. ' +
    'Mindennap frissülő országos időjárás előrejelzés.',
  keywords: 'időjárás, napi előrejelzés, csapadék, hőmérséklet',
};
