import { HttpEvent, HttpHandlerFn, HttpRequest } from '@angular/common/http';
import { Observable } from 'rxjs';

export function portalHeaderHttpInterceptor(req: HttpRequest<unknown>, next: HttpHandlerFn): Observable<HttpEvent<any>> {
  const defaultLanguage = 'hu';
  const defaultHeaders = {
    portal: 'vilaggazdasag',
  };
  // Clone the request to add the new headers
  const clonedRequest = req.clone({
    setHeaders: {
      'Accept-Language': defaultLanguage,
      ...defaultHeaders,
    },
  });

  // Pass the modified request to the next handler in the chain
  return next(clonedRequest);
}
