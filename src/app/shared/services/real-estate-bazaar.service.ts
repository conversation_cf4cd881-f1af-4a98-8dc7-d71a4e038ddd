import { Injectable } from '@angular/core';
import { tap } from 'rxjs/operators';
import { Observable, of } from 'rxjs';
import { CleanHttpService } from './clean-http.service';
import { environment } from '../../../environments/environment';
import { mapRealEstateApiDataToRealEstateData, RealEstateBazaarApiData, RealEstateBazaarBackendResponse, RealEstateBazaarData } from '@trendency/kesma-ui';
import { EnvironmentApiUrl, UtilService } from '@trendency/kesma-core';

@Injectable({
  providedIn: 'root',
})
export class RealEstateBazaarService {
  realEstateData: RealEstateBazaarData[] = [];

  private realEstateDataLoading = false;

  constructor(
    private readonly httpService: CleanHttpService,
    private readonly utilsService: UtilService
  ) {}

  get ingatlanbazarApiUrl(): string {
    if (typeof environment.ingatlanbazarApiUrl === 'string') {
      return environment.ingatlanbazarApiUrl as string;
    }

    const { clientApiUrl, serverApiUrl } = environment.ingatlanbazarApiUrl as EnvironmentApiUrl;
    return this.utilsService.isBrowser() ? clientApiUrl : serverApiUrl;
  }

  handleRealEstateInitEvent(): Observable<RealEstateBazaarBackendResponse | null> {
    if (!this.realEstateDataLoading && this.realEstateData.length < 1) {
      return this.getRealEstateData();
    }

    return of(null);
  }

  private getRealEstateData(): Observable<RealEstateBazaarBackendResponse> {
    this.realEstateDataLoading = true;
    const realEstates: RealEstateBazaarData[] = [];
    return this.httpService.get(`${this.ingatlanbazarApiUrl}/property-search?property_location=1&property_newbuildonly=on&property__2=3_2`).pipe(
      tap((data: RealEstateBazaarBackendResponse) => {
        data?.hits?.forEach((realEstate: RealEstateBazaarApiData) => {
          realEstates.push(mapRealEstateApiDataToRealEstateData(realEstate));
        });

        this.realEstateData = realEstates;
        this.realEstateDataLoading = false;
      })
    );
  }
}
