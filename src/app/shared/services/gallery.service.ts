import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { map } from 'rxjs/operators';
import { ReqService } from '@trendency/kesma-core';
import { ApiResponseMetaList, ApiResult, BackendGalleryDetails, GalleryDetails, mapBackendGalleryDetailsResultToGalleryDetails } from '@trendency/kesma-ui';

@Injectable({
  providedIn: 'root',
})
export class GalleryService {
  constructor(private readonly reqService: ReqService) {}

  getGalleryDetails(slug: string): Observable<GalleryDetails> {
    if (!slug) {
      return throwError(() => 'Undefined gallery slug');
    }

    return this.reqService
      .get<ApiResult<BackendGalleryDetails[], ApiResponseMetaList>>(`media/gallery/${slug}`)
      .pipe(map(mapBackendGalleryDetailsResultToGalleryDetails));
  }
}
