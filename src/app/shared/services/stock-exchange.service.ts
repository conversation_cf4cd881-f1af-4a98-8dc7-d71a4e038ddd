import { Injectable } from '@angular/core';
import { BehaviorSubject, interval, Observable, of } from 'rxjs';
import {
  FinancialAPIStockDetailResult,
  FinancialAPIStockGraphResult,
  FinancialAPIStockIndexGraphResult,
  FinancialAPIStockIndexListResult,
  FinancialAPIStockIndexTopResult,
  FinancialAPIStockInfoResult,
  FinancialAPIStockLatestResult,
  FinancialAPIStockListResult,
  FinancialAPIStockStatisticsResult,
  StockChartType,
  StockIndexChartType,
} from '../definitions';
import { startWith } from 'rxjs/operators';
import { UtilService } from '@trendency/kesma-core';
import { FinancialService } from './financial.service';
import { CleanHttpService } from './clean-http.service';

@Injectable({
  providedIn: 'root',
})
export class StockExchangeService extends FinancialService {
  override readonly autoRefreshTime: number = 30; // seconds
  override readonly cacheMaxAge: number = 25; // seconds

  readonly API_PREFIX: string = 'stock/bet';

  selectedStock$: BehaviorSubject<string | null> = new BehaviorSubject<string | null>(null);
  selectedStockIndex$: BehaviorSubject<string | null> = new BehaviorSubject<string | null>(null);

  autoRefresh$: Observable<number> = this.utilsService.isBrowser() ? interval(this.cacheMaxAge * 1000).pipe(startWith(0)) : of(0);

  constructor(
    protected override readonly httpService: CleanHttpService,
    protected override readonly utilsService: UtilService
  ) {
    super(httpService, utilsService);
  }

  get selectedStock(): string | null {
    return this.selectedStock$.value;
  }

  get selectedStockIndex(): string | null {
    return this.selectedStockIndex$.value;
  }

  getLatestRates(): Observable<FinancialAPIStockLatestResult> {
    return this.getCachedFinancialData<FinancialAPIStockLatestResult>(`${this.API_PREFIX}/latestimportant`);
  }

  getStockListData(): Observable<FinancialAPIStockListResult> {
    return this.getCachedFinancialData<FinancialAPIStockListResult>(`${this.API_PREFIX}/productstradedetails`);
  }

  getXtendStockListData(): Observable<FinancialAPIStockListResult> {
    return this.getCachedFinancialData<FinancialAPIStockListResult>(`${this.API_PREFIX}/productstradedetailsxtend`);
  }

  getStockInfo(stock: string): Observable<FinancialAPIStockInfoResult> {
    return this.getCachedFinancialData<FinancialAPIStockInfoResult>(`${this.API_PREFIX}/products/${encodeURIComponent(stock)}`);
  }

  getStockDetailData(stock: string): Observable<FinancialAPIStockDetailResult> {
    return this.getCachedFinancialData<FinancialAPIStockDetailResult>(`${this.API_PREFIX}/tradestatistics/latest/${encodeURIComponent(stock)}`);
  }

  getStockGraphData(stock: string, type: StockChartType): Observable<FinancialAPIStockGraphResult> {
    return this.getCachedFinancialData<FinancialAPIStockGraphResult>(`${this.API_PREFIX}/tradestatistics/${encodeURIComponent(stock)}/${type}`);
  }

  getStockStatistics(stock: string): Observable<FinancialAPIStockStatisticsResult> {
    return this.getCachedFinancialData<FinancialAPIStockStatisticsResult>(`${this.API_PREFIX}/productstatistics/${encodeURIComponent(stock)}`);
  }

  getStockIndexListData(): Observable<FinancialAPIStockIndexListResult> {
    return this.getCachedFinancialData<FinancialAPIStockIndexListResult>(`${this.API_PREFIX}/indexes`);
  }

  getStockIndexGraphData(stockIndex: string, type: StockIndexChartType): Observable<FinancialAPIStockIndexGraphResult> {
    return this.getCachedFinancialData<FinancialAPIStockIndexGraphResult>(`${this.API_PREFIX}/index/${encodeURIComponent(stockIndex)}/${type}`);
  }

  getStockIndexTop5WinnerData(): Observable<FinancialAPIStockIndexTopResult> {
    return this.getCachedFinancialData<FinancialAPIStockIndexTopResult>(`${this.API_PREFIX}/bux/top5winners`);
  }

  getStockIndexTop5LoserData(): Observable<FinancialAPIStockIndexTopResult> {
    return this.getCachedFinancialData<FinancialAPIStockIndexTopResult>(`${this.API_PREFIX}/bux/top5losers`);
  }

  getStockIndexTop5TurnoverData(): Observable<FinancialAPIStockIndexTopResult> {
    return this.getCachedFinancialData<FinancialAPIStockIndexTopResult>(`${this.API_PREFIX}/bux/top5turnovers`);
  }
}
