import { Injectable } from '@angular/core';
import { ApiResponseMetaList, ApiResult, ArticleSearchResult, BackendArticleSearchResult } from '@trendency/kesma-ui';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ApiService } from './api.service';
import { backendArticlesSearchResultsToArticleSearchResArticles } from '../utils';

@Injectable({
  providedIn: 'root',
})
export class ListPageService {
  /**
   * The ID of the current author selected in the search.
   * Used here: {@link AuthorPageResolver}
   */
  filteredPublicAuthorId?: string;

  constructor(private readonly apiService: ApiService) {}

  searchArticle(
    page = 0,
    itemsPerPage = 10,
    extraParams?: Record<string, string | string[]>
  ): Observable<ApiResult<ArticleSearchResult[], ApiResponseMetaList>> {
    return this.apiService.searchByKeyword(page, itemsPerPage, this.setPublicAuthorParam(extraParams!)).pipe(
      map(({ data, meta }: ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>) => ({
        data: data.map(backendArticlesSearchResultsToArticleSearchResArticles),
        meta,
      }))
    );
  }

  private setPublicAuthorParam(params: Record<string, string | string[]>): Record<string, string | string[]> {
    if (params && 'author' in params) {
      const { author: authorSlug, ...filteredParams } = params;
      return {
        ...filteredParams,
        authorSlug,
      };
    }
    return params;
  }
}
