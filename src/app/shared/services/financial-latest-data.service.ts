import { Injectable } from '@angular/core';
import { Observable, shareReplay } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';
import {
  FinancialAPICurrencyCurrentDataWithCurrency,
  FinancialAPICurrencyLatestData,
  FinancialAPIStockLatestData,
  FinancialAPIStockLatestResult,
  VgExchangeItemData,
} from '../definitions';
import { CurrencyExchangeService } from './currency-exchange.service';
import { getStockExchangeLink, isStockDailyDataAvailable } from '../utils';
import { StockExchangeService } from './stock-exchange.service';

@Injectable({
  providedIn: 'root',
})
export class FinancialLatestDataService {
  availableCurrencies: string[] = ['EUR', 'USD', 'GBP', 'CHF', 'PLN', 'RON', 'CZK'];

  latestCurrencyData$: Observable<VgExchangeItemData[]> = this.currencyExchangeService.autoRefresh$.pipe(
    switchMap(() =>
      this.currencyExchangeService.getLatestRates().pipe(
        map((result: FinancialAPICurrencyLatestData) =>
          Object.entries(result?.data?.rates ?? [])
            .map(([key, value]) => ({
              ...value,
              currency: key,
            }))
            .sort(
              (a: FinancialAPICurrencyCurrentDataWithCurrency, b: FinancialAPICurrencyCurrentDataWithCurrency) =>
                this.availableCurrencies.indexOf(a.currency) - this.availableCurrencies.indexOf(b.currency)
            )
            .map((data: FinancialAPICurrencyCurrentDataWithCurrency) => this.mapCurrencyDataToExchangeItem(data))
        )
      )
    ),
    shareReplay({
      bufferSize: 1,
      refCount: true,
    })
  );

  latestStockData$: Observable<VgExchangeItemData[]> = this.stockExchangeService.autoRefresh$.pipe(
    switchMap(() =>
      this.stockExchangeService.getLatestRates().pipe(
        map((result: FinancialAPIStockLatestResult) =>
          result.data
            .map((stock: FinancialAPIStockLatestData) => ({
              ...stock,
              currentValue: isStockDailyDataAvailable() ? stock.currentValue : null,
            }))
            .map((data: FinancialAPIStockLatestData) => this.mapStockDataToExchangeItem(data))
        )
      )
    ),
    shareReplay({
      bufferSize: 1,
      refCount: true,
    })
  );

  constructor(
    private readonly currencyExchangeService: CurrencyExchangeService,
    private readonly stockExchangeService: StockExchangeService
  ) {}

  private mapCurrencyDataToExchangeItem(data: FinancialAPICurrencyCurrentDataWithCurrency): VgExchangeItemData {
    return {
      id: data.currency,
      name: data.currency + '/HUF',
      value: data.rate,
      percentage: data.percentage,
      direction: this.mapDirection(data.rate, data.direction),
      link: ['/', 'deviza-adatok', data.currency.toLowerCase()],
      type: 'Currency',
    };
  }

  private mapStockDataToExchangeItem(data: FinancialAPIStockLatestData): VgExchangeItemData {
    return {
      id: data.name,
      name: data.displayName,
      value: data.currentValue,
      percentage: data.changePercentage,
      direction: this.mapDirection(data.currentValue, data.changeDirection),
      link: getStockExchangeLink(data.type, data.name),
      type: data.type,
    };
  }

  private mapDirection(value: number | null, backendDirection: string): 'up' | 'down' | 'none' | 'no-data' {
    if (!value || !backendDirection) {
      return 'no-data';
    }

    switch (backendDirection.toLowerCase()) {
      case 'up':
        return 'up';
      case 'down':
        return 'down';
      case 'same':
      case 'none':
        return 'none';
      default:
        return 'no-data';
    }
  }
}
