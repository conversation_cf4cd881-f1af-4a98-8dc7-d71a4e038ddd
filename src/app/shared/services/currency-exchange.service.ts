import { Injectable } from '@angular/core';
import {
  CurrencyChartType,
  FinancialAPICurrencyCrossRatesData,
  FinancialAPICurrencyCrossRatesFriendlyResult,
  FinancialAPICurrencyDateAggregatedData,
  FinancialAPICurrencyDateData,
  FinancialAPICurrencyDayAndWeekData,
  FinancialAPICurrencyLatestData,
  FinancialAPICurrencyMonthAndYearData,
  FinancialAPICurrencyMonthAndYearResult,
  FinancialAPICurrencyStatisticsData,
  FinancialAPICurrencyThreeYearsData,
} from '../definitions';
import { BehaviorSubject, interval, Observable, of } from 'rxjs';
import { financialAPICurrencyCrossRatesMapper } from '../utils';
import { map, startWith } from 'rxjs/operators';
import { UtilService } from '@trendency/kesma-core';
import { FinancialService } from './financial.service';
import { CleanHttpService } from './clean-http.service';

@Injectable({
  providedIn: 'root',
})
export class CurrencyExchangeService extends FinancialService {
  readonly API_PREFIX: string = 'exchangerate';

  selectedCurrency$: BehaviorSubject<string> = new BehaviorSubject<string>('EUR');

  autoRefresh$: Observable<number> = this.utilsService.isBrowser() ? interval(this.cacheMaxAge * 1000).pipe(startWith(0)) : of(0);

  constructor(
    protected override readonly httpService: CleanHttpService,
    protected override readonly utilsService: UtilService
  ) {
    super(httpService, utilsService);
  }

  get selectedCurrency(): string {
    return this.selectedCurrency$.value;
  }

  getLatestRates(): Observable<FinancialAPICurrencyLatestData> {
    return this.getCachedFinancialData<FinancialAPICurrencyLatestData>(`${this.API_PREFIX}/latest`);
  }

  getDayData(): Observable<FinancialAPICurrencyDayAndWeekData> {
    return this.getCachedFinancialData<FinancialAPICurrencyDayAndWeekData>(`${this.API_PREFIX}/minigraphdata`);
  }

  getWeekData(): Observable<FinancialAPICurrencyDayAndWeekData> {
    return this.getCachedFinancialData<FinancialAPICurrencyDayAndWeekData>(`${this.API_PREFIX}/week`);
  }

  getMonthData(): Observable<FinancialAPICurrencyMonthAndYearData> {
    return this.getCachedFinancialData<FinancialAPICurrencyMonthAndYearData>(`${this.API_PREFIX}/month`);
  }

  getYearData(): Observable<FinancialAPICurrencyMonthAndYearData> {
    return this.getCachedFinancialData<FinancialAPICurrencyMonthAndYearData>(`${this.API_PREFIX}/year`);
  }

  getThreeYearsData(): Observable<FinancialAPICurrencyThreeYearsData> {
    return this.getCachedFinancialData<FinancialAPICurrencyThreeYearsData>(`${this.API_PREFIX}/threeyears`);
  }

  getCrossRates(): Observable<FinancialAPICurrencyCrossRatesFriendlyResult> {
    return this.getCachedFinancialData<FinancialAPICurrencyCrossRatesData>(`${this.API_PREFIX}/crossrates`).pipe(map(financialAPICurrencyCrossRatesMapper));
  }

  getStatistics(): Observable<FinancialAPICurrencyStatisticsData> {
    return this.getCachedFinancialData<FinancialAPICurrencyStatisticsData>(`${this.API_PREFIX}/statistics`);
  }

  getGraphData(chartType: CurrencyChartType, currency: string): Observable<FinancialAPICurrencyDateData[] | FinancialAPICurrencyDateAggregatedData[] | null> {
    switch (chartType) {
      case CurrencyChartType.DAY:
        return this.getDayData().pipe(map((result: FinancialAPICurrencyDayAndWeekData) => result.data.data[currency]));
      case CurrencyChartType.WEEK:
        return this.getWeekData().pipe(map((result: FinancialAPICurrencyDayAndWeekData) => result.data.data[currency]));
      case CurrencyChartType.MONTH:
        return this.getMonthData().pipe(
          map((result: FinancialAPICurrencyMonthAndYearData) =>
            result.data.map((dataRow: FinancialAPICurrencyMonthAndYearResult) => ({ date: dataRow.date, ...dataRow.rates[currency] }))
          )
        );
      case CurrencyChartType.YEAR:
        return this.getYearData().pipe(
          map((result: FinancialAPICurrencyMonthAndYearData) =>
            result.data.map((dataRow: FinancialAPICurrencyMonthAndYearResult) => ({ date: dataRow.date, ...dataRow.rates[currency] }))
          )
        );
      case CurrencyChartType.THREE_YEARS:
        return this.getThreeYearsData().pipe(map((result: FinancialAPICurrencyThreeYearsData) => result.data.data[currency]));
      default:
        return of(null);
    }
  }
}
