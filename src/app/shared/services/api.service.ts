import { Injectable } from '@angular/core';
import { backendDateToDate, IHttpOptions, ReqService } from '@trendency/kesma-core';
import {
  Advertisement,
  ApiListResult,
  ApiResponseMetaList,
  ApiResult,
  ArticleCard,
  ArticleCategoryParams,
  ArticleSearchResult,
  BackendArticle,
  BackendArticleSearchResult,
  BackendPodcast,
  BasicDossier,
  buildMenuItem,
  buildPhpArrayParam,
  DossierArticle,
  GalleryData,
  InitResponse,
  Layout,
  mapBackendArticleDataToArticleCard,
  MenuTreeResponse,
  PortalBasedMenuLinkOverride,
  PortfolioResponse,
  PrimaryColumn,
  Region,
  SearchQuery,
  SimplifiedMenuTree,
  TrendingTag,
  VariableDidYouKnowBox,
  Video,
  VideoCard,
} from '@trendency/kesma-ui';
import { Observable, Subject } from 'rxjs';
import { map } from 'rxjs/operators';
import { AuthorData, BackendAuthorData } from '../../feature/author/author.definitions';
import { backendArticlesSearchResultsToArticleSearchResArticles } from '../utils';
import { HttpParams } from '@angular/common/http';
import { AgroResponse } from '../definitions';

@Injectable({
  providedIn: 'root',
})
export class ApiService {
  readonly isHiddenSubject$: Subject<boolean> = new Subject<boolean>();

  constructor(private readonly reqService: ReqService) {}

  /**
   * Creates string form a given query params object. This is used to create a query string that already contains
   * the params in the url. This is need for the requestService, as it makes a key only from the provided url, ignoring
   * the other params.
   * @param params
   * @private
   */
  private paramsToString(params: Record<string, string | string[] | undefined | null>): string {
    const paramsPrepared: Record<string, string>[] = [];
    if (params) {
      Object.keys(params).map((key) => {
        const value = params[key];
        if (value === undefined || value === null) {
          return;
        }
        if (Array.isArray(value)) {
          value.map((item) => {
            // Add [] to end of array params, but only if it is not already there.
            const itemKey = key.endsWith(']') ? key : `${key}[]`;
            paramsPrepared.push({ [itemKey]: encodeURIComponent(item) });
          });
          return;
        }
        paramsPrepared.push({ [key]: encodeURIComponent(value) });
      });
    }
    const paramsString = paramsPrepared
      .map((item) =>
        Object.keys(item)
          .map((key) => `${key}=${item[key]}`)
          .join('&')
      )
      .join('&');
    return paramsString;
  }

  /**
   * Creates a query string from the given query params and extra params. This is used to create a query string that
   * already contains the params in the url. This is need for the requestService, as it makes a key only from the provided url, ignoring
   * the other params.
   * @param queryParams
   * @param extraParams
   * @private
   */
  private searchKeywordParamsToString(
    queryParams: Record<string, string | string[] | undefined | null>,
    extraParams?: Record<string, string | string[]>
  ): string {
    const queryParamsString = queryParams ? this.paramsToString(queryParams) : '';
    const extraParamsString = extraParams ? this.paramsToString(extraParams) : '';

    return queryParamsString?.length && extraParamsString?.length ? `${queryParamsString}&${extraParamsString}` : queryParamsString || extraParamsString;
  }

  get isHidden$(): Observable<boolean> {
    return this.isHiddenSubject$.asObservable();
  }

  public init(): Observable<ApiResult<InitResponse>> {
    return this.reqService.get('/init');
  }

  public getMenu(overrides?: PortalBasedMenuLinkOverride): Observable<SimplifiedMenuTree> {
    return this.reqService.get<ApiResult<MenuTreeResponse>>('menu/tree').pipe(
      map(
        ({ data }) =>
          ({
            header: (data?.header ?? []).map((item) => buildMenuItem(item, '', overrides)),
            header_0: (data?.header_0 ?? []).map((item) => buildMenuItem(item, '', overrides)),
            header_1: (data?.header_1 ?? []).map((item) => buildMenuItem(item, '', overrides)),
            footer: (data?.footer ?? []).map((item) => buildMenuItem(item, '', overrides)),
            footer_0: (data?.footer_0 ?? []).map((item) => buildMenuItem(item, '', overrides)),
            footer_1: (data?.footer_1 ?? []).map((item) => buildMenuItem(item, '', overrides)),
            footer_2: (data?.footer_2 ?? []).map((item) => buildMenuItem(item, '', overrides)),
          }) as SimplifiedMenuTree
      )
    );
  }

  public getAllCommercials(): Observable<ApiResult<Advertisement[], ApiResponseMetaList>> {
    return this.reqService.get('portal/commercials');
  }

  public getLayoutPreview(hash: string): Observable<ApiResult<Layout>> {
    return this.reqService.get(`layout/preview/view?previewHash=${hash}`);
  }

  public getCategoryLayout(categorySlug: string): Observable<ApiResult<Layout>> {
    return this.reqService.get<ApiResult<Layout>>(`column-layout/${categorySlug}`);
  }

  public getCategoryArticles(
    categorySlug: string,
    page = 0,
    itemsPerPage = 10,
    year?: string,
    month?: string,
    excludedIds: string[] = []
  ): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>> {
    let params: ArticleCategoryParams = {
      columnSlug: categorySlug,
      rowCount_limit: itemsPerPage.toString(),
      page_limit: page.toString(),
      'excludedArticleIds[]': excludedIds ? excludedIds : [],
    };
    params = year ? { ...params, year } : params;
    params = month ? { ...params, month } : params;
    return this.reqService.get<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>>(`content-page/articles-by-column`, { params: params }).pipe(
      map(({ data, meta }) => {
        return {
          meta,
          data: data.map((article) => {
            const [publishYear, publishMonth] = (article.publishDate as string).split('-');
            return {
              ...article,
              publishYear,
              publishMonth,
            };
          }),
        };
      })
    );
  }

  public getSidebarArticleRecommendations(
    count: number,
    columnSlug?: string,
    excludedIds: string[] = []
  ): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    let { params }: IHttpOptions = {
      params: {
        rowCount_limit: count.toString(),
        'excludedArticleIds[]': excludedIds,
      },
    };
    params = columnSlug ? { ...params, columnSlug } : params;

    return this.reqService.get<ApiResult<ArticleCard[], ApiResponseMetaList>>(
      columnSlug ? '/content-page/articles-by-column?dev' : '/content-page/articles-by-last-day',
      { params }
    );
  }

  public getOpinionAuthor(authorSlug: string, page = 0, itemsPerPage = 12): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>> {
    return this.reqService.get(`content-page/articles-by-opinion-type`, {
      params: {
        author: authorSlug,
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  public getArticles(itemsPerPage = 3, isOrderedByPageViews = false): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>> {
    let params: ArticleCategoryParams = {
      rowCount_limit: itemsPerPage.toString(),
    };
    params = isOrderedByPageViews ? { ...params, ...{ mode: 'top_viewed' } } : params;

    return this.reqService.get<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>>(`content-page/articles-by-any`, { params });
  }

  public getArticlesByTag(slug: string): Observable<ApiResult<ArticleCard[], ApiResponseMetaList>> {
    return this.reqService.get(`content-page/articles-by-tag?tagSlug=${slug}`);
  }

  public getGalleries(page = 0, itemsPerPage = 15): Observable<ApiResult<GalleryData[], ApiResponseMetaList>> {
    return this.reqService.get(`/media/galleries`, {
      params: {
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  public getVideos(page = 0, itemsPerPage = 15): Observable<ApiResult<{ videos: VideoCard[] }, ApiResponseMetaList>> {
    return this.reqService.get(`/media/videos`, {
      params: {
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  public getVideo(slug: string): Observable<ApiResult<Video[]>> {
    return this.reqService.get<ApiResult<Video[]>>(`/media/videos/${slug}`);
  }

  public getPodcasts(
    page = 0,
    itemsPerPage = 15
  ): Observable<
    ApiResult<
      {
        title: string;
        url: string;
        publishDate?: string;
        lead?: string;
        length?: string;
        tag?: string;
        slug?: string;
        thumbnail?: string;
        columnTitle?: string;
        videaUrl?: string;
        thumbnailFocusedImages?: object;
      }[],
      ApiResponseMetaList
    >
  > {
    return this.reqService.get(`/content-group/podcasts`, {
      params: {
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  public getDossier(dossierSlug: string, page = 0, itemsPerPage = 15): Observable<ApiResult<DossierArticle[], ApiResponseMetaList & Partial<BasicDossier>>> {
    return this.reqService.get(`/content-group/dossiers/${dossierSlug}`, {
      params: { rowCount_limit: itemsPerPage?.toString(), page_limit: page?.toString() },
    });
  }

  public getRegionPage(regionSlug: string, page = 0, itemsPerPage = 21): Observable<ApiResult<BackendArticle[], ApiResponseMetaList & { regionName: string }>> {
    return this.reqService.get(`/content-page/articles-by-region`, {
      params: { regionSlug: regionSlug, rowCount_limit: itemsPerPage?.toString(), page_limit: page?.toString() },
    });
  }

  public getRegions(page = 0, itemsPerPage = 50): Observable<ApiResult<Region[], ApiResponseMetaList & { regionName: string }>> {
    return this.reqService.get(`/source/content-group/regions`, {
      params: { rowCount_limit: itemsPerPage?.toString(), page_limit: page?.toString() },
    });
  }

  public searchByKeyword(
    page?: number,
    rowCount_limit?: number,
    extraParams?: Record<string, string | string[]>
  ): Observable<ApiResult<BackendArticleSearchResult[], ApiResponseMetaList>> {
    const queryParams = { rowCount_limit: rowCount_limit?.toString(), page_limit: page?.toString() };
    const paramsAsString = this.searchKeywordParamsToString(queryParams, extraParams);
    return this.reqService.get(`/content-page/search${paramsAsString ? `?${paramsAsString}` : ''}`);
  }

  public getPortfolioFooter(): Observable<PortfolioResponse> {
    return this.reqService.get('portal/portfolio-footer');
  }

  getTagsOnHeaderBar(): Observable<ApiResult<TrendingTag[], ApiResponseMetaList>> {
    return this.reqService.get('/content-group/tags-on-header-bar');
  }

  getColumn(slug: string): Observable<ApiResult<PrimaryColumn>> {
    return this.reqService.get(`/content-group/columns/${slug}`);
  }

  public getPublicAuthors(authorId?: string, options?: IHttpOptions): Observable<ApiResult<BackendAuthorData[], ApiResponseMetaList>> {
    let params = { ...options?.params };
    params = authorId ? { ...params, 'public_author_id[]': authorId } : params;
    return this.reqService.get(`/user/authors`, { params });
  }

  public getPublicAuthorSocial(global_filter?: string, options?: IHttpOptions): Observable<ApiResult<AuthorData, ApiResponseMetaList>> {
    let params = { ...options?.params };
    params = global_filter ? { ...params, global_filter: global_filter } : params;
    return this.reqService.get(`/user/author_social`, { params });
  }

  getParentColumns(options?: IHttpOptions): Observable<ApiResult<PrimaryColumn[], ApiResponseMetaList>> {
    return this.reqService.get('/source/content-group/columns?parents_only=1', options);
  }

  public getTagsList(tag: string, page = 0, itemsPerPage = 10): Observable<ApiListResult<ArticleCard>> {
    return this.reqService
      .get<ApiListResult<BackendArticleSearchResult>>('content-page/search', {
        params: {
          'tags[]': tag,
          rowCount_limit: itemsPerPage.toString(),
          page_limit: page.toString(),
        },
      })
      .pipe(
        map(({ data, meta }) => ({
          data: data
            .map(mapBackendArticleDataToArticleCard)
            .map((article) => ({ ...article, publishDate: backendDateToDate(article.publishDate as string) }) as ArticleCard),
          meta,
        }))
      );
  }

  getSearch(searchQuery: SearchQuery, page = 0, itemsPerPage = 20): Observable<ApiListResult<ArticleSearchResult>> {
    return this.reqService.get(`content-page/search?`, {
      params: {
        ...searchQuery,
        rowCount_limit: itemsPerPage.toString(),
        page_limit: page.toString(),
      },
    });
  }

  getArticlesByFoundationTag(foundationTag: string): Observable<ApiResult<ArticleSearchResult[], ApiResponseMetaList>> {
    return this.reqService
      .get(`content-page/search`, {
        params: {
          foundationTagSelect: foundationTag,
          rowCount_limit: '1',
          page_limit: '0',
          isFoundationContent: '1',
        },
      })
      .pipe(
        map(({ data, meta }: ApiResult<BackendArticleSearchResult[], ApiResponseMetaList> | any) => ({
          data: data.map(backendArticlesSearchResultsToArticleSearchResArticles),
          meta,
        }))
      );
  }

  searchArticleByTags(tags: string[], contentTypes?: string[], page = 0, itemsPerPage = 10): Observable<ApiResult<ArticleSearchResult[], ApiResponseMetaList>> {
    return this.reqService
      .get(`content-page/search`, {
        params: {
          ...buildPhpArrayParam(tags, 'tags'),
          rowCount_limit: itemsPerPage.toString(),
          page_limit: page.toString(),
          'content_types[]': contentTypes ? contentTypes : [],
        },
      })
      .pipe(
        map(({ data, meta }: ApiResult<BackendArticleSearchResult[], ApiResponseMetaList> | any) => ({
          data: data.map(backendArticlesSearchResultsToArticleSearchResArticles),
          meta,
        }))
      );
  }

  getAgrokep(): Observable<AgroResponse> {
    const params = new HttpParams().append('cache', new Date().getTime().toString());

    return this.reqService.get(`/mediaworks/agrokep`, {
      params: params,
    });
  }

  getVariableSponsoredDidYouKnowBox(id: string): Observable<VariableDidYouKnowBox> {
    return this.reqService.get(`/content-group/did-you-know/${id}?getRandomData=1`);
  }

  getPodcast(slug: string): Observable<BackendPodcast> {
    return this.reqService.get(`/content-group/podcasts/${slug}`);
  }
}
