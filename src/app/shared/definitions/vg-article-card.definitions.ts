export enum ArticleCardType {
  FeaturedTopImgTagTitle,
  FeaturedBigTopImgTagTitleLead,
  FeaturedRightImgTagTitleLead,
  FeaturedTagTitle,
  FeaturedTopImgTagTitleLead,
  FeaturedRightImgTagTitle,
  FeaturedTitle,
  FeaturedImgAbsoluteTagTitle,
  FeaturedImgAbsoluteTagTitle4Per3,
  FeaturedRightAuthorImgTagTitle,
  FeaturedTopAuthorImgTagTitle,
  FeaturedRightImgDateTagTitle,
  RelatedArticle,
  RelatedArticlePlus,
  ExternalRecommendation,
  MostRead,
  Podcast,
  FreshNews,
  OpinionMain,
  OpinionSecondary,
  PodcastLight,
  FeaturedImgAbsoluteTagTitle1Per1,
  ColumnMain,
  FeaturedImgAbsoluteTagTitle4Per3Separator,
}

export type ArticleCardBadgeType = 'isAdultsOnly' | 'isVideoType' | 'isPodcastType' | 'hasGallery';

export type ArticleCardBadgeList = Partial<Record<ArticleCardBadgeType, boolean>>;
