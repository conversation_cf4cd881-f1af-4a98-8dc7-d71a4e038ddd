import { ArticleCard, FocusPointUrlWithAspectRatio, Sponsorship } from '@trendency/kesma-ui';

export enum VgDossierType {
  Horizontal,
  Vertical,
}

export interface VgDossierData {
  title: string;
  slug?: string;
  lead?: string;
  sponsorship?: Sponsorship;
  headerImage?: string;
  overwriteTitle?: string;
  overwriteMoreButtonLabel?: string;
  coverImageFocusedImages?: FocusPointUrlWithAspectRatio;
}

export interface VgDossierBoxData extends VgDossierData {
  mainArticle: ArticleCard;
  secondaryArticles: ArticleCard[];
}

export interface VgDossierRecommendedData extends VgDossierData {
  articles: ArticleCard[];
}
