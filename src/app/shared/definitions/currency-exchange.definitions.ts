export interface FinancialAPICurrencyCurrentData {
  percentage: number;
  rate: number;
  direction: 'up' | 'down' | 'same';
}

export interface FinancialAPICurrencyCurrentDataWithCurrency extends FinancialAPICurrencyCurrentData {
  currency: string;
}

export interface FinancialAPICurrencyDateData {
  date: string; // ISO-8601
  rate: number;
}

export interface FinancialAPICurrencyAggregatedData {
  open: number;
  close: number;
  min: number;
  max: number;
}

export interface FinancialAPICurrencyDateAggregatedData extends FinancialAPICurrencyAggregatedData {
  date: string; // ISO-8601
}

export interface FinancialAPICurrencyStatisticsCurrencyData {
  average: number;
  close: number;
  max: number;
  min: number;
  onemonthpercentage: number;
  open: number;
  sixmonthpercentage: number;
  yearlymax: number;
  yearlymin: number;
  yearpercentage: number;
}

export interface FinancialAPICurrencyResult<T> {
  data: T;
  error: any;
}

export interface FinancialAPICurrencyLatestResult {
  date: string; // ISO-8601
  rates: {
    [currency: string]: FinancialAPICurrencyCurrentData;
  };
}

export type FinancialAPICurrencyLatestData = FinancialAPICurrencyResult<FinancialAPICurrencyLatestResult>;

export interface FinancialAPICurrencyDayAndWeekResult {
  current: {
    [currency: string]: FinancialAPICurrencyCurrentData;
  };
  data: {
    [currency: string]: FinancialAPICurrencyDateData[];
  };
}

export type FinancialAPICurrencyDayAndWeekData = FinancialAPICurrencyResult<FinancialAPICurrencyDayAndWeekResult>;

export interface FinancialAPICurrencyMonthAndYearResult {
  date: string; // ISO-8601
  rates: {
    [currency: string]: FinancialAPICurrencyAggregatedData;
  };
}

export type FinancialAPICurrencyMonthAndYearData = FinancialAPICurrencyResult<FinancialAPICurrencyMonthAndYearResult[]>;

export interface FinancialAPICurrencyThreeYearsResult {
  data: {
    [currency: string]: FinancialAPICurrencyDateAggregatedData[];
  };
}

export type FinancialAPICurrencyThreeYearsData = FinancialAPICurrencyResult<FinancialAPICurrencyThreeYearsResult>;

export interface FinancialAPICurrencyCrossRatesResult {
  data: (string | null)[][];
}

export type FinancialAPICurrencyCrossRatesData = FinancialAPICurrencyResult<FinancialAPICurrencyCrossRatesResult>;

export interface FinancialAPICurrencyCrossRatesFriendlyResult {
  [currency: string]: {
    [currency: string]: number;
  };
}

export interface FinancialAPICurrencyStatisticsResult {
  data: {
    [currency: string]: FinancialAPICurrencyStatisticsCurrencyData;
  };
}

export type FinancialAPICurrencyStatisticsData = FinancialAPICurrencyResult<FinancialAPICurrencyStatisticsResult>;

export interface CurrencyOption {
  currency: string;
  title: string;
}

export enum CurrencyChartType {
  DAY = 'DAY',
  WEEK = 'WEEK',
  MONTH = 'MONTH',
  YEAR = 'YEAR',
  THREE_YEARS = 'THREE_YEARS',
}
