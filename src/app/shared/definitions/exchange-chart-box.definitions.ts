import { AreaSeriesPartialOptions, ChartOptions, ColorType, CrosshairMode, DeepPartial } from 'lightweight-charts';

export const exchangeChartBoxDefaultConfig: DeepPartial<ChartOptions> = {
  height: 170,
  autoSize: true, // Enable responsive auto chart resize
  layout: {
    textColor: '#fff',
    background: {
      type: ColorType.Solid,
      color: '#262626',
    },
  },
  grid: {
    vertLines: {
      color: '#4f4f4f',
    },
    horzLines: {
      visible: false, // Disable horizontal lines
    },
  },
  // Disable move and zoom
  handleScale: false,
  // Disable move and zoom
  handleScroll: false,
  crosshair: {
    mode: CrosshairMode.Hidden,
  },
  rightPriceScale: {
    visible: false,
  },
  timeScale: {
    borderColor: '#fff',
    timeVisible: true, // Enable time handling (not only date)
    fixLeftEdge: true, // Fix x axis labels visibility issue
    fixRightEdge: true, // Fix x axis labels visibility issue
    lockVisibleTimeRangeOnResize: true,
    allowBoldLabels: false,
  },
};

export const exchangeChartBoxSeriesConfig: AreaSeriesPartialOptions = {
  lineColor: '#2DD4B7',
  topColor: 'rgba(45, 212, 183, 0.3)',
  bottomColor: 'rgba(45, 212, 183, 0)',
  priceLineVisible: false,
};
