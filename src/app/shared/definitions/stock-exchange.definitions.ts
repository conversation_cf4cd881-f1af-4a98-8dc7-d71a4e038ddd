export interface FinancialAPIStockResult<T> {
  data: T;
  error: any;
}

export type FinancialAPIStockLatestResult = FinancialAPIStockResult<FinancialAPIStockLatestData[]>;

export interface FinancialAPIStockLatestData {
  date: string; // ISO-8601
  name: string;
  displayName: string;
  fullName: string;
  currentValue: number | null;
  changePercentage: number;
  changeDirection: 'Up' | 'Down' | 'None';
  type: 'Share' | 'Index';
}

export type FinancialAPIStockListResult = FinancialAPIStockResult<FinancialAPIStockListData[]>;

export interface FinancialAPIStockListData {
  date: string; // ISO-8601
  name: string;
  fullName: string;
  isinCode: string;
  lastPrice: number;
  firstPrice: number | null;
  lowPrice: number | null;
  highPrice: number | null;
  closingPrice: number | null;
  currency: string;
  changeDiffLastPrice: number | null;
  changePercentage: number | null;
  accumulatedTurnover: number | null;
  accumulatedVolume: number | null;
  bidVolumesAtLevel: number[];
  bidPricesAtLevel: number[];
  askVolumesAtLevel: number[];
  askPricesAtLevel: number[];
  bidVolumeAtLevel: number | null;
  bidPriceAtLevel: number | null;
  askVolumeAtLevel: number | null;
  askPriceAtLevel: number | null;
}

export type FinancialAPIStockDetailResult = FinancialAPIStockResult<FinancialAPIStockDetailData>;

export interface FinancialAPIStockDetailData {
  date: string; // ISO-8601
  name: string;
  lastPrice: number | null;
  changeDiffLastPrice: number | null;
  changePercentage: number | null;
  accumulatedTurnover: number | null;
  changeDirection: 'Up' | 'Down' | 'None';
}

export type FinancialAPIStockStatisticsResult = FinancialAPIStockResult<FinancialAPIStockStatisticsData>;

export interface FinancialAPIStockStatisticsData {
  name: string;
  isinCode: string;
  bidVolumesAtLevel: number[];
  bidPricesAtLevel: number[];
  askVolumesAtLevel: number[];
  askPricesAtLevel: number[];
  firstPrice: number | null;
  closingPrice: number | null;
  averagePrice: number | null;
  average180DaysPrice: number;
  average360DaysPrice: number;
  lowPrice: number | null;
  highPrice: number | null;
  lowPriceYear: number;
  lowPriceYearDate: string; // ISO-8601
  highPriceYear: number;
  highPriceYearDate: string; // ISO-8601
  lowPriceLife: number;
  lowPriceLifeDate: string; // ISO-8601
  highPriceLife: number;
  highPriceLifeDate: string; // ISO-8601
  numberOfTrades: number | null;
  accumulatedVolume: number | null;
  accumulatedTurnover: number | null;
  currency: string;
  bidVolumeAtLevel: number | null;
  bidPriceAtLevel: number | null;
  askVolumeAtLevel: number | null;
  askPriceAtLevel: number | null;
}

export type FinancialAPIStockInfoResult = FinancialAPIStockResult<FinancialAPIStockInfoData>;

export interface FinancialAPIStockInfoData {
  name: string;
  isinCode: string;
  isActiveProduct: boolean;
  isActiveIsinCode: boolean;
  ticker: string;
  dateOfIntroduction: string; // ISO-8601
  currency: string;
  nominalValue: number;
  introducedQuantity: number;
  capitalization: number;
  securityId: number;
}

export type FinancialAPIStockGraphResult = FinancialAPIStockResult<FinancialAPIStockGraphData>;

export interface FinancialAPIStockGraphData {
  closingPrice: number;
  date: string; // ISO-8601
  isinCode: string;
  name: string;
  points: FinancialAPIStockGraphPoint[];
}

export interface FinancialAPIStockGraphPoint {
  date: string; // ISO-8601
  time: string;
  lastPrice: number;
  accumulatedVolume: number;
  sumOfAccumulatedVolume: number;
  numberOfTrades: number;
  average180DaysPrice: number;
  average360DaysPrice: number;
  numberOfSharesInTrade: number;
}

export type FinancialAPIStockIndexListResult = FinancialAPIStockResult<FinancialAPIStockIndexListData[]>;

export interface FinancialAPIStockIndexListData {
  sid: string;
  displayName: string;
  fullName: string;
}

export type FinancialAPIStockIndexGraphResult = FinancialAPIStockResult<FinancialAPIStockIndexGraphData>;

export interface FinancialAPIStockIndexGraphData {
  name: string;
  time: string;
  date: string; // ISO-8601
  closingPrice: number;
  currentValue: number;
  diffDayNom: number;
  diffDayPer: number;
  buxSumCapitalization: number;
  points: FinancialAPIStockIndexGraphPoint[];
}

export interface FinancialAPIStockIndexGraphPoint {
  date: string; // ISO-8601
  time: string;
  currentValue: number;
  isOfficialClosingPrice: boolean;
}

export type FinancialAPIStockIndexTopResult = FinancialAPIStockResult<FinancialAPIStockIndexTopData[]>;

export interface FinancialAPIStockIndexTopData {
  name: string;
  lastPrice: number;
  changeDiffLastPrice: number;
  changePercentage: number;
  accumulatedTurnover: number;
}

export enum StockChartType {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  YEAR = 'year',
  THREE_YEARS = 'threeyears',
  ALL = 'all',
}

export enum StockIndexChartType {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
  YEAR = 'year',
  THREE_YEARS = 'threeyears',
  ALL = 'all',
}
