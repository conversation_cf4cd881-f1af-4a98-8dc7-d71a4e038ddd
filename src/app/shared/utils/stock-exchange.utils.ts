import { FinancialAPIStockGraphPoint, FinancialAPIStockIndexGraphPoint, StockChartType, StockIndexChartType } from '../definitions';
import { AreaData, AreaSeriesPartialOptions, ChartOptions, ColorType, DeepPartial } from 'lightweight-charts';
import uniqBy from 'lodash-es/uniqBy';
import { format, isAfter, isToday, isWeekend, set } from 'date-fns';
import { max, min } from 'lodash-es';
import { convertFinancialAPIDateToUTCTimestamp } from './financial.utils';

export function financialAPIStockChartDataMapper(chartData: FinancialAPIStockGraphPoint[], graphType?: StockChartType, stock?: string): AreaData[] {
  return uniqBy<FinancialAPIStockGraphPoint>(chartData, 'date')
    .filter((data: FinancialAPIStockGraphPoint) => data.lastPrice !== null)
    .map((data: FinancialAPIStockGraphPoint) => ({
      time: convertFinancialAPIDateToUTCTimestamp(data.date),
      value: data.lastPrice,
      customValues: {
        ...data,
        ...{ stock },
        ...{ graphType },
      },
    }));
}

export const stockChartDefaultConfig: DeepPartial<ChartOptions> = {
  height: 600,
  autoSize: true, // Enable responsive auto chart resize
  layout: {
    textColor: '#262626',
    background: {
      type: ColorType.Solid,
      color: 'white',
    },
    attributionLogo: false,
  },
  grid: {
    vertLines: {
      visible: false, // Disable vertical lines
    },
    horzLines: {
      color: '#d1d1d1',
    },
  },
  // Disable move and zoom
  handleScale: false,
  // Disable move and zoom
  handleScroll: false,
  crosshair: {
    horzLine: {
      color: '#0d9482',
      labelBackgroundColor: '#0d9482',
    },
    vertLine: {
      color: '#0d9482',
      labelBackgroundColor: '#0d9482',
    },
  },
  leftPriceScale: {
    visible: true,
    borderVisible: false,
    entireTextOnly: true, // Fix y axis labels visibility issue
    scaleMargins: {
      top: 0.1,
      bottom: 0.1,
    },
  },
  rightPriceScale: {
    visible: false,
  },
  timeScale: {
    borderColor: '#d1d1d1',
    timeVisible: true, // Enable time handling (not only date)
    fixLeftEdge: true, // Fix x axis labels visibility issue
    fixRightEdge: true, // Fix x axis labels visibility issue
    lockVisibleTimeRangeOnResize: true,
    allowBoldLabels: false,
    ticksVisible: true,
  },
};

export const stockChartSeriesConfig: AreaSeriesPartialOptions = {
  lineColor: '#2dd4b7',
  topColor: 'rgba(45, 212, 183, 0.3)',
  bottomColor: 'rgba(45, 212, 183, 0)',
  crosshairMarkerRadius: 5,
  crosshairMarkerBorderColor: '#2dd4b7',
  crosshairMarkerBackgroundColor: '#ffffff',
  crosshairMarkerBorderWidth: 3,
  priceFormat: {
    precision: 4,
  },
};

export function financialAPIStockIndexChartDataMapper(
  chartData: FinancialAPIStockIndexGraphPoint[],
  graphType?: StockIndexChartType,
  stockIndex?: string | null
): AreaData[] {
  return uniqBy<FinancialAPIStockIndexGraphPoint>(chartData, 'date')
    .filter((data: FinancialAPIStockIndexGraphPoint) => data.currentValue !== null)
    .map((data: FinancialAPIStockIndexGraphPoint) => ({
      time: convertFinancialAPIDateToUTCTimestamp(data.date),
      value: data.currentValue,
      customValues: {
        ...data,
        ...{ stockIndex },
        ...{ graphType },
      },
    }));
}

export const stockIndexChartDefaultConfig: DeepPartial<ChartOptions> = {
  height: 600,
  autoSize: true, // Enable responsive auto chart resize
  layout: {
    textColor: '#262626',
    background: {
      type: ColorType.Solid,
      color: 'white',
    },
    attributionLogo: false,
  },
  grid: {
    vertLines: {
      visible: false, // Disable vertical lines
    },
    horzLines: {
      color: '#d1d1d1',
    },
  },
  // Disable move and zoom
  handleScale: false,
  // Disable move and zoom
  handleScroll: false,
  crosshair: {
    horzLine: {
      color: '#0d9482',
      labelBackgroundColor: '#0d9482',
    },
    vertLine: {
      color: '#0d9482',
      labelBackgroundColor: '#0d9482',
    },
  },
  leftPriceScale: {
    visible: true,
    borderVisible: false,
    entireTextOnly: true, // Fix y axis labels visibility issue
    scaleMargins: {
      top: 0.1,
      bottom: 0.1,
    },
  },
  rightPriceScale: {
    visible: false,
  },
  timeScale: {
    borderColor: '#d1d1d1',
    timeVisible: true, // Enable time handling (not only date)
    fixLeftEdge: true, // Fix x axis labels visibility issue
    fixRightEdge: true, // Fix x axis labels visibility issue
    lockVisibleTimeRangeOnResize: true,
    allowBoldLabels: false,
    ticksVisible: true,
  },
};

export const stockIndexChartSeriesConfig: AreaSeriesPartialOptions = {
  lineColor: '#2dd4b7',
  topColor: 'rgba(45, 212, 183, 0.3)',
  bottomColor: 'rgba(45, 212, 183, 0)',
  crosshairMarkerRadius: 5,
  crosshairMarkerBorderColor: '#2dd4b7',
  crosshairMarkerBackgroundColor: '#ffffff',
  crosshairMarkerBorderWidth: 3,
  priceFormat: {
    precision: 4,
  },
};

export function formatStockDate(isoDate?: string | null): string {
  if (!isoDate) {
    return '';
  }

  const date: Date = new Date(isoDate);

  if (isToday(date)) {
    return format(date, 'HH:mm:ss');
  } else {
    return format(date, 'yyyy.MM.dd.');
  }
}

export function getMinMaxPrice(points: number[], closingPrice: number): { min: number; max: number } {
  const minPrice: number = min(points) ?? 0;
  const maxPrice: number = max(points) ?? 0;

  return {
    min: minPrice > closingPrice ? closingPrice : minPrice,
    max: maxPrice < closingPrice ? closingPrice : maxPrice,
  };
}

export function isStockDailyDataAvailable(): boolean {
  const budapestDate: Date = new Date(new Date().toLocaleString('en-US', { timeZone: 'Europe/Budapest' }));
  return (
    !isWeekend(budapestDate) &&
    isAfter(
      budapestDate,
      set(budapestDate, {
        hours: 9,
        minutes: 15,
        seconds: 0,
        milliseconds: 0,
      })
    )
  );
}

export function getStockExchangeLink(type: 'Share' | 'Index' | 'Currency', id: string): string | string[] {
  if (type === 'Index') {
    return ['/', 'reszvenyek', 'indexek', id.toLowerCase()];
  }

  return ['/', 'reszvenyek', id.toLowerCase()];
}
