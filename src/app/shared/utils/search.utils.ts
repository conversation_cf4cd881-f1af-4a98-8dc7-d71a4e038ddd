import { ArticleSearchResult, BackendArticleSearchResult } from '@trendency/kesma-ui';

export function backendArticlesSearchResultsToArticleSearchResArticles(article: BackendArticleSearchResult): ArticleSearchResult {
  const [year, month] = article.publishDate.split('-');
  return {
    ...article,
    length: parseInt(article.length, 10),
    year,
    month,
    columnTitleColor: '',
  } as ArticleSearchResult;
}

export function calculateFilters(filters: Record<string, string>): Record<string, string> {
  // if from_date doesn't exist then to_date should be deleted
  if (!filters['from_date'] && filters['to_date']) {
    filters = Object.fromEntries(Object.entries(filters).filter(([key]) => key !== 'to_date'));
  }

  return filters;
}

export function calculatePageTypeFilters(filters: Record<string, string>, pageParams: Record<string, string>): Record<string, string> {
  if (pageParams['authorSlug']) {
    filters = { ...filters, author: pageParams['authorSlug'] };
  }

  if (pageParams['categorySlug']) {
    filters = { ...filters, 'columnSlugs[]': pageParams['categorySlug'] };
  }

  return filters;
}
