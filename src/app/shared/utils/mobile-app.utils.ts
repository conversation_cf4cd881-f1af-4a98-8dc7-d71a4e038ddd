// http://detectmobilebrowsers.com/

const mobileAppRegex = /mobile\/(?<platform>.+)\/(?<device>.+)\/(?<os_version>.+)\/(?<app_version>.+)\/(?<device_language>.+)\/(?<userid>.+)/gi;

function isFacebookOrMessengerWebview(userAgent: string): boolean {
  const facebookWebviewRegex = /FBAN|FBAV|FBBV|FB_IAB|FB_MESSENGER|FB4A/i;

  return facebookWebviewRegex.test(userAgent);
}

export function isMobileApp(userAgent: string): boolean {
  // Ha kellenek az adatok:
  // const [agent, platform, device, os_version, app_version, device_language, user_id] = mobileRegex.exec(userAgent) ?? []
  // const appData = { agent, platform, device, os_version, app_version, device_language, user_id }
  // Test agent: mobile/android/samsung_galaxy_s21/11.0/4.2.1/en-US/12345

  return isFacebookOrMessengerWebview(userAgent) ? false : !!userAgent.match(mobileAppRegex);
}
