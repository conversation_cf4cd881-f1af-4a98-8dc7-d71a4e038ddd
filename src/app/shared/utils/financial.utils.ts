import { UTCTimestamp } from 'lightweight-charts';

export function convertFinancialAPIDateToUTCTimestamp(apiDate: string): UTCTimestamp {
  // Need to manipulate date to give an UTC value for chart library, later we subtract added minutes before display date-times
  const date: Date = new Date(apiDate);
  return (new Date(date.getTime() - new Date().getTimezoneOffset() * 60 * 1000).getTime() / 1000) as UTCTimestamp;
}

export function convertUTCTimestampToDate(utcTimestamp: UTCTimestamp): Date {
  // Convert back manipulated date to normal date
  const date: Date = new Date(utcTimestamp * 1000);
  return new Date(date.getTime() + new Date().getTimezoneOffset() * 60 * 1000);
}
