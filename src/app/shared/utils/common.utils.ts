import { ArticleCardBadgeList } from '../definitions';
import { ArticleCard, buildArticleUrl } from '@trendency/kesma-ui';

export const getArticleBadges = (article: ArticleCard | undefined): { items: ArticleCardBadgeList; hasBadges: boolean } => {
  const items: ArticleCardBadgeList = {
    ...(article?.isAdultsOnly ? { isAdultsOnly: !!+article.isAdultsOnly } : {}),
    ...(article?.isVideoType ? { isVideoType: !!+article.isVideoType } : {}),
    ...(article?.isPodcastType ? { isPodcastType: !!+article.isPodcastType } : {}),
    ...(article?.hasGallery ? { hasGallery: !!+article.hasGallery } : {}),
  };

  return {
    hasBadges: Object.entries(items).some(([_key, value]) => value),
    items,
  };
};

export const buildUrlByContentType = (articleCard: ArticleCard | undefined): string[] => {
  switch (articleCard?.contentType) {
    case 'video':
      return ['/', 'video', articleCard.slug as string];
    case 'podcast':
      return ['/', 'podcastok', articleCard.slug as string];
    default:
      return buildArticleUrl(articleCard as ArticleCard);
  }
};
