import {
  Article,
  ArticleBody,
  ArticleBodyType,
  ArticleCard,
  BackendArticle,
  backendDateToDate,
  BackendRecommendedArticle,
  backendVotingDataToVotingData,
  ExternalRecommendation,
  getPrimaryColumnColorComboByColumnTitleColor,
  PersonalizedRecommendationArticle,
} from '@trendency/kesma-ui';
import { TitleCasePipe } from '@angular/common';
import { VgBrandingBoxArticle } from '../definitions';

export const backendArticlesToArticles = (article: BackendArticle): Article => {
  const { lastUpdated, publishDate: pubDate, dossier } = article;
  let publishDate: Date | undefined;
  let year: number = 0;
  let month: number = 0;
  if (pubDate) {
    publishDate = backendDateToDate(typeof pubDate === 'string' ? pubDate : pubDate.date) ?? undefined;
    year = publishDate?.getUTCFullYear() ?? 0;
    month = publishDate?.getUTCMonth() ?? 0;
  }

  const last: Date | undefined = lastUpdated ? (backendDateToDate(lastUpdated) ?? undefined) : undefined;
  const body: ArticleBody[] = article.body.map((element: ArticleBody) => {
    if (element.type === ArticleBodyType.Voting) {
      const votingValue = backendVotingDataToVotingData(element.details[0]?.value);
      const detail = { ...element.details[0], value: votingValue };
      return { ...element, details: [detail] };
    }
    return element;
  });
  return {
    ...article,
    publicAuthor: article?.publicAuthor ?? 'she',
    dossier: dossier?.[0],
    lastUpdated: last,
    publishDate,
    year,
    month,
    preTitle: article?.preTitle,
    tag: article?.tags?.[0],
    columnSlug: article.primaryColumn?.slug,
    columnTitle: article?.primaryColumn?.title,
    primaryColumnColorCombo: article?.primaryColumn?.titleColor ? getPrimaryColumnColorComboByColumnTitleColor(article?.primaryColumn?.titleColor) : undefined,
    body,
    isPodcastType: article?.podcastType,
    isVideoType: article?.videoType,
  };
};

export const externalRecommendationToArticleCard = (externalRecommendation: ExternalRecommendation): ArticleCard => ({
  id: externalRecommendation.spr,
  title: externalRecommendation.title,
  columnTitle: externalRecommendation.siteName,
  category: {
    name: new TitleCasePipe().transform(externalRecommendation.siteName),
    slug: undefined,
  },
  thumbnail: externalRecommendation.imagePath
    ? {
        url: externalRecommendation.imagePath,
      }
    : undefined,
  slug: undefined,
  publishDate: undefined,
  label: {
    text: externalRecommendation.siteName,
    url: externalRecommendation.siteName ?? '',
  },
  publishYear: undefined,
  publishMonth: undefined,
  url: externalRecommendation.url,
  columnEmphasizeOnArticleCard: true,
});

export const backendRecommendedArticleToArticleCard = ({
  id,
  slug,
  title,
  publishDate,
  thumbnailUrl,
  thumbnail,
  excerpt,
  readingLength,
  columnTitleColor,
  titleColor,
  columnSlug,
  preTitle,
  preTitleColor,
  regions,
  tags,
}: BackendRecommendedArticle): ArticleCard => {
  const [publishYear, publishMonth] = publishDate.split('-');
  return {
    id,
    title,
    slug,
    regions,
    tags,
    publishDate: backendDateToDate(publishDate) as Date,
    publishMonth,
    publishYear,
    thumbnail:
      thumbnailUrl || thumbnail
        ? {
            url: thumbnailUrl || thumbnail || '',
          }
        : undefined,
    category: {
      name: 'VG.hu',
      slug: columnSlug,
    },
    readingTime: readingLength?.toString(),
    columnTitle: 'VG.hu',
    preTitle: preTitle,
    primaryColumnColorCombo:
      columnTitleColor || columnTitleColor ? getPrimaryColumnColorComboByColumnTitleColor(columnTitleColor ? columnTitleColor : (titleColor ?? '')) : undefined,
    columnSlug,
    preTitleColor,
    lead: excerpt,
    label: {
      text: 'VG.hu',
    },
    columnEmphasizeOnArticleCard: true,
  };
};

export const mapTrafficDeflectorArticlesToBrandingBoxArticle = (
  personalizedRecommendationArticle: PersonalizedRecommendationArticle
): VgBrandingBoxArticle => ({
  title: personalizedRecommendationArticle?.title,
  description: personalizedRecommendationArticle?.head,
  imageUrl: personalizedRecommendationArticle?.image,
  link: personalizedRecommendationArticle?.url,
});
