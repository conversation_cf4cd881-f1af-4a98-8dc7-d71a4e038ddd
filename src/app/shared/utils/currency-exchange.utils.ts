import {
  CurrencyChartType,
  CurrencyOption,
  FinancialAPICurrencyCrossRatesData,
  FinancialAPICurrencyCrossRatesFriendlyResult,
  FinancialAPICurrencyDateAggregatedData,
  FinancialAPICurrencyDateData,
} from '../definitions';
import { AreaData, AreaSeriesPartialOptions, ChartOptions, ColorType, DeepPartial } from 'lightweight-charts';
import uniqBy from 'lodash-es/uniqBy';
import { convertFinancialAPIDateToUTCTimestamp } from './financial.utils';

export const currencyOptions: CurrencyOption[] = [
  { currency: 'EUR', title: 'Euró' },
  { currency: 'USD', title: 'USA dollár' },
  { currency: 'CHF', title: '<PERSON>v<PERSON><PERSON>ci frank' },
  { currency: 'AUD', title: 'Ausztrál dollár' },
  { currency: 'BGN', title: 'Bolgár leva' },
  { currency: 'BRL', title: 'Brazil reál' },
  { currency: 'CAD', title: 'Kanadai dollár' },
  { currency: 'CNY', title: '<PERSON><PERSON><PERSON> jüan' },
  { currency: 'CZK', title: 'Cseh korona' },
  { currency: 'DKK', title: 'Dán korona' },
  { currency: 'GBP', title: 'Angol font' },
  { currency: 'HKD', title: 'Hongkongi dollár' },
  { currency: 'IDR', title: 'Indonéz rúpia' },
  { currency: 'ILS', title: 'Izraeli sékel' },
  { currency: 'INR', title: 'Indiai rúpia' },
  { currency: 'ISK', title: 'Izlandi korona' },
  { currency: 'JPY', title: 'Japán jen' },
  { currency: 'KRW', title: 'Dél-koreai won' },
  { currency: 'MXN', title: 'Mexikói peso' },
  { currency: 'MYR', title: 'Maláj ringgit' },
  { currency: 'NOK', title: 'Norvég korona' },
  { currency: 'NZD', title: 'Új-zélandi dollár' },
  { currency: 'PHP', title: 'Fülöp-szigeteki peso' },
  { currency: 'PLN', title: 'Lengyel zloty' },
  { currency: 'RON', title: 'Román lej' },
  { currency: 'RSD', title: 'Szerb dínár' },
  { currency: 'RUB', title: 'Orosz rubel' },
  { currency: 'SEK', title: 'Svéd korona' },
  { currency: 'SGD', title: 'Szingapúri dollár' },
  { currency: 'THB', title: 'Thai bát' },
  { currency: 'TRY', title: 'Török líra' },
  { currency: 'UAH', title: 'Ukrán hrivnya' },
  { currency: 'ZAR', title: 'Dél-afrikai rand' },
];

export function financialAPICurrencyCrossRatesMapper(apiData: FinancialAPICurrencyCrossRatesData): FinancialAPICurrencyCrossRatesFriendlyResult {
  const currencyColumn: string[] = apiData.data.data[0]?.slice(1) as string[];

  return apiData.data.data
    .slice(1)
    .map((currencyRow: (string | null)[]) => [
      currencyRow[0] as string,
      currencyColumn
        .map((currency: string, index: number) => [currency, +(currencyRow as string[])[index + 1].replace(',', '.')])
        .reduce(
          (acc, [currency, value]) => ({
            ...acc,
            [currency as string]: value as number,
          }),
          {}
        ),
    ])
    .reduce(
      (acc, [currency, value]) => ({
        ...acc,
        [currency as string]: value as { [currency: string]: number },
      }),
      {}
    );
}

export function financialAPICurrencyChartDataMapper(
  chartData: FinancialAPICurrencyDateData[] | FinancialAPICurrencyDateAggregatedData[],
  graphType: CurrencyChartType
): AreaData[] {
  return uniqBy<FinancialAPICurrencyDateData | FinancialAPICurrencyDateAggregatedData>(chartData, 'date').map(
    (data: FinancialAPICurrencyDateData | FinancialAPICurrencyDateAggregatedData) => ({
      time: convertFinancialAPIDateToUTCTimestamp(data.date),
      value: (data as FinancialAPICurrencyDateData).rate ?? (data as FinancialAPICurrencyDateAggregatedData).close,
      customValues:
        graphType !== CurrencyChartType.DAY && graphType !== CurrencyChartType.WEEK
          ? {
              ...data,
            }
          : undefined,
    })
  );
}

export const currencyChartDefaultConfig: DeepPartial<ChartOptions> = {
  height: 600,
  autoSize: true, // Enable responsive auto chart resize
  layout: {
    textColor: '#262626',
    background: {
      type: ColorType.Solid,
      color: 'white',
    },
    attributionLogo: false,
  },
  grid: {
    vertLines: {
      visible: false, // Disable vertical lines
    },
    horzLines: {
      color: '#d1d1d1',
    },
  },
  // Disable move and zoom
  handleScale: false,
  // Disable move and zoom
  handleScroll: false,
  crosshair: {
    horzLine: {
      color: '#0d9482',
      labelBackgroundColor: '#0d9482',
    },
    vertLine: {
      color: '#0d9482',
      labelBackgroundColor: '#0d9482',
    },
  },
  leftPriceScale: {
    visible: true,
    borderVisible: false,
    entireTextOnly: true, // Fix y axis labels visibility issue
    scaleMargins: {
      top: 0.1,
      bottom: 0.1,
    },
  },
  rightPriceScale: {
    visible: false,
  },
  timeScale: {
    borderColor: '#d1d1d1',
    timeVisible: true, // Enable time handling (not only date)
    fixLeftEdge: true, // Fix x axis labels visibility issue
    fixRightEdge: true, // Fix x axis labels visibility issue
    lockVisibleTimeRangeOnResize: true,
    allowBoldLabels: false,
    ticksVisible: true,
  },
};

export const currencyChartSeriesConfig: AreaSeriesPartialOptions = {
  lineColor: '#2dd4b7',
  topColor: 'rgba(45, 212, 183, 0.3)',
  bottomColor: 'rgba(45, 212, 183, 0)',
  crosshairMarkerRadius: 5,
  crosshairMarkerBorderColor: '#2dd4b7',
  crosshairMarkerBackgroundColor: '#ffffff',
  crosshairMarkerBorderWidth: 3,
  priceFormat: {
    precision: 4,
    minMove: 0.0001,
  },
};
