import {
  ApiResponseMetaList,
  ApiResult,
  backendDateToDate,
  BackendGalleryDetails,
  FakeBool,
  GalleryData,
  GalleryDetails,
  GalleryRecommendationData,
  mapBackendGalleryImageToGalleryImage,
  toBool,
} from '@trendency/kesma-ui';

export const mapBackendGalleryDetailsResultToGalleryDetails = ({
  data: [
    {
      id,
      title,
      pre_title,
      preTitle,
      slug,
      tags,
      description,
      photographer,
      publishDate,
      highlightedImageUrl,
      highlightedImageUrlCreatedAt,
      images,
      robotsTag,
    },
  ],
}: ApiResult<BackendGalleryDetails[], ApiResponseMetaList>): GalleryDetails => ({
  id,
  title,
  pre_title,
  preTitle,
  slug,
  tags,
  description,
  photographer,
  publishDate: backendDateToDate(publishDate) as Date,
  highlightedImage: {
    url: highlightedImageUrl,
    creationDate: backendDateToDate(highlightedImageUrlCreatedAt) as Date,
  },
  images: images.map(mapBackendGalleryImageToGalleryImage),
  robotsTag,
});

export const mapGalleryRecommendationsToGalleryData = ({
  id,
  count,
  description,
  highlightedImageUrl,
  photographer,
  publicDate,
  slug,
  title,
  isAdult,
}: GalleryRecommendationData & { isAdult?: FakeBool }): GalleryData => {
  return {
    id,
    description,
    count,
    highlightedImageUrl: highlightedImageUrl || '',
    images: [],
    title,
    slug,
    photographer: photographer || '',
    publishDate: publicDate || new Date(),
    isAdult: toBool(isAdult),
  };
};
