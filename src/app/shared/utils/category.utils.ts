import { ApiResponseMetaList, ApiResult, ArticleCard, LayoutWithExcludeIds } from '@trendency/kesma-ui';
import { CategoryResolverResponse } from 'src/app/feature/category-page/api/category.definitions';

export const mapCategoryResponse = (
  categoryResponse: ApiResult<ArticleCard[], ApiResponseMetaList>,
  slug: string,
  year = '',
  month = '',
  layoutResponse: LayoutWithExcludeIds = { data: null, excludedIds: [], columnTitle: '' },
  noLayoutData = false
): CategoryResolverResponse => ({
  layoutApiResponse: (noLayoutData ? null : layoutResponse?.data) as any,
  columnTitle: layoutResponse.columnTitle || categoryResponse?.meta?.['column']?.['title'],
  columnParentTitle: String(categoryResponse?.meta?.['columnParentTitle']),
  excludedIds: layoutResponse?.excludedIds,
  category: categoryResponse,
  slug,
  year,
  month,
  sponsorship: (layoutResponse as CategoryResolverResponse)?.sponsorship,
});
