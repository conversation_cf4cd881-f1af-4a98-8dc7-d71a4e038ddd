import { Routes } from '@angular/router';
import { NewsletterConfirmPageComponent } from './feature/newsletter/components/newsletter-confirm-page/newsletter-confirm-page.component';
import { NewsletterSuccessPageComponent } from './feature/newsletter/components/newsletter-succes-page/newsletter-success-page.component';
import { BaseComponent, Error404Component, InitResolver, NewsletterType, RecommendationsResolver } from './shared';
import { CheckRedirectBefore404Guard } from '@trendency/kesma-ui';

export const routes: Routes = [
  {
    path: 'layout-editor',
    loadChildren: () => import('./feature/layout-editor/layout-editor.routing').then((m) => m.LAYOUT_EDITOR_ROUTES),
  },
  {
    path: '',
    component: BaseComponent,
    resolve: { data: InitResolver },
    providers: [InitResolver],
    children: [
      {
        path: '',
        pathMatch: 'full',
        loadChildren: () => import('./feature/home/<USER>').then((m) => m.HOME_ROUTES),
      },
      // "short-circuit" - ha a file nem létezik, az SSR lefut - ami file lehet, azt küldjük 404-re
      {
        path: 'assets/:file',
        redirectTo: '404',
      },
      {
        path: 'assets/:dir/:file',
        redirectTo: '404',
      },
      {
        path: 'script/:file',
        redirectTo: '404',
      },
      {
        path: 'cikk-elonezet/:previewHash',
        loadChildren: () => import('./feature/slug-route-handler/slug-route-handler.routing').then((m) => m.STATIC_PAGE_ROUTES),
        data: {
          omitGlobalPageView: true,
          isFullWidth: true,
        },
      },
      {
        path: 'cikk-elonezet/:previewHash/:previewType',
        loadChildren: () => import('./feature/slug-route-handler/slug-route-handler.routing').then((m) => m.STATIC_PAGE_ROUTES),
        data: {
          omitGlobalPageView: true,
          isFullWidth: true,
        },
      },
      {
        path: 'elrendezes-elonezet/:layoutHash',
        loadChildren: () => import('./feature/layout-preview/layout-preview.routing').then((m) => m.LAYOUT_PREVIEW_ROUTES),
      },
      {
        path: 'kereses',
        loadChildren: () => import('./feature/search-page/search-page.routing').then((m) => m.SEARCH_PAGE_ROUTES),
      },
      {
        path: 'galeriak',
        loadChildren: () => import('./feature/galleries/galleries.routing').then((m) => m.GALLERIES_ROUTES),
        data: {
          isFullWidth: true,
        },
      },
      {
        path: 'videok',
        loadChildren: () => import('./feature/videos/videos.routing').then((m) => m.VIDEOS_ROUTES),
        data: {
          isFullWidth: true,
        },
      },
      {
        path: 'video',
        loadChildren: () => import('./feature/videos/videos.routing').then((m) => m.VIDEOS_ROUTES),
        data: {
          isFullWidth: true,
          isOld: true,
        },
      },
      {
        path: 'podcast',
        loadChildren: () => import('./feature/podcast-articles/podcast-articles.routing').then((m) => m.PODCASTS_ROUTES),
        data: {
          isFullWidth: true,
        },
      },
      {
        path: 'podcastok',
        loadChildren: () => import('./feature/podcasts/podcasts.routing').then((m) => m.PODCASTS_ROUTES),
        data: {
          isFullWidth: true,
        },
      },
      {
        path: 'dosszie',
        loadChildren: () => import('./feature/dossiers/dossiers.routing').then((m) => m.DOSSIERS_ROUTES),
      },
      {
        path: 'szerzo',
        loadChildren: () => import('./feature/author/author.routing').then((m) => m.AUTHOR_ROUTES),
      },
      {
        path: 'szerzok',
        loadChildren: () => import('./feature/author/author.routing').then((m) => m.AUTHOR_ROUTES),
      },
      {
        path: 'cimke',
        loadChildren: () => import('./feature/tags-page/tags-page.routing').then((m) => m.TAGS_PAGE_ROUTES),
      },
      {
        path: 'hirfolyam/:dossierSlug',
        loadChildren: () => import('./feature/news-feed/news-feed.routing').then((m) => m.NEWS_FEED_ROUTING),
      },
      {
        path: 'szoszedet',
        loadChildren: () => import('./feature/glossary/glossary.routing').then((m) => m.GLOSSARY_ROUTES),
      },
      {
        path: 'hirlevel-feliratkozas',
        loadChildren: () => import('./feature/newsletter/newsletter.routing').then((m) => m.NEWS_LETTER_ROUTES),
        pathMatch: 'full',
        data: { type: NewsletterType.reggeli },
      },
      {
        path: 'hirlevel-feliratkozas-2',
        loadChildren: () => import('./feature/newsletter/newsletter.routing').then((m) => m.NEWS_LETTER_ROUTES),
        pathMatch: 'full',
        data: { type: NewsletterType.heti_befektetoi },
      },
      {
        path: 'hirlevel-feliratkozas-megerositese',
        component: NewsletterConfirmPageComponent,
        pathMatch: 'full',
      },
      {
        path: 'hirlevel-feliratkozas-megerosites-sikeres',
        component: NewsletterSuccessPageComponent,
        data: { pageTextSuffix: 'feliratkozás' },
        pathMatch: 'full',
        providers: [RecommendationsResolver],
        resolve: { recommendations: RecommendationsResolver },
      },
      {
        path: 'hirlevel-leiratkozas',
        component: NewsletterSuccessPageComponent,
        data: { pageTextSuffix: 'leiratkozás' },
        pathMatch: 'full',
        providers: [RecommendationsResolver],
        resolve: { recommendations: RecommendationsResolver },
      },
      {
        path: 'deviza-adatok',
        loadChildren: () => import('./feature/currency-exchange/currency-exchange.routing').then((m) => m.CURRENCY_EXCHANGE_ROUTING),
      },
      {
        path: 'reszvenyek',
        loadChildren: () => import('./feature/stock-exchange/stock-exchange.routing').then((m) => m.STOCK_EXCHANGE_ROUTING),
      },
      {
        path: 'friss-hirek',
        loadChildren: () => import('./feature/latest-news-page/latest-news-page.routing').then((m) => m.LATEST_NEW_PAGE_ROUTING),
      },
      {
        path: 'alapko-tartalom',
        loadChildren: () => import('./feature/foundation-content/foundation-content.routing').then((m) => m.FOUNDATION_CONTENT_ROUTING),
      },
      {
        path: 'rovat/:categorySlug',
        loadChildren: () => import('./feature/category-page/category.routing').then((m) => m.CATEGORY_ROUTES),
        data: {
          omitGlobalPageView: true,
        },
      },
      {
        path: '404',
        component: Error404Component,
        providers: [RecommendationsResolver],
        resolve: { data: RecommendationsResolver },
        canActivate: [CheckRedirectBefore404Guard],
      },
      {
        path: 'galeria',
        loadChildren: () => import('./feature/gallery-layer/gallery-layer.routing').then((m) => m.GALLERY_LAYER_ROUTES),
      },
      {
        path: 'velemeny',
        children: [
          {
            path: '',
            pathMatch: 'full',
            loadChildren: () => import('./feature/opinions/opinions.routing').then((m) => m.OPINIONS_ROUTES),
          },
        ],
      },
      {
        path: ':categorySlug/:year/:month/:articleSlug',
        loadChildren: () => import('./feature/slug-route-handler/slug-route-handler.routing').then((m) => m.STATIC_PAGE_ROUTES),
        data: {
          omitGlobalPageView: true,
          isFullWidth: true,
          isArticlePage: true,
          skipSeoMetaCheck: true,
        },
      },
      {
        path: ':categorySlug/:articleSlug',
        loadChildren: () => import('./feature/slug-route-handler/slug-route-handler.routing').then((m) => m.STATIC_PAGE_ROUTES),
        data: {
          omitGlobalPageView: true,
          isFullWidth: true,
          isArticlePage: true,
          skipSeoMetaCheck: true,
        },
      },
      {
        path: ':categorySlug/:categorySlugSecondary/:articleSlug',
        loadChildren: () => import('./feature/slug-route-handler/slug-route-handler.routing').then((m) => m.STATIC_PAGE_ROUTES),
        data: {
          omitGlobalPageView: true,
          isFullWidth: true,
          isArticlePage: true,
          skipSeoMetaCheck: true,
        },
      },
      {
        path: ':slug',
        loadChildren: () => import('./feature/slug-route-handler/slug-route-handler.routing').then((m) => m.STATIC_PAGE_ROUTES),
      },
      {
        path: '**',
        redirectTo: '404',
      },
    ],
  },
];
