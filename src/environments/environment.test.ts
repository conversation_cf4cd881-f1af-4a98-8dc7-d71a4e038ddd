import { VgEnvironment } from './environment.definitions';

// UAT teszt környezet
export const environment: VgEnvironment = {
  production: true,
  type: 'beta',
  apiUrl: 'http://vgfe.apptest.content.private/publicapi/hu',
  financialApiUrl: 'http://findata.apptest.content.private/restapi',
  iriszApiUrl: {
    clientApiUrl: 'https://irisz.origo.hu',
    serverApiUrl: 'http://irisz.apptest.origo.private',
  },
  ingatlanbazarApiUrl: {
    clientApiUrl: 'https://www.ingatlanbazar.hu/api',
    serverApiUrl: 'http://localhost:30005/ingatlanbazar-api',
  },
  zoeApiUrl: {
    clientApiUrl: 'https://zoe.mediaworks.hu',
    serverApiUrl: 'http://localhost:30005/zoe-api',
  },
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  ssrProxyConfig: [
    {
      path: '/ingatlanbazar-api',
      target: 'https://www.ingatlanbazar.hu/api',
    },
    {
      path: '/zoe-api',
      target: 'https://zoe.mediaworks.hu',
    },
  ],
  facebookAppId: '',
  siteUrl: 'http://vgfe.apptest.content.private',
  googleSiteKey: '6LeEZMUpAAAAALY1aDgrRgcmGEf1-GozC7RwnkYx',
  googleTagManager: 'GTM-TS5VMZ',
  gemiusId: 'd12aCArh.v5nYC5zXw1smpby.tMsbkbs2anSKLr9tJz.57',
  httpReqTimeout: 30, // second
  sentry: {
    dsn: '',
    tracingOrigins: [],
    sampleRate: 0.1,
  },
};
