import { VgEnvironment } from './environment.definitions';

// <PERSON><PERSON>
export const environment: VgEnvironment = {
  production: true,
  type: 'prod',
  apiUrl: {
    clientApiUrl: 'https://api.vg.hu/publicapi/hu',
    serverApiUrl: 'http://vgfeapi.app.content.private/publicapi/hu',
  },
  financialApiUrl: {
    clientApiUrl: 'https://fd.mediaworks.hu',
    serverApiUrl: 'http://findata-restapi.app.content.private',
  },
  iriszApiUrl: {
    clientApiUrl: 'https://irisz.origo.hu',
    serverApiUrl: 'http://irisz.app.origo.private',
  },
  ingatlanbazarApiUrl: {
    clientApiUrl: 'https://www.ingatlanbazar.hu/api',
    serverApiUrl: 'http://localhost:30005/ingatlanbazar-api',
  },
  zoeApiUrl: {
    clientApiUrl: 'https://zoe.mediaworks.hu',
    serverApiUrl: 'http://localhost:30005/zoe-api',
  },
  personalizedRecommendationApiUrl: 'https://terelo.mediaworks.hu/api',
  ssrProxyConfig: [
    {
      path: '/ingatlanbazar-api',
      target: 'https://www.ingatlanbazar.hu/api',
    },
    {
      path: '/zoe-api',
      target: 'https://zoe.mediaworks.hu',
    },
  ],
  facebookAppId: '1534911313439123',
  siteUrl: 'https://www.vg.hu',
  googleSiteKey: '6LeEZMUpAAAAALY1aDgrRgcmGEf1-GozC7RwnkYx',
  googleTagManager: 'GTM-TS5VMZ',
  gemiusId: 'd12aCArh.v5nYC5zXw1smpby.tMsbkbs2anSKLr9tJz.57',
  httpReqTimeout: 30, // second
  sentry: {
    dsn: '',
    tracingOrigins: [],
    sampleRate: 0.1,
  },
};
