{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"vilaggazdasag": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "inlineStyle": false, "inlineTemplate": false, "prefix": "app"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser-esbuild", "options": {"outputPath": "dist/browser", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "allowedCommonJsDependencies": ["date-fns-tz"], "stylePreprocessorOptions": {"includePaths": ["src/scss"]}, "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "src/assets/images/favicons", "output": "/"}, {"glob": "manifest.json", "input": "src/assets/json", "output": "/"}, {"glob": "robots.txt", "input": "src", "output": "/"}, {"glob": "ads.txt", "input": "src", "output": "/"}, {"glob": "**/*", "input": "node_modules/@trendency/kesma-ui/assets", "output": "/assets"}, {"glob": "**/*", "input": "node_modules/@trendency/vg-ui/assets", "output": "/assets"}], "styles": ["node_modules/swiper/swiper-bundle.css", "src/scss/styles.scss"], "scripts": ["src/assets/vendors/embedly/platform.js", "src/assets/vendors/instagram/instagram.js", "src/assets/vendors/newsletter-script.js"], "preserveSymlinks": true}, "configurations": {"production": {"optimization": {"fonts": true, "scripts": true, "styles": {"inlineCritical": false, "minify": true}}, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "10mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "outputHashing": "all"}, "dev-php81": {"optimization": {"fonts": true, "scripts": true, "styles": {"inlineCritical": false, "minify": true}}, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "10mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev-php81.ts"}], "outputHashing": "all"}, "staging": {"optimization": {"fonts": true, "scripts": true, "styles": {"inlineCritical": false, "minify": true}}, "budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "10mb"}, {"type": "anyComponentStyle", "maximumWarning": "200kb", "maximumError": "10mb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.beta.ts"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}]}, "local": {"buildOptimizer": false, "optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "local"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "vilaggazdasag:build:production"}, "staging": {"buildTarget": "vilaggazdasag:build:staging"}, "development": {"buildTarget": "vilaggazdasag:build:development"}, "local": {"buildTarget": "vilaggazdasag:build:local"}}, "defaultConfiguration": "local"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "vilaggazdasag:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/scss/styles.scss"], "scripts": []}}, "server": {"builder": "@angular-devkit/build-angular:server", "options": {"outputPath": "dist/server", "main": "server.ts", "tsConfig": "tsconfig.server.json", "inlineStyleLanguage": "scss", "stylePreprocessorOptions": {"includePaths": ["src/scss"]}}, "configurations": {"production": {"outputHashing": "media", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}]}, "dev-php81": {"outputHashing": "media", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev-php81.ts"}]}, "staging": {"outputHashing": "media", "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.beta.ts"}]}, "development": {"optimization": false, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "buildOptimizer": false}, "local": {"optimization": false, "buildOptimizer": false}}, "defaultConfiguration": "local"}, "serve-ssr": {"builder": "@angular-devkit/build-angular:ssr-dev-server", "configurations": {"development": {"browserTarget": "vilaggazdasag:build:development", "serverTarget": "vilaggazdasag:server:development"}, "production": {"browserTarget": "vilaggazdasag:build:production", "serverTarget": "vilaggazdasag:server:production"}, "staging": {"browserTarget": "vilaggazdasag:build:staging", "serverTarget": "vilaggazdasag:server:staging"}, "local": {"browserTarget": "vilaggazdasag:build", "serverTarget": "vilaggazdasag:server"}}, "defaultConfiguration": "local"}, "prerender": {"builder": "@angular-devkit/build-angular:prerender", "options": {"routes": ["/"]}, "configurations": {"production": {"browserTarget": "vilaggazdasag:build:production", "serverTarget": "vilaggazdasag:server:production"}, "development": {"browserTarget": "vilaggazdasag:build:development", "serverTarget": "vilaggazdasag:server:development"}, "staging": {"browserTarget": "vilaggazdasag:build:development", "serverTarget": "vilaggazdasag:server:development"}, "local": {"browserTarget": "vilaggazdasag:build", "serverTarget": "vilaggazdasag:server"}}, "defaultConfiguration": "local"}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"cache": {"enabled": false}, "analytics": "d350187e-d3c4-43d0-9b8b-d4f4eba16a19", "schematicCollections": ["@angular-eslint/schematics"]}, "schematics": {"@angular-eslint/schematics:application": {"setParserOptionsProject": true}, "@angular-eslint/schematics:library": {"setParserOptionsProject": true}}}