# KESMA / Világgazdaság

> This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 13.1.2.

## Setup

A projectnek vannak belső függőségei, melyek a `@trendency/*` scope-ban találhatóak és a Nexus repositoryból telepíthetőek.

A csomagok forrásai itt találhatóak

* [@trendency/kesma-core](https://gitlab.trendency.hu/frontend/trendency-npm-packages/ui-lib)
* [@trendency/ui-lib (kesma-ui)](https://gitlab.trendency.hu/frontend/trendency-npm-packages/ui-lib)

A publikált csomagok pedig itt:

* [Nexus @trendency/kesma-core](https://dev-nexus.trendency.hu/repository/npm-hosted/@trendency/kesma-core)
* [Nexus @trendency/kesma-ui](http://dev-nexus.trendency.hu/repository/npm-hosted/@trendency/kesma-ui)

### @trendency névtér és repo beállítása

Ha sem a lokális konfigurációd, sem a projekt nem tartalmazza a Nexus NPM repository elérését akkor hozzá kell adni és autentikálni kell:

```bash
npm login --registry=https://dev-nexus.trendency.hu/repository/npm-hosted/
```

Itt be fogja kérni az LDAP userneved, jelszavad és e-mail címed. Ha minden rendben van, akkor az `npm i` már gond nélkül le fog futni.

## Lokális fejlesztés a @trendency csomagokkal

Amennyben szükség van rá, mind a `core`, mind a `*-ui` library-k kapcsolhatók az oldalhoz úgy, hogy a libraryben történt módosítások azonnal megjelennek az oldalon. Ehhez "linkelni" kell a kapcsolódó library-k forrását.

1. telepíteni kell a library-k forrását
    ```bash
    # [opcionális] csak akkor kell, ha akarsz/szükséges a core-ban is módosítani (kevéssé valószínű)
    $ <NAME_EMAIL>:frontend/trendency-npm-packages/ui-lib.git
    ```
2. installálni kell a csomagokat és buildelni kell őket
    ```bash
    # 1. [opcionális] csak akkor kell, ha akarsz/szükséges a core-ban is módosítani (kevéssé valószínű)
    # 2. átmegyünk a ui-lib -be
    $ cd ui-lib
    $ npm i
    $ npm run build:all
    $ cd ..
    ```
3. linkelni a buildelt verziókat
    ```bash
    # [opcionális] csak akkor kell, ha akarsz/szükséges a core-ban is módosítani (kevéssé valószínű)
    $ cd core/dist/trendency/kesma-core
    $ npm link
    # átmegyünk a ui-lib -be
    $ cd ../../../../ui-lib/dist/trendency/kesma-ui
    $ npm link
    ```
4. linkelni a library-ket a projectbe
    ```bash
    $ cd vilaggazdasag
    # [opcionális] csak akkor kell, ha akarsz/szükséges a core-ban is módosítani (kevéssé valószínű)
    $ npm link @trendency/kesma-core @trendency/kesma-ui 
    # HA nem akarod linkelni a core-t:
    $ npm link @trendency/kesma-ui
    ```
6. el kell indítani a library-ket `watch` módban:

   ⚠ fontos! Külön terminálban indítsuk, vagy tmux, screen, stb. alkalmazással, mert előtérben fut és addig a terminál másra nem használható!
    ```bash
    $ cd core
    $ npm run watch:kesma
    ```
7. végül egy külön terminálban el kell indítani a projectet _amint a vg-ui watch build is lefutott!_
    ```bash
    $ cd vilaggazdasag
    $ npm run start
    ```

## ⚠ Egyéb fontos tudnivalók

Ha verziót akarsz kiadni, akkor build előtt **állítsd le a watch-okat!** különben amíg a publish-hez buildelsz
az érzékelt változások miatt újra és újra le fogja generálni a watch miatt a többi terminálban a projektet és
eltörheti a publish-hoz futó buildedet.

## Storybook

ld. [👉 @trendency/ui-lib#README.md](https://gitlab.trendency.hu/frontend/trendency-npm-packages/ui-lib/-/blob/master/README.md)

## Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The app will automatically reload if you change any of the source files.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory.

## Running unit tests

Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Running end-to-end tests

Run `ng e2e` to execute the end-to-end tests via a platform of your choice. To use this command, you need to first add a package that implements end-to-end testing capabilities.

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI Overview and Command Reference](https://angular.io/cli) page.
